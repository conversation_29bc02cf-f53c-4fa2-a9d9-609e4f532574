"use client"

import { useState } from "react"
import { Search, Plus, Eye, RefreshCw, Play, History, TrendingUp, GitBranch } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import Link from "next/link"

// Mock data for audit rules
const mockRules = [
  {
    id: "RULE_001",
    name: "高耗能客户识别规则",
    status: "已执行",
    lastModified: "2024-01-15",
    description: "识别月度用电量超过10000千瓦时的工商业客户",
    creator: "张三",
    executionCount: 15,
    lastExecution: "2024-01-15 14:30:00",
    hitCount: 120,
  },
  {
    id: "RULE_002",
    name: "异常用电模式检测",
    status: "待执行",
    lastModified: "2024-01-14",
    description: "检测用电模式异常变化的客户",
    creator: "李四",
    executionCount: 0,
    lastExecution: null,
    hitCount: 0,
  },
  {
    id: "RULE_003",
    name: "电费缴纳异常规则",
    status: "草稿",
    lastModified: "2024-01-13",
    description: "识别电费缴纳行为异常的客户",
    creator: "王五",
    executionCount: 0,
    lastExecution: null,
    hitCount: 0,
  },
  {
    id: "RULE_004",
    name: "功率因数不达标检测",
    status: "已执行",
    lastModified: "2024-01-12",
    description: "检测功率因数低于标准值的工业客户",
    creator: "赵六",
    executionCount: 8,
    lastExecution: "2024-01-12 09:15:00",
    hitCount: 45,
  },
  {
    id: "RULE_005",
    name: "峰谷用电比例异常",
    status: "待执行",
    lastModified: "2024-01-11",
    description: "检测峰谷用电比例异常的客户",
    creator: "孙七",
    executionCount: 0,
    lastExecution: null,
    hitCount: 0,
  },
  {
    id: "RULE_006",
    name: "电表数据完整性检查",
    status: "已执行",
    lastModified: "2024-01-10",
    description: "检查电表数据的完整性和一致性",
    creator: "周八",
    executionCount: 22,
    lastExecution: "2024-01-10 16:45:00",
    hitCount: 78,
  },
]

export default function RuleManagementPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("全部")
  const [selectedRule, setSelectedRule] = useState<(typeof mockRules)[0] | null>(null)
  const [currentPage, setCurrentPage] = useState(1)
  const itemsPerPage = 6

  // Filter data based on search and status
  const filteredData = mockRules.filter((rule) => {
    const matchesSearch =
      searchTerm === "" ||
      rule.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      rule.description.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesStatus = statusFilter === "全部" || rule.status === statusFilter

    return matchesSearch && matchesStatus
  })

  // Pagination
  const totalPages = Math.ceil(filteredData.length / itemsPerPage)
  const startIndex = (currentPage - 1) * itemsPerPage
  const paginatedData = filteredData.slice(startIndex, startIndex + itemsPerPage)

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "已执行":
        return <Badge className="bg-green-100 text-green-800 border-green-300">已执行</Badge>
      case "待执行":
        return <Badge className="bg-yellow-100 text-yellow-800 border-yellow-300">待执行</Badge>
      case "草稿":
        return <Badge className="bg-gray-100 text-gray-800 border-gray-300">草稿</Badge>
      default:
        return <Badge variant="secondary">{status}</Badge>
    }
  }

  const handleViewDetails = (rule: (typeof mockRules)[0]) => {
    setSelectedRule(rule)
  }

  return (
    <div className="flex-1 space-y-6 p-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div className="space-y-2">
          <h1 className="text-3xl font-bold tracking-tight text-gray-900">稽查规则管理</h1>
          <p className="text-gray-600">管理和执行系统中的稽查规则</p>
        </div>
        <Link href="/rule-generation">
          <Button className="flex items-center gap-2">
            <Plus className="h-4 w-4" />
            新建规则
          </Button>
        </Link>
      </div>

      {/* Controls Section */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="搜索规则名称..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-full sm:w-48">
                <SelectValue placeholder="状态" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="全部">全部</SelectItem>
                <SelectItem value="草稿">草稿</SelectItem>
                <SelectItem value="待执行">待执行</SelectItem>
                <SelectItem value="已执行">已执行</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Main Content - Rule List Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <History className="h-5 w-5" />
            规则列表
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>规则名称</TableHead>
                  <TableHead>状态</TableHead>
                  <TableHead>最后修改日期</TableHead>
                  <TableHead>操作</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {paginatedData.map((rule) => (
                  <TableRow key={rule.id}>
                    <TableCell>
                      <div className="space-y-1">
                        <div className="font-medium">{rule.name}</div>
                        <div className="text-sm text-gray-500 line-clamp-1">{rule.description}</div>
                      </div>
                    </TableCell>
                    <TableCell>{getStatusBadge(rule.status)}</TableCell>
                    <TableCell className="text-sm text-gray-600">{rule.lastModified}</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Button size="sm" variant="outline" onClick={() => handleViewDetails(rule)}>
                          <Eye className="h-3 w-3 mr-1" />
                          查看详情
                        </Button>
                        <Link href={`/rule-generation?ruleId=${rule.id}`}>
                          <Button size="sm">
                            <RefreshCw className="h-3 w-3 mr-1" />
                            解构与迭代
                          </Button>
                        </Link>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>

          {/* Pagination */}
          <div className="flex items-center justify-between space-x-2 py-4">
            <div className="text-sm text-gray-500">
              显示 {startIndex + 1} 到 {Math.min(startIndex + itemsPerPage, filteredData.length)} 条， 共{" "}
              {filteredData.length} 条记录
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
                disabled={currentPage === 1}
              >
                上一页
              </Button>
              <div className="flex items-center space-x-1">
                {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                  <Button
                    key={page}
                    variant={currentPage === page ? "default" : "outline"}
                    size="sm"
                    onClick={() => setCurrentPage(page)}
                    className="w-8 h-8"
                  >
                    {page}
                  </Button>
                ))}
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage((prev) => Math.min(prev + 1, totalPages))}
                disabled={currentPage === totalPages}
              >
                下一页
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Future Features Section */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-semibold text-gray-900">扩展功能</h2>
          <Button disabled className="flex items-center gap-2">
            <Play className="h-4 w-4" />
            执行规则 (未来功能)
          </Button>
        </div>

        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {/* Rule Execution History */}
          <Card className="border-2 border-dashed border-gray-300 bg-gray-50/50">
            <CardHeader className="text-center pb-4">
              <div className="mx-auto w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center mb-3">
                <History className="h-6 w-6 text-gray-400" />
              </div>
              <CardTitle className="text-sm font-medium text-gray-600">规则执行历史</CardTitle>
            </CardHeader>
            <CardContent className="text-center">
              <p className="text-xs text-gray-500 mb-2">未来功能</p>
              <p className="text-xs text-gray-400">查看规则的详细执行历史和结果统计</p>
              <div className="mt-3 space-y-1">
                <div className="h-2 bg-gray-200 rounded"></div>
                <div className="h-2 bg-gray-200 rounded w-3/4"></div>
                <div className="h-2 bg-gray-200 rounded w-1/2"></div>
              </div>
            </CardContent>
          </Card>

          {/* Rule Evaluation and Iteration Suggestions */}
          <Card className="border-2 border-dashed border-gray-300 bg-gray-50/50">
            <CardHeader className="text-center pb-4">
              <div className="mx-auto w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center mb-3">
                <TrendingUp className="h-6 w-6 text-gray-400" />
              </div>
              <CardTitle className="text-sm font-medium text-gray-600">规则评估与迭代建议</CardTitle>
            </CardHeader>
            <CardContent className="text-center">
              <p className="text-xs text-gray-500 mb-2">未来功能</p>
              <p className="text-xs text-gray-400">基于执行结果提供规则优化建议</p>
              <div className="mt-3 space-y-2">
                <div className="flex items-center justify-between text-xs">
                  <span className="text-gray-400">准确率</span>
                  <span className="text-gray-400">--</span>
                </div>
                <div className="flex items-center justify-between text-xs">
                  <span className="text-gray-400">覆盖率</span>
                  <span className="text-gray-400">--</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Rule Version Management */}
          <Card className="border-2 border-dashed border-gray-300 bg-gray-50/50">
            <CardHeader className="text-center pb-4">
              <div className="mx-auto w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center mb-3">
                <GitBranch className="h-6 w-6 text-gray-400" />
              </div>
              <CardTitle className="text-sm font-medium text-gray-600">规则版本管理</CardTitle>
            </CardHeader>
            <CardContent className="text-center">
              <p className="text-xs text-gray-500 mb-2">未来功能</p>
              <p className="text-xs text-gray-400">管理规则的版本历史和变更记录</p>
              <div className="mt-3 space-y-1">
                <div className="flex items-center gap-2 text-xs text-gray-400">
                  <div className="w-2 h-2 bg-gray-300 rounded-full"></div>
                  <span>v1.0</span>
                </div>
                <div className="flex items-center gap-2 text-xs text-gray-400">
                  <div className="w-2 h-2 bg-gray-300 rounded-full"></div>
                  <span>v1.1</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Rule Details Modal */}
      <Dialog open={!!selectedRule} onOpenChange={() => setSelectedRule(null)}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Eye className="h-5 w-5" />
              规则详情: {selectedRule?.name}
            </DialogTitle>
          </DialogHeader>
          {selectedRule && (
            <div className="space-y-6">
              {/* Basic Information */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-700">规则ID</label>
                  <p className="text-sm text-gray-900 mt-1 font-mono">{selectedRule.id}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">状态</label>
                  <div className="mt-1">{getStatusBadge(selectedRule.status)}</div>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">创建者</label>
                  <p className="text-sm text-gray-900 mt-1">{selectedRule.creator}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">最后修改日期</label>
                  <p className="text-sm text-gray-900 mt-1">{selectedRule.lastModified}</p>
                </div>
              </div>

              {/* Description */}
              <div>
                <label className="text-sm font-medium text-gray-700">规则描述</label>
                <p className="text-sm text-gray-900 mt-1 p-3 bg-gray-50 rounded border">{selectedRule.description}</p>
              </div>

              {/* Execution Statistics */}
              <div>
                <label className="text-sm font-medium text-gray-700">执行统计</label>
                <div className="mt-1 grid grid-cols-3 gap-4">
                  <div className="p-3 bg-blue-50 rounded border border-blue-200">
                    <div className="text-lg font-semibold text-blue-900">{selectedRule.executionCount}</div>
                    <div className="text-xs text-blue-700">执行次数</div>
                  </div>
                  <div className="p-3 bg-green-50 rounded border border-green-200">
                    <div className="text-lg font-semibold text-green-900">{selectedRule.hitCount}</div>
                    <div className="text-xs text-green-700">命中问题数</div>
                  </div>
                  <div className="p-3 bg-orange-50 rounded border border-orange-200">
                    <div className="text-lg font-semibold text-orange-900">
                      {selectedRule.executionCount > 0
                        ? Math.round((selectedRule.hitCount / selectedRule.executionCount) * 100) / 100
                        : 0}
                    </div>
                    <div className="text-xs text-orange-700">平均命中率</div>
                  </div>
                </div>
              </div>

              {/* Last Execution */}
              {selectedRule.lastExecution && (
                <div>
                  <label className="text-sm font-medium text-gray-700">最后执行时间</label>
                  <p className="text-sm text-gray-900 mt-1 font-mono">{selectedRule.lastExecution}</p>
                </div>
              )}

              {/* Action Buttons */}
              <div className="flex gap-4 pt-4 border-t">
                <Link href={`/rule-generation?ruleId=${selectedRule.id}`} className="flex-1">
                  <Button className="w-full">
                    <RefreshCw className="mr-2 h-4 w-4" />
                    解构与迭代
                  </Button>
                </Link>
                <Button variant="outline" disabled className="flex-1">
                  <Play className="mr-2 h-4 w-4" />
                  立即执行 (未来功能)
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}
