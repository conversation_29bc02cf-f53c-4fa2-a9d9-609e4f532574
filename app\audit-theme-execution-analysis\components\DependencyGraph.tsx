'use client'

import React from 'react'
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>it<PERSON> } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { 
  Database, 
  Table, 
  Columns, 
  ArrowRight, 
  AlertTriangle,
  CheckCircle,
  Clock
} from 'lucide-react'

interface DependencyNode {
  id: string
  name: string
  type: 'table' | 'view' | 'function' | 'procedure'
  status: 'normal' | 'warning' | 'error'
  performance: number
  dependencies: string[]
}

interface DependencyGraphProps {
  data: DependencyNode[]
}

export default function DependencyGraph({ data }: DependencyGraphProps) {
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'normal':
        return <CheckCircle className="w-4 h-4 text-green-500" />
      case 'warning':
        return <AlertTriangle className="w-4 h-4 text-yellow-500" />
      case 'error':
        return <AlertTriangle className="w-4 h-4 text-red-500" />
      default:
        return <Clock className="w-4 h-4 text-gray-500" />
    }
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'table':
        return <Table className="w-4 h-4" />
      case 'view':
        return <Database className="w-4 h-4" />
      case 'function':
        return <Columns className="w-4 h-4" />
      case 'procedure':
        return <Database className="w-4 h-4" />
      default:
        return <Database className="w-4 h-4" />
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'normal':
        return <Badge className="bg-green-100 text-green-800">正常</Badge>
      case 'warning':
        return <Badge className="bg-yellow-100 text-yellow-800">警告</Badge>
      case 'error':
        return <Badge className="bg-red-100 text-red-800">错误</Badge>
      default:
        return <Badge variant="secondary">未知</Badge>
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>血缘图谱依赖关系分析</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {data.map((node) => (
            <div key={node.id} className="border rounded-lg p-4">
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center gap-3">
                  {getStatusIcon(node.status)}
                  {getTypeIcon(node.type)}
                  <div>
                    <h4 className="font-medium">{node.name}</h4>
                    <p className="text-sm text-gray-600 capitalize">{node.type}</p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  {getStatusBadge(node.status)}
                  <Badge variant="outline">{node.performance}%</Badge>
                </div>
              </div>
              
              {node.dependencies.length > 0 && (
                <div className="mt-3">
                  <p className="text-sm text-gray-600 mb-2">依赖关系：</p>
                  <div className="flex flex-wrap gap-2">
                    {node.dependencies.map((dep, index) => (
                      <div key={index} className="flex items-center gap-1 text-sm">
                        <span className="text-gray-500">{dep}</span>
                        {index < node.dependencies.length - 1 && (
                          <ArrowRight className="w-3 h-3 text-gray-400" />
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              )}
              
              <div className="flex gap-2 mt-3">
                <Button size="sm" variant="outline">
                  查看详情
                </Button>
                <Button size="sm" variant="ghost">
                  优化建议
                </Button>
              </div>
            </div>
          ))}
        </div>
        
        {/* 依赖关系统计 */}
        <div className="mt-6 grid grid-cols-3 gap-4 p-4 bg-gray-50 rounded-lg">
          <div className="text-center">
            <p className="text-2xl font-bold text-blue-600">
              {data.length}
            </p>
            <p className="text-sm text-gray-600">总节点数</p>
          </div>
          <div className="text-center">
            <p className="text-2xl font-bold text-green-600">
              {data.filter(n => n.status === 'normal').length}
            </p>
            <p className="text-sm text-gray-600">正常节点</p>
          </div>
          <div className="text-center">
            <p className="text-2xl font-bold text-red-600">
              {data.filter(n => n.status === 'error').length}
            </p>
            <p className="text-sm text-gray-600">问题节点</p>
          </div>
        </div>
      </CardContent>
    </Card>
  )
} 