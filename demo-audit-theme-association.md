# 稽查主题要素关联功能演示

## 功能演示步骤

### 1. 访问页面
- 打开浏览器访问: `http://localhost:3000/audit-theme-association`
- 或者通过侧边栏菜单: 营销稽查主题规则智能化应用 > 稽查主题要素关联

### 2. 查看稽查主题视图
- 页面默认显示"稽查主题视图"标签页
- 可以看到三个示例稽查主题：
  - 电价政策合规性稽查 (高优先级)
  - 用电量异常稽查 (中优先级)
  - 客户信息真实性稽查 (高优先级)

### 3. 查看主题详情
- 点击任意主题卡片查看详细信息
- 查看该主题关联的稽查要素列表
- 观察关联强度和关联日期

### 4. 关联新要素
- 点击主题卡片上的"关联要素"按钮
- 在弹出的对话框中选择稽查主题
- 选择要关联的稽查要素（支持类型筛选）
- 点击"确认关联"完成关联

### 5. 更新关联要素
- 点击主题卡片上的"更新要素"按钮
- 观察更新进度条和状态提示
- 查看更新建议列表，包括：
  - 新增要素：最新电价调整政策、电价违规处罚标准
  - 更新要素：电价政策文件（已更新）
- 选择要应用的更新建议
- 点击"应用选中更新"确认应用

### 6. 查看稽查要素视图
- 切换到"稽查要素视图"标签页
- 查看所有稽查要素列表
- 使用搜索功能和类型筛选
- 观察要素的关联主题数量

### 7. 管理关联关系
- 切换到"关联关系管理"标签页
- 查看所有关联关系的总览
- 可以移除不需要的关联关系
- 观察关联强度和要素类型

## 功能亮点

### 1. 智能更新机制
- **一键更新**: 点击"更新要素"按钮即可触发智能分析
- **进度显示**: 实时显示更新进度和状态
- **详细建议**: 提供包含置信度、原因说明的更新建议
- **选择性应用**: 支持选择性地应用更新建议

### 2. 丰富的要素类型
- **风险点**: 用电量异常阈值等风险识别要素
- **政策依据**: 电价政策文件等法规依据
- **控制措施**: 相关的控制要求和措施
- **合规要求**: 客户身份验证等合规要求

### 3. 直观的用户界面
- **标签页设计**: 三个主要视图便于切换
- **卡片布局**: 主题信息清晰展示
- **状态标识**: 优先级、状态、关联强度等一目了然
- **操作按钮**: 关联要素、更新要素等操作便捷

### 4. 数据管理
- **关联强度**: 强关联、中关联、弱关联三级分类
- **时间记录**: 关联日期、更新时间等时间信息
- **来源追踪**: 要素来源、政策来源等信息记录
- **状态管理**: 活跃、非活跃、草稿等状态标识

## 技术特性

### 1. 响应式设计
- 支持桌面端和移动端访问
- 自适应布局和交互体验

### 2. 类型安全
- 完整的TypeScript类型定义
- 接口设计和数据模型规范

### 3. 组件化架构
- 模块化的组件设计
- 可复用的UI组件
- 清晰的状态管理

### 4. 用户体验
- 加载状态和进度提示
- 错误处理和用户反馈
- 直观的操作流程

## 示例数据

### 稽查主题示例
1. **电价政策合规性稽查**
   - 分类: 价格管理
   - 优先级: 高
   - 关联要素: 8个

2. **用电量异常稽查**
   - 分类: 用电管理
   - 优先级: 中
   - 关联要素: 12个

3. **客户信息真实性稽查**
   - 分类: 客户管理
   - 优先级: 高
   - 关联要素: 6个

### 稽查要素示例
1. **电价政策文件** (政策依据)
2. **用电量异常阈值** (风险点)
3. **客户身份验证要求** (合规要求)

### 更新建议示例
1. **新增要素**: 最新电价调整政策 (置信度: 95%)
2. **更新要素**: 电价政策文件 (置信度: 88%)
3. **新增要素**: 电价违规处罚标准 (置信度: 82%)

## 使用建议

1. **定期更新**: 建议定期使用"更新要素"功能保持要素的最新性
2. **关联管理**: 及时管理关联关系，移除过时的关联
3. **类型筛选**: 使用要素类型筛选功能快速找到目标要素
4. **批量操作**: 在更新建议中选择性地应用更新，避免不必要的变更 