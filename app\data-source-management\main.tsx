"use client"

import { useState, use<PERSON>emo } from "react"
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Checkbox } from "@/components/ui/checkbox"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { 
  Search, 
  Eye, 
  Edit, 
  Trash2, 
  Plus,
  FileText,
  Database,
  Globe,
  File,
  Link,
  RefreshCw
} from "lucide-react"
import { 
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination"

// 知识源类型定义
interface DataSource {
  id: string
  name: string
  type: 'PDF' | 'Word' | 'TXT' | 'URL' | 'Database' | 'API'
  description: string
  url?: string
  filePath?: string
  status: 'Active' | 'Inactive' | 'Error' | 'Processing'
  lastUpdate: string
  lastSync: string
  tags: string[]
  metadata?: {
    size?: string
    pages?: number
    encoding?: string
    lastModified?: string
  }
}

// 模拟数据
const mockDataSources: DataSource[] = [
  {
    id: "DS001",
    name: "电力营销政策文件库",
    type: "PDF",
    description: "包含国家电网公司最新的营销政策文件，用于稽查要素提取",
    filePath: "/documents/policies/2024/",
    status: "Active",
    lastUpdate: "2024-01-15 10:30:00",
    lastSync: "2024-01-15 10:30:00",
    tags: ["政策文件", "营销", "PDF"],
    metadata: {
      size: "2.5MB",
      pages: 45,
      encoding: "UTF-8",
      lastModified: "2024-01-15"
    }
  },
  {
    id: "DS002",
    name: "客户投诉报告",
    type: "Word",
    description: "客户投诉相关文档，用于分析客户问题和稽查要素识别",
    filePath: "/documents/complaints/",
    status: "Active",
    lastUpdate: "2024-01-14 15:20:00",
    lastSync: "2024-01-14 15:20:00",
    tags: ["投诉", "客户", "Word"],
    metadata: {
      size: "1.8MB",
      pages: 23,
      encoding: "UTF-8",
      lastModified: "2024-01-14"
    }
  },
  {
    id: "DS003",
    name: "国家能源局官网",
    type: "URL",
    description: "国家能源局官方网站，获取最新的能源政策和法规",
    url: "https://www.nea.gov.cn/",
    status: "Active",
    lastUpdate: "2024-01-13 09:15:00",
    lastSync: "2024-01-13 09:15:00",
    tags: ["官方网站", "政策", "URL"]
  },
  {
    id: "DS004",
    name: "稽查案例数据库",
    type: "Database",
    description: "历史稽查案例数据库，包含成功案例和失败案例",
    status: "Active",
    lastUpdate: "2024-01-12 14:45:00",
    lastSync: "2024-01-12 14:45:00",
    tags: ["案例", "数据库", "历史"]
  },
  {
    id: "DS005",
    name: "临时文档",
    type: "TXT",
    description: "临时存储的文本文件，用于测试和验证",
    filePath: "/temp/test.txt",
    status: "Inactive",
    lastUpdate: "2024-01-11 16:30:00",
    lastSync: "2024-01-11 16:30:00",
    tags: ["临时", "测试", "TXT"],
    metadata: {
      size: "15KB",
      encoding: "UTF-8",
      lastModified: "2024-01-11"
    }
  },
  {
    id: "DS006",
    name: "外部API接口",
    type: "API",
    description: "第三方数据服务API，用于获取实时数据",
    url: "https://api.external-service.com/data",
    status: "Error",
    lastUpdate: "2024-01-10 11:20:00",
    lastSync: "2024-01-10 11:20:00",
    tags: ["API", "外部", "实时"]
  }
]

export function DataSourceManagement() {
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedType, setSelectedType] = useState<string>("all")
  const [selectedStatus, setSelectedStatus] = useState<string>("all")
  const [selectedElements, setSelectedElements] = useState<string[]>([])
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [editingDataSource, setEditingDataSource] = useState<DataSource | null>(null)
  const [deletingDataSource, setDeletingDataSource] = useState<DataSource | null>(null)
  const [currentPage, setCurrentPage] = useState(1)
  const itemsPerPage = 10

  // 新增知识源表单状态
  const [newDataSource, setNewDataSource] = useState({
    name: "",
    type: "PDF" as DataSource['type'],
    description: "",
    url: "",
    filePath: "",
    tags: ""
  })

  // 过滤后的数据
  const filteredDataSources = useMemo(() => {
    return mockDataSources.filter(source => {
      const matchesSearch = source.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           source.description.toLowerCase().includes(searchTerm.toLowerCase())
      const matchesType = selectedType === "all" || source.type === selectedType
      const matchesStatus = selectedStatus === "all" || source.status === selectedStatus
      
      return matchesSearch && matchesType && matchesStatus
    })
  }, [searchTerm, selectedType, selectedStatus])

  // 分页数据
  const paginatedDataSources = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage
    const endIndex = startIndex + itemsPerPage
    return filteredDataSources.slice(startIndex, endIndex)
  }, [filteredDataSources, currentPage])

  // 总页数
  const totalPages = Math.ceil(filteredDataSources.length / itemsPerPage)

  // 当过滤条件改变时重置到第一页
  const resetToFirstPage = () => {
    setCurrentPage(1)
  }

  // 处理搜索和过滤变化
  const handleSearchChange = (value: string) => {
    setSearchTerm(value)
    resetToFirstPage()
  }

  const handleTypeChange = (value: string) => {
    setSelectedType(value)
    resetToFirstPage()
  }

  const handleStatusChange = (value: string) => {
    setSelectedStatus(value)
    resetToFirstPage()
  }

  // 选择相关处理
  const handleSelectDataSource = (sourceId: string, checked: boolean) => {
    if (checked) {
      setSelectedElements(prev => [...prev, sourceId])
    } else {
      setSelectedElements(prev => prev.filter(id => id !== sourceId))
    }
  }

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedElements(paginatedDataSources.map(source => source.id))
    } else {
      setSelectedElements([])
    }
  }

  // 批量操作
  const handleBulkAction = (action: string) => {
    if (selectedElements.length === 0) return

    switch (action) {
      case 'activate':
        console.log('批量激活:', selectedElements)
        break
      case 'deactivate':
        console.log('批量停用:', selectedElements)
        break
      case 'delete':
        console.log('批量删除:', selectedElements)
        break
      case 'sync':
        console.log('批量同步:', selectedElements)
        break
    }
    setSelectedElements([])
  }

  // 单个知识源操作
  const handleDataSourceAction = (action: string, source: DataSource) => {
    switch (action) {
      case 'view':
        console.log('查看知识源:', source)
        break
      case 'edit':
        setEditingDataSource(source)
        setIsEditDialogOpen(true)
        break
      case 'delete':
        setDeletingDataSource(source)
        setIsDeleteDialogOpen(true)
        break
      case 'sync':
        console.log('同步知识源:', source)
        break
    }
  }

  // 获取状态徽章样式
  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'Active':
        return 'default'
      case 'Inactive':
        return 'secondary'
      case 'Error':
        return 'destructive'
      case 'Processing':
        return 'outline'
      default:
        return 'secondary'
    }
  }

  // 获取类型图标
  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'PDF':
        return <FileText className="h-4 w-4" />
      case 'Word':
        return <FileText className="h-4 w-4" />
      case 'TXT':
        return <File className="h-4 w-4" />
      case 'URL':
        return <Globe className="h-4 w-4" />
      case 'Database':
        return <Database className="h-4 w-4" />
      case 'API':
        return <Link className="h-4 w-4" />
      default:
        return <File className="h-4 w-4" />
    }
  }

  // 添加新知识源
  const handleAddDataSource = () => {
    const tags = newDataSource.tags.split(',').map(tag => tag.trim()).filter(tag => tag)
    const source: DataSource = {
      id: `DS${Date.now()}`,
      name: newDataSource.name,
      type: newDataSource.type,
      description: newDataSource.description,
      url: newDataSource.url || undefined,
      filePath: newDataSource.filePath || undefined,
      status: 'Active',
      lastUpdate: new Date().toLocaleString('zh-CN'),
      lastSync: new Date().toLocaleString('zh-CN'),
      tags
    }
    
    console.log('添加知识源:', source)
    setIsAddDialogOpen(false)
    setNewDataSource({
      name: "",
      type: "PDF",
      description: "",
      url: "",
      filePath: "",
      tags: ""
    })
  }

  // 编辑知识源
  const handleEditDataSource = () => {
    if (!editingDataSource) return
    
    console.log('编辑知识源:', editingDataSource)
    setIsEditDialogOpen(false)
    setEditingDataSource(null)
  }

  // 删除知识源
  const handleDeleteDataSource = () => {
    if (!deletingDataSource) return
    
    console.log('删除知识源:', deletingDataSource)
    setIsDeleteDialogOpen(false)
    setDeletingDataSource(null)
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">稽查知识源管理</h1>
          <p className="text-muted-foreground">
            管理和配置稽查知识的知识源，支持多种格式和来源
          </p>
        </div>
        <Button onClick={() => setIsAddDialogOpen(true)}>
          <Plus className="h-4 w-4 mr-2" />
          添加知识源
        </Button>
      </div>

      {/* 搜索和过滤 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Search className="h-5 w-5" />
            搜索和过滤
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="space-y-2">
              <Label htmlFor="search">搜索</Label>
              <Input
                id="search"
                placeholder="搜索知识源名称或描述..."
                value={searchTerm}
                onChange={(e) => handleSearchChange(e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="type">类型</Label>
              <Select value={selectedType} onValueChange={handleTypeChange}>
                <SelectTrigger>
                  <SelectValue placeholder="选择类型" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部类型</SelectItem>
                  <SelectItem value="PDF">PDF文档</SelectItem>
                  <SelectItem value="Word">Word文档</SelectItem>
                  <SelectItem value="TXT">文本文件</SelectItem>
                  <SelectItem value="URL">网页链接</SelectItem>
                  <SelectItem value="Database">数据库</SelectItem>
                  <SelectItem value="API">API接口</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="status">状态</Label>
              <Select value={selectedStatus} onValueChange={handleStatusChange}>
                <SelectTrigger>
                  <SelectValue placeholder="选择状态" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部状态</SelectItem>
                  <SelectItem value="Active">活跃</SelectItem>
                  <SelectItem value="Inactive">停用</SelectItem>
                  <SelectItem value="Error">错误</SelectItem>
                  <SelectItem value="Processing">处理中</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label>操作</Label>
              <div className="flex gap-2">
                <Button variant="outline" size="sm" onClick={() => handleBulkAction('sync')}>
                  <RefreshCw className="h-4 w-4 mr-1" />
                  同步
                </Button>
                <Button variant="outline" size="sm" onClick={() => handleBulkAction('delete')}>
                  <Trash2 className="h-4 w-4 mr-1" />
                  删除
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 知识源列表 */}
      <Card>
        <CardHeader>
          <CardTitle>知识源列表</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-12">
                    <Checkbox
                      checked={selectedElements.length === paginatedDataSources.length && paginatedDataSources.length > 0}
                      onCheckedChange={handleSelectAll}
                    />
                  </TableHead>
                  <TableHead>知识源</TableHead>
                  <TableHead>类型</TableHead>
                  <TableHead>状态</TableHead>
                  <TableHead>最后更新</TableHead>
                  <TableHead>最后同步</TableHead>
                  <TableHead>标签</TableHead>
                  <TableHead className="text-right">操作</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {paginatedDataSources.map((source) => (
                  <TableRow key={source.id}>
                    <TableCell>
                      <Checkbox
                        checked={selectedElements.includes(source.id)}
                        onCheckedChange={(checked) => handleSelectDataSource(source.id, checked as boolean)}
                      />
                    </TableCell>
                    <TableCell>
                      <div>
                        <div className="font-medium">{source.name}</div>
                        <div className="text-sm text-muted-foreground">{source.description}</div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        {getTypeIcon(source.type)}
                        <span>{source.type}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant={getStatusBadgeVariant(source.status)}>
                        {source.status}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">
                        {new Date(source.lastUpdate).toLocaleDateString('zh-CN')}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">
                        {new Date(source.lastSync).toLocaleDateString('zh-CN')}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex flex-wrap gap-1">
                        {source.tags.slice(0, 2).map((tag, index) => (
                          <Badge key={index} variant="outline" className="text-xs">
                            {tag}
                          </Badge>
                        ))}
                        {source.tags.length > 2 && (
                          <Badge variant="outline" className="text-xs">
                            +{source.tags.length - 2}
                          </Badge>
                        )}
                      </div>
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex items-center justify-end gap-2">
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleDataSourceAction('view', source)}
                              >
                                <Eye className="h-4 w-4" />
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent>查看详情</TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleDataSourceAction('edit', source)}
                              >
                                <Edit className="h-4 w-4" />
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent>编辑</TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleDataSourceAction('sync', source)}
                              >
                                <RefreshCw className="h-4 w-4" />
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent>同步</TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleDataSourceAction('delete', source)}
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent>删除</TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>

          {/* 分页 */}
          {totalPages > 1 && (
            <div className="mt-4">
              <Pagination>
                <PaginationContent>
                  <PaginationItem>
                    <PaginationPrevious 
                      onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                      className={currentPage === 1 ? "pointer-events-none opacity-50" : "cursor-pointer"}
                    />
                  </PaginationItem>
                  {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                    <PaginationItem key={page}>
                      <PaginationLink
                        onClick={() => setCurrentPage(page)}
                        isActive={currentPage === page}
                        className="cursor-pointer"
                      >
                        {page}
                      </PaginationLink>
                    </PaginationItem>
                  ))}
                  <PaginationItem>
                    <PaginationNext 
                      onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                      className={currentPage === totalPages ? "pointer-events-none opacity-50" : "cursor-pointer"}
                    />
                  </PaginationItem>
                </PaginationContent>
              </Pagination>
            </div>
          )}
        </CardContent>
      </Card>

      {/* 添加知识源对话框 */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>添加知识源</DialogTitle>
            <DialogDescription>
              配置新的知识源，支持多种格式和来源类型
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">知识源名称</Label>
                <Input
                  id="name"
                  value={newDataSource.name}
                  onChange={(e) => setNewDataSource(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="输入知识源名称"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="type">类型</Label>
                <Select value={newDataSource.type} onValueChange={(value: DataSource['type']) => setNewDataSource(prev => ({ ...prev, type: value }))}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="PDF">PDF文档</SelectItem>
                    <SelectItem value="Word">Word文档</SelectItem>
                    <SelectItem value="TXT">文本文件</SelectItem>
                    <SelectItem value="URL">网页链接</SelectItem>
                    <SelectItem value="Database">数据库</SelectItem>
                    <SelectItem value="API">API接口</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="description">描述</Label>
              <Textarea
                id="description"
                value={newDataSource.description}
                onChange={(e) => setNewDataSource(prev => ({ ...prev, description: e.target.value }))}
                placeholder="输入知识源描述"
                rows={3}
              />
            </div>
            {(newDataSource.type === 'URL' || newDataSource.type === 'API') && (
              <div className="space-y-2">
                <Label htmlFor="url">URL地址</Label>
                <Input
                  id="url"
                  value={newDataSource.url}
                  onChange={(e) => setNewDataSource(prev => ({ ...prev, url: e.target.value }))}
                  placeholder="输入URL地址"
                />
              </div>
            )}
            {(newDataSource.type === 'PDF' || newDataSource.type === 'Word' || newDataSource.type === 'TXT') && (
              <div className="space-y-2">
                <Label htmlFor="filePath">文件路径</Label>
                <Input
                  id="filePath"
                  value={newDataSource.filePath}
                  onChange={(e) => setNewDataSource(prev => ({ ...prev, filePath: e.target.value }))}
                  placeholder="输入文件路径"
                />
              </div>
            )}
            <div className="space-y-2">
              <Label htmlFor="tags">标签</Label>
              <Input
                id="tags"
                value={newDataSource.tags}
                onChange={(e) => setNewDataSource(prev => ({ ...prev, tags: e.target.value }))}
                placeholder="输入标签，用逗号分隔"
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
              取消
            </Button>
            <Button onClick={handleAddDataSource}>
              添加
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 编辑知识源对话框 */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>编辑知识源</DialogTitle>
            <DialogDescription>
              修改知识源配置信息
            </DialogDescription>
          </DialogHeader>
          {editingDataSource && (
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="edit-name">知识源名称</Label>
                  <Input
                    id="edit-name"
                    value={editingDataSource.name}
                    onChange={(e) => setEditingDataSource(prev => prev ? { ...prev, name: e.target.value } : null)}
                    placeholder="输入知识源名称"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="edit-type">类型</Label>
                  <Select value={editingDataSource.type} onValueChange={(value: DataSource['type']) => setEditingDataSource(prev => prev ? { ...prev, type: value } : null)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="PDF">PDF文档</SelectItem>
                      <SelectItem value="Word">Word文档</SelectItem>
                      <SelectItem value="TXT">文本文件</SelectItem>
                      <SelectItem value="URL">网页链接</SelectItem>
                      <SelectItem value="Database">数据库</SelectItem>
                      <SelectItem value="API">API接口</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-description">描述</Label>
                <Textarea
                  id="edit-description"
                  value={editingDataSource.description}
                  onChange={(e) => setEditingDataSource(prev => prev ? { ...prev, description: e.target.value } : null)}
                  placeholder="输入知识源描述"
                  rows={3}
                />
              </div>
              {(editingDataSource.type === 'URL' || editingDataSource.type === 'API') && (
                <div className="space-y-2">
                  <Label htmlFor="edit-url">URL地址</Label>
                  <Input
                    id="edit-url"
                    value={editingDataSource.url || ''}
                    onChange={(e) => setEditingDataSource(prev => prev ? { ...prev, url: e.target.value } : null)}
                    placeholder="输入URL地址"
                  />
                </div>
              )}
              {(editingDataSource.type === 'PDF' || editingDataSource.type === 'Word' || editingDataSource.type === 'TXT') && (
                <div className="space-y-2">
                  <Label htmlFor="edit-filePath">文件路径</Label>
                  <Input
                    id="edit-filePath"
                    value={editingDataSource.filePath || ''}
                    onChange={(e) => setEditingDataSource(prev => prev ? { ...prev, filePath: e.target.value } : null)}
                    placeholder="输入文件路径"
                  />
                </div>
              )}
              <div className="space-y-2">
                <Label htmlFor="edit-status">状态</Label>
                <Select value={editingDataSource.status} onValueChange={(value: DataSource['status']) => setEditingDataSource(prev => prev ? { ...prev, status: value } : null)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Active">活跃</SelectItem>
                    <SelectItem value="Inactive">停用</SelectItem>
                    <SelectItem value="Error">错误</SelectItem>
                    <SelectItem value="Processing">处理中</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              取消
            </Button>
            <Button onClick={handleEditDataSource}>
              保存
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 删除确认对话框 */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>确认删除</DialogTitle>
            <DialogDescription>
              确定要删除知识源 "{deletingDataSource?.name}" 吗？此操作不可撤销。
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
              取消
            </Button>
            <Button variant="destructive" onClick={handleDeleteDataSource}>
              删除
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
} 