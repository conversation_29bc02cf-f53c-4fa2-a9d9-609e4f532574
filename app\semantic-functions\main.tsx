"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { 
  Search, 
  Filter, 
  Plus, 
  Eye, 
  Edit, 
  Copy, 
  MoreHorizontal,
  Code,
  BookOpen,
  Users,
  Zap
} from "lucide-react"

// 语义逻辑函数库定义说明
const functionLibDesc = `语义逻辑函数库：预定义的一组操作符和函数，用于解释、处理和转换语义模型中的数据和关系，从而实现业务逻辑和用户查询的准确解析与执行。`

const mockFunctions = [
  {
    id: 1,
    name: "CONTAINS",
    category: "操作符",
    description: "判断字段值是否包含指定内容。",
    usage: 120,
    status: "active",
    tags: ["包含", "字符串", "条件"],
    parameters: [
      { name: "field", type: "string", required: true, description: "字段名" },
      { name: "value", type: "string", required: true, description: "要匹配的内容" }
    ],
    returnType: "boolean",
    examples: [
      "CONTAINS(customer_name, '张三') // 判断客户名是否包含'张三'"
    ],
    definition: "用于处理'包含'操作，常用于模糊查询。",
    relatedFunctions: ["EQUALS", "OR"],
    physicalMappings: [
      { name: "INSTR", type: "MySQL函数", example: "INSTR(customer_name, '张三') > 0", desc: "MySQL字符串包含判断" },
      { name: "LIKE", type: "SQL表达式", example: "customer_name LIKE '%张三%'", desc: "SQL模糊匹配" }
    ]
  },
  {
    id: 2,
    name: "EQUALS",
    category: "操作符",
    description: "判断字段值是否等于指定内容。",
    usage: 110,
    status: "active",
    tags: ["等于", "比较", "条件"],
    parameters: [
      { name: "field", type: "string", required: true, description: "字段名" },
      { name: "value", type: "any", required: true, description: "要比较的值" }
    ],
    returnType: "boolean",
    examples: [
      "EQUALS(status, '已完成') // 判断状态是否为'已完成'"
    ],
    definition: "用于处理'等于'操作，常用于精确匹配。",
    relatedFunctions: ["CONTAINS", "AND"],
    physicalMappings: [
      { name: "=", type: "SQL运算符", example: "status = '已完成'", desc: "SQL等值判断" }
    ]
  },
  {
    id: 3,
    name: "GREATER_THAN",
    category: "操作符",
    description: "判断字段值是否大于指定值。",
    usage: 90,
    status: "active",
    tags: ["大于", "比较", "条件"],
    parameters: [
      { name: "field", type: "string", required: true, description: "字段名" },
      { name: "value", type: "number", required: true, description: "比较值" }
    ],
    returnType: "boolean",
    examples: [
      "GREATER_THAN(amount, 1000) // 判断金额是否大于1000"
    ],
    definition: "用于处理'大于'操作，常用于数值比较。",
    relatedFunctions: ["EQUALS", "LESS_THAN"],
    physicalMappings: [
      { name: ">", type: "SQL运算符", example: "amount > 1000", desc: "SQL大于判断" }
    ]
  },
  {
    id: 4,
    name: "AND",
    category: "逻辑运算",
    description: "多个条件的逻辑与。",
    usage: 150,
    status: "active",
    tags: ["与", "逻辑", "条件"],
    parameters: [
      { name: "conditions", type: "array", required: true, description: "条件数组" }
    ],
    returnType: "boolean",
    examples: [
      "AND(EQUALS(type, '高压'), GREATER_THAN(amount, 1000))"
    ],
    definition: "逻辑与运算，所有条件均为真时返回真。",
    relatedFunctions: ["OR"],
    physicalMappings: [
      { name: "AND", type: "SQL关键字", example: "type = '高压' AND amount > 1000", desc: "SQL多条件与" }
    ]
  },
  {
    id: 5,
    name: "OR",
    category: "逻辑运算",
    description: "多个条件的逻辑或。",
    usage: 140,
    status: "active",
    tags: ["或", "逻辑", "条件"],
    parameters: [
      { name: "conditions", type: "array", required: true, description: "条件数组" }
    ],
    returnType: "boolean",
    examples: [
      "OR(EQUALS(region, '南区'), EQUALS(region, '北区'))"
    ],
    definition: "逻辑或运算，任一条件为真时返回真。",
    relatedFunctions: ["AND"],
    physicalMappings: [
      { name: "OR", type: "SQL关键字", example: "region = '南区' OR region = '北区'", desc: "SQL多条件或" }
    ]
  },
  {
    id: 6,
    name: "AGGREGATE_SUM",
    category: "聚合函数",
    description: "对指定字段进行求和聚合。",
    usage: 80,
    status: "active",
    tags: ["聚合", "求和", "统计"],
    parameters: [
      { name: "field", type: "string", required: true, description: "需要求和的字段名" },
      { name: "group_by_fields", type: "array", required: false, description: "分组字段列表" }
    ],
    returnType: "number",
    examples: [
      "AGGREGATE_SUM(power_usage, [month]) // 统计每月用电量"
    ],
    definition: "对数据集中的指定字段进行分组求和，常用于业务统计分析。",
    relatedFunctions: ["AGGREGATE_AVG", "AGGREGATE_MAX"],
    physicalMappings: [
      { name: "SUM", type: "SQL聚合函数", example: "SUM(power_usage)", desc: "SQL求和聚合" }
    ]
  },
  {
    id: 7,
    name: "FILTER_BY_MONTH",
    category: "过滤函数",
    description: "按月份过滤数据。",
    usage: 60,
    status: "active",
    tags: ["过滤", "时间", "月份"],
    parameters: [
      { name: "date_field", type: "string", required: true, description: "日期字段名" },
      { name: "month_value", type: "string", required: true, description: "月份值，如'2024-06'" }
    ],
    returnType: "array",
    examples: [
      "FILTER_BY_MONTH(bill_date, '2024-06') // 过滤出6月账单数据"
    ],
    definition: "根据指定月份筛选数据，常用于时间维度分析。",
    relatedFunctions: ["FILTER_BY_DATE_RANGE"],
    physicalMappings: [
      { name: "DATE_FORMAT & =", type: "MySQL表达式", example: "DATE_FORMAT(bill_date, '%Y-%m') = '2024-06'", desc: "按月过滤" }
    ]
  }
]

const categories = ["全部", "操作符", "逻辑运算", "聚合函数", "过滤函数", "数据格式化", "统计分析", "业务逻辑"]
const statuses = ["全部", "active", "draft", "deprecated"]

export default function SemanticFunctions() {
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("全部")
  const [selectedStatus, setSelectedStatus] = useState("全部")
  const [selectedFunction, setSelectedFunction] = useState<any>(null)

  const filteredFunctions = mockFunctions.filter(func => {
    const matchesSearch = func.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         func.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         func.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
    const matchesCategory = selectedCategory === "全部" || func.category === selectedCategory
    const matchesStatus = selectedStatus === "全部" || func.status === selectedStatus
    
    return matchesSearch && matchesCategory && matchesStatus
  })

  return (
    <div className="space-y-6">
      {/* 顶部定义说明 */}
      {/* <Card>
        <CardHeader>
          <CardTitle>语义逻辑函数库</CardTitle>
          <CardDescription>{functionLibDesc}</CardDescription>
        </CardHeader>
      </Card> */}

      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">语义逻辑函数库管理</h1>
          <p className="text-muted-foreground">
            管理语义逻辑函数，支持函数分类、参数配置和使用示例
          </p>
        </div>
        <Button>
          <Plus className="h-4 w-4 mr-2" />
          新建函数
        </Button>
      </div>

      <Card>
        <CardContent className="p-6">
          <div className="grid gap-4 md:grid-cols-4">
            <div className="relative">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="搜索函数名称、描述或标签..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={selectedCategory} onValueChange={setSelectedCategory}>
              <SelectTrigger>
                <SelectValue placeholder="选择分类" />
              </SelectTrigger>
              <SelectContent>
                {categories.map((category) => (
                  <SelectItem key={category} value={category}>
                    {category}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select value={selectedStatus} onValueChange={setSelectedStatus}>
              <SelectTrigger>
                <SelectValue placeholder="选择状态" />
              </SelectTrigger>
              <SelectContent>
                {statuses.map((status) => (
                  <SelectItem key={status} value={status}>
                    {status === 'active' ? '已激活' : status === 'draft' ? '草稿' : status === 'deprecated' ? '已废弃' : status}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Button variant="outline" className="w-full">
              <Filter className="h-4 w-4 mr-2" />
              高级筛选
            </Button>
          </div>
        </CardContent>
      </Card>

      <div className="grid gap-4">
        {filteredFunctions.map((func) => (
          <Card key={func.id} className="cursor-pointer hover:shadow-md transition-shadow">
            <CardContent className="p-6">
              <div className="flex items-start justify-between">
                <div className="flex-1 space-y-3">
                  <div className="flex items-center space-x-3">
                    <BookOpen className="h-5 w-5 text-green-500" />
                    <div>
                      <h3 className="text-lg font-semibold">{func.name}</h3>
                      <p className="text-sm text-muted-foreground">{func.description}</p>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-4">
                    <Badge variant="outline">{func.category}</Badge>
                    <Badge variant={func.status === 'active' ? 'default' : 'secondary'}>
                      {func.status === 'active' ? '已激活' : func.status === 'draft' ? '草稿' : '已废弃'}
                    </Badge>
                    <div className="flex items-center space-x-1 text-sm text-muted-foreground">
                      <Users className="h-4 w-4" />
                      <span>使用 {func.usage} 次</span>
                    </div>
                    <div className="flex items-center space-x-1 text-sm text-muted-foreground">
                      <Code className="h-4 w-4" />
                      <span>返回 {func.returnType}</span>
                    </div>
                  </div>
                  
                  <div className="flex flex-wrap gap-2">
                    {func.tags.map((tag, index) => (
                      <Badge key={index} variant="outline" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                </div>
                
                <div className="flex items-center space-x-2">
                  <Dialog>
                    <DialogTrigger asChild>
                      <Button 
                        variant="ghost" 
                        size="sm"
                        onClick={() => setSelectedFunction(func)}
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
                      <DialogHeader>
                        <DialogTitle className="flex items-center space-x-2">
                          <BookOpen className="h-5 w-5 text-green-500" />
                          <span>{func.name}</span>
                        </DialogTitle>
                        <DialogDescription>{func.description}</DialogDescription>
                      </DialogHeader>
                      
                      <Tabs defaultValue="parameters" className="w-full">
                        <TabsList>
                          <TabsTrigger value="parameters">参数配置</TabsTrigger>
                          <TabsTrigger value="examples">使用示例</TabsTrigger>
                          <TabsTrigger value="related">关联函数</TabsTrigger>
                          <TabsTrigger value="physical">物理层映射</TabsTrigger>
                        </TabsList>
                        
                        <TabsContent value="parameters" className="space-y-4">
                          <Card>
                            <CardHeader>
                              <CardTitle className="text-lg">参数配置</CardTitle>
                            </CardHeader>
                            <CardContent>
                              <div className="space-y-3">
                                {func.parameters.map((param, index) => (
                                  <div key={index} className="flex items-center justify-between p-3 border rounded">
                                    <div className="flex-1">
                                      <div className="flex items-center space-x-2">
                                        <span className="font-medium">{param.name}</span>
                                        <Badge variant="outline">{param.type}</Badge>
                                        {param.required && <Badge variant="destructive">必需</Badge>}
                                      </div>
                                      <p className="text-sm text-muted-foreground mt-1">{param.description}</p>
                                      {"default" in param && param.default !== undefined && (
                                        <p className="text-xs text-muted-foreground mt-1">
                                          默认值: {String(param.default)}
                                        </p>
                                      )}
                                    </div>
                                  </div>
                                ))}
                              </div>
                            </CardContent>
                          </Card>
                        </TabsContent>
                        
                        <TabsContent value="examples" className="space-y-4">
                          <Card>
                            <CardHeader>
                              <CardTitle className="text-lg">使用示例</CardTitle>
                            </CardHeader>
                            <CardContent>
                              <div className="space-y-3">
                                {func.examples.map((example, index) => (
                                  <div key={index} className="p-3 bg-muted rounded-lg">
                                    <p className="text-sm font-mono">{example}</p>
                                  </div>
                                ))}
                              </div>
                            </CardContent>
                          </Card>
                        </TabsContent>
                        
                        <TabsContent value="related" className="space-y-4">
                          <Card>
                            <CardHeader>
                              <CardTitle className="text-lg">关联函数</CardTitle>
                            </CardHeader>
                            <CardContent>
                              <div className="grid gap-2">
                                {func.relatedFunctions.map((related, index) => (
                                  <div key={index} className="flex items-center justify-between p-2 border rounded">
                                    <span className="text-sm font-medium">{related}</span>
                                    <Button variant="ghost" size="sm">
                                      <Eye className="h-4 w-4" />
                                    </Button>
                                  </div>
                                ))}
                              </div>
                            </CardContent>
                          </Card>
                        </TabsContent>
                        
                        <TabsContent value="physical" className="space-y-4">
                          <Card>
                            <CardHeader>
                              <CardTitle className="text-lg">物理层函数/逻辑映射</CardTitle>
                            </CardHeader>
                            <CardContent>
                              {func.physicalMappings && func.physicalMappings.length > 0 ? (
                                <table className="w-full text-sm border mb-2">
                                  <thead>
                                    <tr className="bg-gray-50">
                                      <th className="p-2 border">物理层函数/表达式</th>
                                      <th className="p-2 border">类型</th>
                                      <th className="p-2 border">示例</th>
                                      <th className="p-2 border">说明</th>
                                    </tr>
                                  </thead>
                                  <tbody>
                                    {func.physicalMappings.map((mapping, idx) => (
                                      <tr key={idx}>
                                        <td className="p-2 border">{mapping.name}</td>
                                        <td className="p-2 border">{mapping.type}</td>
                                        <td className="p-2 border font-mono">{mapping.example}</td>
                                        <td className="p-2 border">{mapping.desc}</td>
                                      </tr>
                                    ))}
                                  </tbody>
                                </table>
                              ) : (
                                <div className="text-muted-foreground">暂无物理层映射</div>
                              )}
                            </CardContent>
                          </Card>
                        </TabsContent>
                      </Tabs>
                    </DialogContent>
                  </Dialog>
                  
                  <Button variant="ghost" size="sm">
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button variant="ghost" size="sm">
                    <Copy className="h-4 w-4" />
                  </Button>
                  <Button variant="ghost" size="sm">
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <div className="flex items-center justify-between">
        <p className="text-sm text-muted-foreground">
          显示 {filteredFunctions.length} 个函数，共 {mockFunctions.length} 个
        </p>
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm" disabled>
            上一页
          </Button>
          <Button variant="outline" size="sm">
            下一页
          </Button>
        </div>
      </div>
    </div>
  )
} 