import { Parser } from 'node-sql-parser'
import type { 
  SQLParseResult, 
  TableReference, 
  ColumnReference, 
  Relation, 
  JoinCondition,
  WhereCondition,
  ParseOptions,
  SQLOperation
} from '@/types/sql-analysis'

export class SQLAnalyzer {
  private parser: Parser
  private options: ParseOptions

  constructor(options: Partial<ParseOptions> = {}) {
    this.parser = new Parser()
    this.options = {
      dialect: 'mysql',
      strict_mode: false,
      extract_relations: true,
      infer_implicit_relations: true,
      confidence_threshold: 0.7,
      max_parsing_time_ms: 5000,
      ...options
    }
  }

  async parseSQL(sqlContent: string): Promise<SQLParseResult> {
    const startTime = Date.now()
    
    try {
      // 使用node-sql-parser解析SQL
      const ast = this.parser.astify(sqlContent, { database: this.options.dialect })
      
      const tables = this.extractTables(ast)
      const columns = this.extractColumns(ast)
      const relations = this.extractRelations(ast, sqlContent)
      
      const parseResult: SQLParseResult = {
        id: this.generateId(),
        sql_content: sqlContent,
        parse_status: 'SUCCESS',
        extracted_tables: tables,
        extracted_relations: relations,
        extracted_columns: columns,
        confidence_score: this.calculateConfidenceScore(relations),
        created_at: new Date(),
        parsing_time_ms: Date.now() - startTime
      }

      return parseResult
    } catch (error) {
      return {
        id: this.generateId(),
        sql_content: sqlContent,
        parse_status: 'FAILED',
        extracted_tables: [],
        extracted_relations: [],
        extracted_columns: [],
        confidence_score: 0,
        created_at: new Date(),
        parsing_time_ms: Date.now() - startTime
      }
    }
  }

  private extractTables(ast: any): TableReference[] {
    const tables: TableReference[] = []
    
    if (Array.isArray(ast)) {
      ast.forEach(statement => this.extractTablesFromStatement(statement, tables))
    } else {
      this.extractTablesFromStatement(ast, tables)
    }
    
    return tables
  }

  private extractTablesFromStatement(statement: any, tables: TableReference[]): void {
    if (!statement) return

    // 处理FROM子句
    if (statement.from) {
      statement.from.forEach((fromItem: any) => {
        if (fromItem.table) {
          tables.push({
            name: fromItem.table,
            alias: fromItem.as,
            schema: fromItem.db,
            type: 'BASE_TABLE',
            operations: [this.getOperationType(statement)]
          })
        }
      })
    }

    // 处理JOIN子句
    if (statement.from) {
      statement.from.forEach((fromItem: any) => {
        if (fromItem.join) {
          fromItem.join.forEach((joinItem: any) => {
            if (joinItem.table) {
              tables.push({
                name: joinItem.table,
                alias: joinItem.as,
                schema: joinItem.db,
                type: 'BASE_TABLE',
                operations: ['JOIN']
              })
            }
          })
        }
      })
    }
  }

  private extractColumns(ast: any): ColumnReference[] {
    const columns: ColumnReference[] = []
    
    if (Array.isArray(ast)) {
      ast.forEach(statement => this.extractColumnsFromStatement(statement, columns))
    } else {
      this.extractColumnsFromStatement(ast, columns)
    }
    
    return columns
  }

  private extractColumnsFromStatement(statement: any, columns: ColumnReference[]): void {
    if (!statement || !statement.columns) return

    statement.columns.forEach((column: any) => {
      if (column.expr && column.expr.type === 'column_ref') {
        columns.push({
          name: column.expr.column,
          table: column.expr.table || '',
          alias: column.as,
          isCalculated: false
        })
      } else if (column.expr && column.expr.type === 'binary_expr') {
        // 处理计算字段
        columns.push({
          name: column.as || 'calculated_field',
          table: '',
          alias: column.as,
          isCalculated: true,
          expression: this.parser.sqlify(column.expr)
        })
      }
    })
  }

  private extractRelations(ast: any, sqlContent: string): Relation[] {
    const relations: Relation[] = []
    
    if (Array.isArray(ast)) {
      ast.forEach(statement => this.extractRelationsFromStatement(statement, relations, sqlContent))
    } else {
      this.extractRelationsFromStatement(ast, relations, sqlContent)
    }
    
    return relations
  }

  private extractRelationsFromStatement(statement: any, relations: Relation[], sqlContent: string): void {
    if (!statement) return

    // 从JOIN条件提取关系
    this.extractJoinRelations(statement, relations, sqlContent)
    
    // 从WHERE条件提取关系
    this.extractWhereRelations(statement, relations, sqlContent)
  }

  private extractJoinRelations(statement: any, relations: Relation[], sqlContent: string): void {
    if (!statement.from) return

    statement.from.forEach((fromItem: any) => {
      if (fromItem.join) {
        fromItem.join.forEach((joinItem: any) => {
          if (joinItem.on) {
            const joinRelation = this.parseJoinCondition(joinItem, sqlContent)
            if (joinRelation) {
              relations.push(joinRelation)
            }
          }
        })
      }
    })
  }

  private extractWhereRelations(statement: any, relations: Relation[], sqlContent: string): void {
    if (!statement.where) return

    const whereRelations = this.parseWhereConditions(statement.where, sqlContent)
    relations.push(...whereRelations)
  }

  private parseJoinCondition(joinItem: any, sqlContent: string): Relation | null {
    if (!joinItem.on || joinItem.on.type !== 'binary_expr') return null

    const condition = joinItem.on
    if (condition.operator === '=' && 
        condition.left.type === 'column_ref' && 
        condition.right.type === 'column_ref') {
      
      return {
        id: this.generateId(),
        from_table: condition.left.table || '',
        from_column: condition.left.column,
        to_table: condition.right.table || '',
        to_column: condition.right.column,
        relation_type: this.mapJoinType(joinItem.join),
        confidence: 0.95, // JOIN关系置信度高
        sql_source: sqlContent,
        join_condition: this.parser.sqlify(condition),
        created_at: new Date()
      }
    }

    return null
  }

  private parseWhereConditions(whereClause: any, sqlContent: string): Relation[] {
    const relations: Relation[] = []
    
    if (whereClause.type === 'binary_expr') {
      if (whereClause.operator === '=' && 
          whereClause.left.type === 'column_ref' && 
          whereClause.right.type === 'column_ref') {
        
        relations.push({
          id: this.generateId(),
          from_table: whereClause.left.table || '',
          from_column: whereClause.left.column,
          to_table: whereClause.right.table || '',
          to_column: whereClause.right.column,
          relation_type: 'WHERE_CONDITION',
          confidence: 0.8, // WHERE条件关系置信度中等
          sql_source: sqlContent,
          created_at: new Date()
        })
      }
    }

    return relations
  }

  private mapJoinType(joinType: string): Relation['relation_type'] {
    switch (joinType?.toUpperCase()) {
      case 'INNER JOIN':
      case 'JOIN':
        return 'INNER_JOIN'
      case 'LEFT JOIN':
        return 'LEFT_JOIN'
      case 'RIGHT JOIN':
        return 'RIGHT_JOIN'
      case 'FULL JOIN':
      case 'FULL OUTER JOIN':
        return 'FULL_JOIN'
      default:
        return 'INNER_JOIN'
    }
  }

  private getOperationType(statement: any): SQLOperation['type'] {
    if (statement.type) {
      switch (statement.type.toLowerCase()) {
        case 'select': return 'SELECT'
        case 'insert': return 'INSERT'
        case 'update': return 'UPDATE'
        case 'delete': return 'DELETE'
        case 'create': return 'CREATE'
        case 'drop': return 'DROP'
        case 'alter': return 'ALTER'
        default: return 'SELECT'
      }
    }
    return 'SELECT'
  }

  private calculateConfidenceScore(relations: Relation[]): number {
    if (relations.length === 0) return 0
    
    const totalConfidence = relations.reduce((sum, rel) => sum + rel.confidence, 0)
    return Math.round((totalConfidence / relations.length) * 100) / 100
  }

  private generateId(): string {
    return `sql_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  // 批量解析多个SQL语句
  async parseBatchSQL(sqlStatements: string[]): Promise<SQLParseResult[]> {
    const results: SQLParseResult[] = []
    
    for (const sql of sqlStatements) {
      const result = await this.parseSQL(sql)
      results.push(result)
    }
    
    return results
  }

  // 获取解析统计信息
  getParsingStats(results: SQLParseResult[]) {
    const successCount = results.filter(r => r.parse_status === 'SUCCESS').length
    const totalRelations = results.reduce((sum, r) => sum + r.extracted_relations.length, 0)
    const avgConfidence = results.reduce((sum, r) => sum + r.confidence_score, 0) / results.length

    return {
      total_parsed: results.length,
      success_rate: (successCount / results.length) * 100,
      total_relations: totalRelations,
      avg_confidence: Math.round(avgConfidence * 100) / 100,
      avg_parsing_time: results.reduce((sum, r) => sum + r.parsing_time_ms, 0) / results.length
    }
  }
}
