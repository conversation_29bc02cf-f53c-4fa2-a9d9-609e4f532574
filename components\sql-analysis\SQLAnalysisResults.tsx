"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON>eader, Card<PERSON>itle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { 
  Database, 
  GitBranch, 
  FileText, 
  TrendingUp, 
  Clock,
  AlertTriangle,
  CheckCircle,
  Info,
  Download,
  Share2,
  Copy
} from "lucide-react"
import type { SQLParseResult, Relation, TableReference, ColumnReference } from "@/types/sql-analysis"

interface SQLAnalysisResultsProps {
  parseResult: SQLParseResult
  onViewGraph?: () => void
  onExport?: () => void
}

export default function SQLAnalysisResults({
  parseResult,
  onViewGraph,
  onExport
}: SQLAnalysisResultsProps) {
  const [activeTab, setActiveTab] = useState("overview")
  const [selectedRelation, setSelectedRelation] = useState<Relation | null>(null)

  // 计算统计信息
  const stats = {
    totalTables: parseResult.extracted_tables.length,
    totalRelations: parseResult.extracted_relations.length,
    totalColumns: parseResult.extracted_columns.length,
    avgConfidence: parseResult.confidence_score,
    joinRelations: parseResult.extracted_relations.filter(r => r.relation_type.includes('JOIN')).length,
    whereRelations: parseResult.extracted_relations.filter(r => r.relation_type === 'WHERE_CONDITION').length
  }

  // 获取关系类型的颜色
  const getRelationTypeColor = (type: string) => {
    const colors = {
      'INNER_JOIN': 'bg-blue-100 text-blue-800',
      'LEFT_JOIN': 'bg-green-100 text-green-800',
      'RIGHT_JOIN': 'bg-red-100 text-red-800',
      'FULL_JOIN': 'bg-purple-100 text-purple-800',
      'WHERE_CONDITION': 'bg-orange-100 text-orange-800'
    }
    return colors[type as keyof typeof colors] || 'bg-gray-100 text-gray-800'
  }

  // 获取置信度等级
  const getConfidenceLevel = (confidence: number) => {
    if (confidence >= 0.9) return { level: '高', color: 'text-green-600', icon: CheckCircle }
    if (confidence >= 0.7) return { level: '中', color: 'text-yellow-600', icon: Info }
    return { level: '低', color: 'text-red-600', icon: AlertTriangle }
  }

  // 复制到剪贴板
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
  }

  return (
    <div className="space-y-6">
      {/* 解析状态提示 */}
      <Alert className={parseResult.parse_status === 'SUCCESS' ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}>
        <div className="flex items-center gap-2">
          {parseResult.parse_status === 'SUCCESS' ? (
            <CheckCircle className="w-4 h-4 text-green-600" />
          ) : (
            <AlertTriangle className="w-4 h-4 text-red-600" />
          )}
          <AlertDescription className={parseResult.parse_status === 'SUCCESS' ? 'text-green-800' : 'text-red-800'}>
            {parseResult.parse_status === 'SUCCESS' 
              ? `SQL解析成功完成，耗时 ${parseResult.parsing_time_ms}ms`
              : 'SQL解析失败，请检查语法或联系管理员'
            }
          </AlertDescription>
        </div>
      </Alert>

      {/* 统计概览卡片 */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4 text-center">
            <Database className="w-8 h-8 text-blue-500 mx-auto mb-2" />
            <div className="text-2xl font-bold text-blue-600">{stats.totalTables}</div>
            <div className="text-sm text-gray-600">数据表</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4 text-center">
            <GitBranch className="w-8 h-8 text-green-500 mx-auto mb-2" />
            <div className="text-2xl font-bold text-green-600">{stats.totalRelations}</div>
            <div className="text-sm text-gray-600">关联关系</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4 text-center">
            <TrendingUp className="w-8 h-8 text-purple-500 mx-auto mb-2" />
            <div className="text-2xl font-bold text-purple-600">
              {Math.round(stats.avgConfidence * 100)}%
            </div>
            <div className="text-sm text-gray-600">平均置信度</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4 text-center">
            <Clock className="w-8 h-8 text-orange-500 mx-auto mb-2" />
            <div className="text-2xl font-bold text-orange-600">{parseResult.parsing_time_ms}ms</div>
            <div className="text-sm text-gray-600">解析耗时</div>
          </CardContent>
        </Card>
      </div>

      {/* 详细结果 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <FileText className="w-5 h-5" />
              解析详细结果
            </div>
            <div className="flex gap-2">
              <Button variant="outline" size="sm" onClick={onViewGraph}>
                <GitBranch className="w-4 h-4 mr-1" />
                查看关系图谱
              </Button>
              <Button variant="outline" size="sm" onClick={onExport}>
                <Download className="w-4 h-4 mr-1" />
                导出结果
              </Button>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="overview">概览</TabsTrigger>
              <TabsTrigger value="tables">数据表</TabsTrigger>
              <TabsTrigger value="relations">关联关系</TabsTrigger>
              <TabsTrigger value="columns">字段信息</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">关系类型分布</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <div className="flex justify-between items-center">
                        <span>JOIN关系</span>
                        <Badge variant="secondary">{stats.joinRelations}</Badge>
                      </div>
                      <div className="flex justify-between items-center">
                        <span>WHERE条件关系</span>
                        <Badge variant="secondary">{stats.whereRelations}</Badge>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">质量评估</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <span>解析状态</span>
                        <Badge variant={parseResult.parse_status === 'SUCCESS' ? 'default' : 'destructive'}>
                          {parseResult.parse_status}
                        </Badge>
                      </div>
                      <div className="flex items-center justify-between">
                        <span>置信度等级</span>
                        <div className="flex items-center gap-1">
                          {(() => {
                            const { level, color, icon: Icon } = getConfidenceLevel(stats.avgConfidence)
                            return (
                              <>
                                <Icon className={`w-4 h-4 ${color}`} />
                                <span className={color}>{level}</span>
                              </>
                            )
                          })()}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="tables" className="space-y-4">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>表名</TableHead>
                    <TableHead>别名</TableHead>
                    <TableHead>模式</TableHead>
                    <TableHead>类型</TableHead>
                    <TableHead>操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {parseResult.extracted_tables.map((table, index) => (
                    <TableRow key={index}>
                      <TableCell className="font-mono font-medium">{table.name}</TableCell>
                      <TableCell>{table.alias || '-'}</TableCell>
                      <TableCell>{table.schema || '-'}</TableCell>
                      <TableCell>
                        <Badge variant="outline">{table.type}</Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex gap-1 flex-wrap">
                          {table.operations.map((op, i) => (
                            <Badge key={i} variant="secondary" className="text-xs">
                              {op}
                            </Badge>
                          ))}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TabsContent>

            <TabsContent value="relations" className="space-y-4">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>源表.字段</TableHead>
                    <TableHead>目标表.字段</TableHead>
                    <TableHead>关系类型</TableHead>
                    <TableHead>置信度</TableHead>
                    <TableHead>JOIN条件</TableHead>
                    <TableHead>操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {parseResult.extracted_relations.map((relation) => {
                    const { level, color, icon: Icon } = getConfidenceLevel(relation.confidence)
                    return (
                      <TableRow 
                        key={relation.id}
                        className={selectedRelation?.id === relation.id ? 'bg-blue-50' : ''}
                      >
                        <TableCell className="font-mono">
                          {relation.from_table}.{relation.from_column}
                        </TableCell>
                        <TableCell className="font-mono">
                          {relation.to_table}.{relation.to_column}
                        </TableCell>
                        <TableCell>
                          <Badge className={getRelationTypeColor(relation.relation_type)}>
                            {relation.relation_type}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Icon className={`w-4 h-4 ${color}`} />
                            <span className="text-sm">
                              {Math.round(relation.confidence * 100)}%
                            </span>
                          </div>
                        </TableCell>
                        <TableCell className="font-mono text-xs max-w-48 truncate">
                          {relation.join_condition || '-'}
                        </TableCell>
                        <TableCell>
                          <div className="flex gap-1">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => setSelectedRelation(relation)}
                            >
                              <Info className="w-3 h-3" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => copyToClipboard(
                                `${relation.from_table}.${relation.from_column} -> ${relation.to_table}.${relation.to_column}`
                              )}
                            >
                              <Copy className="w-3 h-3" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    )
                  })}
                </TableBody>
              </Table>
            </TabsContent>

            <TabsContent value="columns" className="space-y-4">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>字段名</TableHead>
                    <TableHead>所属表</TableHead>
                    <TableHead>别名</TableHead>
                    <TableHead>数据类型</TableHead>
                    <TableHead>是否计算字段</TableHead>
                    <TableHead>表达式</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {parseResult.extracted_columns.map((column, index) => (
                    <TableRow key={index}>
                      <TableCell className="font-mono font-medium">{column.name}</TableCell>
                      <TableCell className="font-mono">{column.table}</TableCell>
                      <TableCell>{column.alias || '-'}</TableCell>
                      <TableCell>
                        {column.dataType ? (
                          <Badge variant="outline">{column.dataType}</Badge>
                        ) : '-'}
                      </TableCell>
                      <TableCell>
                        <Badge variant={column.isCalculated ? 'default' : 'secondary'}>
                          {column.isCalculated ? '是' : '否'}
                        </Badge>
                      </TableCell>
                      <TableCell className="font-mono text-xs max-w-48 truncate">
                        {column.expression || '-'}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* 选中关系的详细信息 */}
      {selectedRelation && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              关系详细信息
              <Button variant="ghost" size="sm" onClick={() => setSelectedRelation(null)}>
                ×
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium text-gray-600">源表字段</label>
                <div className="font-mono text-lg">
                  {selectedRelation.from_table}.{selectedRelation.from_column}
                </div>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-600">目标表字段</label>
                <div className="font-mono text-lg">
                  {selectedRelation.to_table}.{selectedRelation.to_column}
                </div>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-600">关系类型</label>
                <div>
                  <Badge className={getRelationTypeColor(selectedRelation.relation_type)}>
                    {selectedRelation.relation_type}
                  </Badge>
                </div>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-600">置信度</label>
                <div className="text-lg font-semibold">
                  {Math.round(selectedRelation.confidence * 100)}%
                </div>
              </div>
              {selectedRelation.join_condition && (
                <div className="col-span-2">
                  <label className="text-sm font-medium text-gray-600">JOIN条件</label>
                  <div className="font-mono bg-gray-100 p-2 rounded text-sm">
                    {selectedRelation.join_condition}
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
