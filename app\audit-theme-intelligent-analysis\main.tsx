"use client"

import { useState, useEffect, useMemo } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Checkbox } from "@/components/ui/checkbox"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Progress } from "@/components/ui/progress"
import { Separator } from "@/components/ui/separator"
import { Textarea } from "@/components/ui/textarea"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import {
  Search, Plus, RefreshCw, Link as LinkIcon, Unlink, AlertTriangle, CheckCircle, Clock, FileText, Target, Settings, ArrowRight, Filter, Eye, Edit, TrendingUp, AlertCircle, ExternalLink, MessageSquare, BookOpen, Zap
} from "lucide-react"

// 类型定义（合并两页所有类型）
export interface AuditTheme {
  id: string
  name: string
  description: string
  category: string
  priority: 'high' | 'medium' | 'low'
  status: 'active' | 'inactive' | 'draft'
  createdAt: string
  updatedAt: string
  associatedElementsCount: number
}

export interface AuditElement {
  id: string
  name: string
  type: 'risk_point' | 'policy_basis' | 'control_measure' | 'compliance_requirement'
  description: string
  source: string
  status: 'active' | 'inactive' | 'pending'
  associatedThemesCount: number
  lastUpdated: string
}

export interface Association {
  id: string
  themeId: string
  elementId: string
  themeName: string
  elementName: string
  elementType: string
  associationDate: string
  strength: 'strong' | 'medium' | 'weak'
  notes?: string
}

export interface UpdateSuggestion {
  type: 'new_element' | 'updated_element' | 'outdated_element' | 'removed_element'
  element: AuditElement
  reason: string
  confidence: number
  action: 'add' | 'update' | 'remove' | 'review'
}

export interface NewlyIdentifiedElement {
  id: string
  name: string
  type: 'Entity' | 'Attribute' | 'Event' | 'Relationship'
  description: string
  sourcePolicy: string
  extractionDate: string
  confidence: number
  suggestedActions: string[]
  originalText: string
}

export interface UncoveredElement {
  id: string
  name: string
  type: 'Entity' | 'Attribute' | 'Event' | 'Relationship'
  description: string
  sourcePolicy: string
  currentCoverage: string
  riskLevel: 'High' | 'Medium' | 'Low'
  suggestedThemes: string[]
  originalText: string
}

export interface PolicyInconsistency {
  id: string
  elementName: string
  elementType: 'Entity' | 'Attribute' | 'Event' | 'Relationship'
  currentDefinition: string
  newDefinition: string
  sourcePolicy: string
  changeType: 'Definition' | 'Threshold' | 'Criteria' | 'Scope'
  impactLevel: 'High' | 'Medium' | 'Low'
  affectedThemes: string[]
  suggestedAction: 'Adopt' | 'Review' | 'Reject'
  originalText: string
}

export default function AuditThemeIntelligentAnalysis() {
  // 主题、要素、关联关系等状态
  const [themes, setThemes] = useState<AuditTheme[]>([])
  const [elements, setElements] = useState<AuditElement[]>([])
  const [associations, setAssociations] = useState<Association[]>([])
  const [selectedTheme, setSelectedTheme] = useState<AuditTheme | null>(null)
  const [selectedElement, setSelectedElement] = useState<AuditElement | null>(null)
  const [selectedElements, setSelectedElements] = useState<string[]>([])
  const [isAssociationDialogOpen, setIsAssociationDialogOpen] = useState(false)
  const [isThemeDetailOpen, setIsThemeDetailOpen] = useState(false)
  const [isElementDetailOpen, setIsElementDetailOpen] = useState(false)
  const [activeTab, setActiveTab] = useState<'themes' | 'elements' | 'associations' | 'intelligence'>('themes')
  const [activeIntelligenceTab, setActiveIntelligenceTab] = useState<'newly-identified' | 'uncovered' | 'inconsistencies'>('newly-identified')
  const [selectedIntelligence, setSelectedIntelligence] = useState<any>(null)
  const [isIntelligenceDetailOpen, setIsIntelligenceDetailOpen] = useState(false)

  // 模拟数据初始化
  useEffect(() => {
    const mockThemes: AuditTheme[] = [
      {
        id: "1",
        name: "电价政策合规性稽查",
        description: "检查电价政策执行情况，确保符合国家规定",
        category: "价格管理",
        priority: "high",
        status: "active",
        createdAt: "2024-01-15",
        updatedAt: "2024-01-20",
        associatedElementsCount: 8
      },
      {
        id: "2",
        name: "用电量异常稽查",
        description: "识别用电量异常波动，发现潜在违规行为",
        category: "用电管理",
        priority: "medium",
        status: "active",
        createdAt: "2024-01-10",
        updatedAt: "2024-01-18",
        associatedElementsCount: 12
      },
      {
        id: "3",
        name: "客户信息真实性稽查",
        description: "验证客户信息的真实性和完整性",
        category: "客户管理",
        priority: "high",
        status: "active",
        createdAt: "2024-01-12",
        updatedAt: "2024-01-19",
        associatedElementsCount: 6
      }
    ]
    const mockElements: AuditElement[] = [
      {
        id: "1",
        name: "电价政策文件",
        type: "policy_basis",
        description: "国家发改委发布的电价政策文件",
        source: "发改委官网",
        status: "active",
        associatedThemesCount: 3,
        lastUpdated: "2024-01-20"
      },
      {
        id: "2",
        name: "用电量异常阈值",
        type: "risk_point",
        description: "用电量异常判断的阈值标准",
        source: "历史数据分析",
        status: "active",
        associatedThemesCount: 2,
        lastUpdated: "2024-01-18"
      },
      {
        id: "3",
        name: "客户身份验证要求",
        type: "compliance_requirement",
        description: "客户身份信息验证的合规要求",
        source: "监管规定",
        status: "active",
        associatedThemesCount: 4,
        lastUpdated: "2024-01-19"
      }
    ]
    const mockAssociations: Association[] = [
      {
        id: "1",
        themeId: "1",
        elementId: "1",
        themeName: "电价政策合规性稽查",
        elementName: "电价政策文件",
        elementType: "policy_basis",
        associationDate: "2024-01-15",
        strength: "strong"
      },
      {
        id: "2",
        themeId: "2",
        elementId: "2",
        themeName: "用电量异常稽查",
        elementName: "用电量异常阈值",
        elementType: "risk_point",
        associationDate: "2024-01-10",
        strength: "strong"
      }
    ]
    setThemes(mockThemes)
    setElements(mockElements)
    setAssociations(mockAssociations)
  }, [])

  // 获取主题的关联要素
  const getThemeElements = (themeId: string) => {
    return associations
      .filter(assoc => assoc.themeId === themeId)
      .map(assoc => {
        const element = elements.find(el => el.id === assoc.elementId)
        return { ...assoc, element }
      })
      .filter(item => item.element)
  }

  // 获取要素的关联主题
  const getElementThemes = (elementId: string) => {
    return associations
      .filter(assoc => assoc.elementId === elementId)
      .map(assoc => {
        const theme = themes.find(th => th.id === assoc.themeId)
        return { ...assoc, theme }
      })
      .filter(item => item.theme)
  }

  // 绑定要素
  const addElementAssociation = (themeId: string, elementIds: string[]) => {
    const newAssociations = elementIds.map(elementId => {
      const theme = themes.find(t => t.id === themeId)
      const element = elements.find(e => e.id === elementId)
      return {
        id: `${themeId}-${elementId}-${Date.now()}`,
        themeId,
        elementId,
        themeName: theme?.name || "",
        elementName: element?.name || "",
        elementType: element?.type || "risk_point",
        associationDate: new Date().toISOString().split('T')[0],
        strength: "medium" as const
      }
    })
    setAssociations([...associations, ...newAssociations])
    setIsAssociationDialogOpen(false)
    setSelectedElements([])
  }

  // 解绑要素
  const removeElementAssociation = (associationId: string) => {
    setAssociations(associations.filter(assoc => assoc.id !== associationId))
  }

  // 智能分析 mock 数据
  const mockNewlyIdentifiedElements: NewlyIdentifiedElement[] = [
    {
      id: "NIE001",
      name: "分布式能源客户",
      type: "Entity",
      description: "使用分布式能源系统的客户，包括太阳能、风能等可再生能源用户",
      sourcePolicy: "新能源政策文件2024-01",
      extractionDate: "2024-01-20",
      confidence: 0.95,
      suggestedActions: ["创建新的稽查主题", "关联现有客户主题", "设置专项监控规则"],
      originalText: "分布式能源客户是指使用分布式能源系统的客户，包括太阳能、风能等可再生能源用户。"
    }
  ]
  const mockUncoveredElements: UncoveredElement[] = [
    {
      id: "UE001",
      name: "虚拟电厂参与度",
      type: "Attribute",
      description: "客户参与虚拟电厂项目的程度和贡献度",
      sourcePolicy: "虚拟电厂试点政策2024-04",
      currentCoverage: "无相关稽查主题覆盖",
      riskLevel: "High",
      suggestedThemes: ["虚拟电厂稽查", "新能源参与稽查", "电网互动稽查"],
      originalText: "虚拟电厂参与度是指客户参与虚拟电厂项目的程度和贡献度。"
    }
  ]
  const mockPolicyInconsistencies: PolicyInconsistency[] = [
    {
      id: "PI001",
      elementName: "高耗能客户阈值",
      elementType: "Attribute",
      currentDefinition: "年用电量超过1000万千瓦时的客户",
      newDefinition: "年用电量超过800万千瓦时的客户",
      sourcePolicy: "节能降耗政策2024-07",
      changeType: "Threshold",
      impactLevel: "High",
      affectedThemes: ["高耗能客户稽查", "节能稽查", "负荷管理稽查"],
      suggestedAction: "Adopt",
      originalText: "高耗能客户是指年用电量超过800万千瓦时的客户。"
    }
  ]

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* <h1 className="text-3xl font-bold">稽查主题要素智能化分析</h1>
      <p className="text-gray-600 mt-2">本页面整合了要素关联与智能分析的全部功能。</p> */}
      <Tabs value={activeTab} onValueChange={v => setActiveTab(v as any)} className="space-y-4">
        <TabsList>
          <TabsTrigger value="themes">稽查主题</TabsTrigger>
          <TabsTrigger value="elements">稽查要素</TabsTrigger>
          <TabsTrigger value="associations">关联关系管理</TabsTrigger>
          <TabsTrigger value="intelligence">智能分析</TabsTrigger>
        </TabsList>
        <TabsContent value="themes">
          <Card>
            <CardHeader>
              <CardTitle>稽查主题列表</CardTitle>
              <CardDescription>查看所有稽查主题及其关联的稽查要素</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4">
                {themes.map((theme) => (
                  <Card key={theme.id} className="cursor-pointer hover:shadow-md transition-shadow"
                        onClick={() => { setSelectedTheme(theme); setIsThemeDetailOpen(true); }}>
                    <CardContent className="p-4">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                            <h3 className="font-semibold">{theme.name}</h3>
                            <Badge>{theme.priority === 'high' ? '高优先级' : theme.priority === 'medium' ? '中优先级' : '低优先级'}</Badge>
                            <Badge variant={theme.status === 'active' ? 'default' : 'secondary'}>{theme.status === 'active' ? '活跃' : theme.status === 'inactive' ? '非活跃' : '草稿'}</Badge>
                          </div>
                          <p className="text-sm text-gray-600 mb-2">{theme.description}</p>
                          <div className="flex items-center gap-4 text-sm text-gray-500">
                            <span>分类: {theme.category}</span>
                            <span>关联要素: {theme.associatedElementsCount} 个</span>
                            <span>更新时间: {theme.updatedAt}</span>
                          </div>
                        </div>
                        <Button size="sm" variant="outline" onClick={e => { e.stopPropagation(); setIsAssociationDialogOpen(true); setSelectedTheme(theme); }}>绑定/解绑要素</Button>
                      </div>
                      {/* 展示已关联要素 */}
                      <div className="mt-4">
                        <div className="font-medium mb-2">已关联要素：</div>
                        <div className="flex flex-wrap gap-2">
                          {getThemeElements(theme.id).map(({ element, id }) => (
                            <Badge key={id} className="flex items-center gap-1 cursor-pointer" onClick={e => { e.stopPropagation(); setSelectedElement(element ?? null); setIsElementDetailOpen(true); }}>
                              {element?.name}
                              <Button size="icon" variant="ghost" className="p-0 ml-1" onClick={e => { e.stopPropagation(); removeElementAssociation(id); }}><Unlink className="w-3 h-3" /></Button>
                            </Badge>
                          ))}
                          {getThemeElements(theme.id).length === 0 && <span className="text-gray-400">暂无关联要素</span>}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="elements">
          <Card>
            <CardHeader>
              <CardTitle>稽查要素列表</CardTitle>
              <CardDescription>查看所有稽查要素及其关联的稽查主题</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4">
                {elements.map((el) => (
                  <Card key={el.id} className="cursor-pointer hover:shadow-md transition-shadow"
                        onClick={() => { setSelectedElement(el); setIsElementDetailOpen(true); }}>
                    <CardContent className="p-4">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                            <h3 className="font-semibold">{el.name}</h3>
                            <Badge>{el.type}</Badge>
                            <Badge variant={el.status === 'active' ? 'default' : 'secondary'}>{el.status === 'active' ? '活跃' : el.status === 'inactive' ? '非活跃' : '待处理'}</Badge>
                          </div>
                          <p className="text-sm text-gray-600 mb-2">{el.description}</p>
                          <div className="flex items-center gap-4 text-sm text-gray-500">
                            <span>来源: {el.source}</span>
                            <span>关联主题: {el.associatedThemesCount} 个</span>
                            <span>更新时间: {el.lastUpdated}</span>
                          </div>
                        </div>
                      </div>
                      {/* 展示已关联主题 */}
                      <div className="mt-4">
                        <div className="font-medium mb-2">已关联主题：</div>
                        <div className="flex flex-wrap gap-2">
                          {getElementThemes(el.id).map(({ theme, id }) => (
                            <Badge key={id} className="flex items-center gap-1 cursor-pointer" onClick={e => { e.stopPropagation(); setSelectedTheme(theme ?? null); setIsThemeDetailOpen(true); }}>
                              {theme?.name}
                            </Badge>
                          ))}
                          {getElementThemes(el.id).length === 0 && <span className="text-gray-400">暂无关联主题</span>}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="associations">
          <Card>
            <CardHeader>
              <CardTitle>关联关系管理</CardTitle>
              <CardDescription>管理稽查主题与稽查要素之间的关联关系</CardDescription>
            </CardHeader>
            <CardContent>
              {/* 关联关系管理功能实现 */}
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="intelligence">
          <Card>
            <CardHeader>
              <CardTitle>智能分析</CardTitle>
              <CardDescription>基于智能算法自动识别的新要素、未覆盖要素及政策不一致</CardDescription>
            </CardHeader>
            <CardContent>
              {/* 统计卡片区 start */}
              <div className="grid grid-cols-4 gap-4 mb-6">
                <Card className="flex flex-col items-center justify-center py-4">
                  <div className="text-gray-500 text-sm mb-1">新识别要素</div>
                  <div className="text-2xl font-bold text-blue-600">{mockNewlyIdentifiedElements.length}</div>
                </Card>
                <Card className="flex flex-col items-center justify-center py-4">
                  <div className="text-gray-500 text-sm mb-1">未覆盖要素</div>
                  <div className="text-2xl font-bold text-orange-500 flex items-center gap-1">
                    {mockUncoveredElements.length}
                    <AlertTriangle className="w-5 h-5 text-orange-400" />
                  </div>
                </Card>
                <Card className="flex flex-col items-center justify-center py-4">
                  <div className="text-gray-500 text-sm mb-1">政策不一致</div>
                  <div className="text-2xl font-bold text-red-500 flex items-center gap-1">
                    {mockPolicyInconsistencies.length}
                    <AlertCircle className="w-5 h-5 text-red-400" />
                  </div>
                </Card>
                <Card className="flex flex-col items-center justify-center py-4">
                  <div className="text-gray-500 text-sm mb-1">平均置信度</div>
                  <div className="text-2xl font-bold text-green-600 flex items-center gap-1">
                    {mockNewlyIdentifiedElements.length > 0 ? `${(mockNewlyIdentifiedElements.reduce((sum, e) => sum + e.confidence, 0) / mockNewlyIdentifiedElements.length * 100).toFixed(1)}%` : '--'}
                    <TrendingUp className="w-5 h-5 text-green-400" />
                  </div>
                </Card>
              </div>
              {/* 统计卡片区 end */}
              <Tabs value={activeIntelligenceTab} onValueChange={v => setActiveIntelligenceTab(v as any)} className="space-y-2">
                <TabsList>
                  <TabsTrigger value="newly-identified">新识别要素</TabsTrigger>
                  <TabsTrigger value="uncovered">未覆盖要素</TabsTrigger>
                  <TabsTrigger value="inconsistencies">政策不一致</TabsTrigger>
                </TabsList>
                <TabsContent value="newly-identified">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>要素名称</TableHead>
                        <TableHead>类型</TableHead>
                        <TableHead>来源政策</TableHead>
                        <TableHead>置信度</TableHead>
                        <TableHead>建议操作</TableHead>
                        <TableHead>操作</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {mockNewlyIdentifiedElements.map(element => (
                        <TableRow key={element.id}>
                          <TableCell>{element.name}</TableCell>
                          <TableCell>{element.type}</TableCell>
                          <TableCell>{element.sourcePolicy}</TableCell>
                          <TableCell>{(element.confidence * 100).toFixed(0)}%</TableCell>
                          <TableCell>{element.suggestedActions.join('、')}</TableCell>
                          <TableCell>
                            <Button size="sm" variant="ghost" onClick={() => { setSelectedIntelligence(element); setIsIntelligenceDetailOpen(true); }}>详情</Button>
                          </TableCell>
                        </TableRow>
                      ))}
                      {mockNewlyIdentifiedElements.length === 0 && <TableRow><TableCell colSpan={6} className="text-center text-gray-400">暂无数据</TableCell></TableRow>}
                    </TableBody>
                  </Table>
                </TabsContent>
                <TabsContent value="uncovered">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>要素名称</TableHead>
                        <TableHead>类型</TableHead>
                        <TableHead>当前覆盖情况</TableHead>
                        <TableHead>风险等级</TableHead>
                        <TableHead>建议主题</TableHead>
                        <TableHead>操作</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {mockUncoveredElements.map(element => (
                        <TableRow key={element.id}>
                          <TableCell>{element.name}</TableCell>
                          <TableCell>{element.type}</TableCell>
                          <TableCell>{element.currentCoverage}</TableCell>
                          <TableCell>{element.riskLevel}</TableCell>
                          <TableCell>{element.suggestedThemes.join('、')}</TableCell>
                          <TableCell>
                            <Button size="sm" variant="ghost" onClick={() => { setSelectedIntelligence(element); setIsIntelligenceDetailOpen(true); }}>详情</Button>
                          </TableCell>
                        </TableRow>
                      ))}
                      {mockUncoveredElements.length === 0 && <TableRow><TableCell colSpan={6} className="text-center text-gray-400">暂无数据</TableCell></TableRow>}
                    </TableBody>
                  </Table>
                </TabsContent>
                <TabsContent value="inconsistencies">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>要素名称</TableHead>
                        <TableHead>类型</TableHead>
                        <TableHead>当前定义</TableHead>
                        <TableHead>新定义</TableHead>
                        <TableHead>影响主题</TableHead>
                        <TableHead>操作</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {mockPolicyInconsistencies.map(item => (
                        <TableRow key={item.id}>
                          <TableCell>{item.elementName}</TableCell>
                          <TableCell>{item.elementType}</TableCell>
                          <TableCell>{item.currentDefinition}</TableCell>
                          <TableCell>{item.newDefinition}</TableCell>
                          <TableCell>{item.affectedThemes.join('、')}</TableCell>
                          <TableCell>
                            <Button size="sm" variant="ghost" onClick={() => { setSelectedIntelligence(item); setIsIntelligenceDetailOpen(true); }}>详情</Button>
                          </TableCell>
                        </TableRow>
                      ))}
                      {mockPolicyInconsistencies.length === 0 && <TableRow><TableCell colSpan={6} className="text-center text-gray-400">暂无数据</TableCell></TableRow>}
                    </TableBody>
                  </Table>
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
          {/* 智能分析详情弹窗 */}
          <Dialog open={isIntelligenceDetailOpen} onOpenChange={setIsIntelligenceDetailOpen}>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>智能分析详情</DialogTitle>
              </DialogHeader>
              {selectedIntelligence && (
                <div className="space-y-3 text-sm">
                  {selectedIntelligence.name && (
                    <>
                      <div><b>要素名称：</b>{selectedIntelligence.name}</div>
                      <div><b>类型：</b>{selectedIntelligence.type}</div>
                      <div><b>描述：</b>{selectedIntelligence.description}</div>
                      <div><b>来源政策：</b>{selectedIntelligence.sourcePolicy}</div>
                      {selectedIntelligence.extractionDate && <div><b>抽取日期：</b>{selectedIntelligence.extractionDate}</div>}
                      {selectedIntelligence.confidence !== undefined && <div><b>置信度：</b>{(selectedIntelligence.confidence * 100).toFixed(1)}%</div>}
                      {selectedIntelligence.suggestedActions && <div><b>建议操作：</b>{selectedIntelligence.suggestedActions.join('、')}</div>}
                      {selectedIntelligence.originalText && <div><b>原文片段：</b>{selectedIntelligence.originalText}</div>}
                    </>
                  )}
                  {selectedIntelligence.currentCoverage && (
                    <>
                      <div><b>要素名称：</b>{selectedIntelligence.name}</div>
                      <div><b>类型：</b>{selectedIntelligence.type}</div>
                      <div><b>描述：</b>{selectedIntelligence.description}</div>
                      <div><b>来源政策：</b>{selectedIntelligence.sourcePolicy}</div>
                      <div><b>当前覆盖情况：</b>{selectedIntelligence.currentCoverage}</div>
                      <div><b>风险等级：</b>{selectedIntelligence.riskLevel}</div>
                      <div><b>建议主题：</b>{selectedIntelligence.suggestedThemes.join('、')}</div>
                      {selectedIntelligence.originalText && <div><b>原文片段：</b>{selectedIntelligence.originalText}</div>}
                    </>
                  )}
                  {selectedIntelligence.currentDefinition && (
                    <>
                      <div><b>要素名称：</b>{selectedIntelligence.elementName}</div>
                      <div><b>类型：</b>{selectedIntelligence.elementType}</div>
                      <div><b>当前定义：</b>{selectedIntelligence.currentDefinition}</div>
                      <div><b>新定义：</b>{selectedIntelligence.newDefinition}</div>
                      <div><b>来源政策：</b>{selectedIntelligence.sourcePolicy}</div>
                      <div><b>变更类型：</b>{selectedIntelligence.changeType}</div>
                      <div><b>影响等级：</b>{selectedIntelligence.impactLevel}</div>
                      <div><b>影响主题：</b>{selectedIntelligence.affectedThemes.join('、')}</div>
                      <div><b>建议操作：</b>{selectedIntelligence.suggestedAction}</div>
                      {selectedIntelligence.originalText && <div><b>原文片段：</b>{selectedIntelligence.originalText}</div>}
                    </>
                  )}
                </div>
              )}
            </DialogContent>
          </Dialog>
        </TabsContent>
      </Tabs>
      {/* 绑定要素弹窗 */}
      <Dialog open={isAssociationDialogOpen} onOpenChange={setIsAssociationDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>绑定要素到主题：{selectedTheme?.name}</DialogTitle>
            <DialogDescription>请选择要绑定的要素</DialogDescription>
          </DialogHeader>
          <div className="space-y-2">
            {elements.map(el => (
              <div key={el.id} className="flex items-center gap-2">
                <Checkbox checked={selectedElements.includes(el.id)} onCheckedChange={checked => {
                  setSelectedElements(checked ? [...selectedElements, el.id] : selectedElements.filter(id => id !== el.id))
                }} />
                <span>{el.name}</span>
              </div>
            ))}
          </div>
          <DialogFooter>
            <Button onClick={() => addElementAssociation(selectedTheme?.id || '', selectedElements)} disabled={!selectedTheme || selectedElements.length === 0}>确认绑定</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      {/* 主题详情弹窗 */}
      <Dialog open={isThemeDetailOpen} onOpenChange={setIsThemeDetailOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>稽查主题详情</DialogTitle>
          </DialogHeader>
          {selectedTheme && (
            <div className="space-y-2">
              <div><b>名称：</b>{selectedTheme.name}</div>
              <div><b>描述：</b>{selectedTheme.description}</div>
              <div><b>分类：</b>{selectedTheme.category}</div>
              <div><b>优先级：</b>{selectedTheme.priority}</div>
              <div><b>状态：</b>{selectedTheme.status}</div>
              <div><b>创建时间：</b>{selectedTheme.createdAt}</div>
              <div><b>更新时间：</b>{selectedTheme.updatedAt}</div>
              <div><b>已关联要素：</b>{getThemeElements(selectedTheme.id).map(({ element }) => element?.name).join('、') || '无'}</div>
            </div>
          )}
        </DialogContent>
      </Dialog>
      {/* 要素详情弹窗 */}
      <Dialog open={isElementDetailOpen} onOpenChange={setIsElementDetailOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>稽查要素详情</DialogTitle>
          </DialogHeader>
          {selectedElement && (
            <div className="space-y-2">
              <div><b>名称：</b>{selectedElement.name}</div>
              <div><b>类型：</b>{selectedElement.type}</div>
              <div><b>描述：</b>{selectedElement.description}</div>
              <div><b>来源：</b>{selectedElement.source}</div>
              <div><b>状态：</b>{selectedElement.status}</div>
              <div><b>更新时间：</b>{selectedElement.lastUpdated}</div>
              <div><b>已关联主题：</b>{getElementThemes(selectedElement.id).map(({ theme }) => theme?.name).join('、') || '无'}</div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
} 