'use client'

import React, { useState } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Badge } from '@/components/ui/badge'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Progress } from '@/components/ui/progress'
import { <PERSON><PERSON>, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs'
import {
  Upload,
  FileText,
  Search,
  Eye,
  Download,
  Wand2,
  CheckCircle,
  AlertCircle,
  Loader2,
  File,
  FileType,
  FileSpreadsheet,
  Trash2,
  Plus,
  ArrowRight,
} from 'lucide-react'
import { useRouter } from 'next/navigation'

interface PolicyFile {
  id: string
  name: string
  type: string
  size: string
  uploadTime: string
  status: 'uploading' | 'processing' | 'completed' | 'error'
  progress: number
}

interface ExtractedElement {
  id: string
  element: string
  type: 'condition' | 'threshold' | 'dimension' | 'measure'
  confidence: number
  source: string
  description: string
}

interface RuleTemplate {
  id: string
  name: string
  source: string
  elements: string[]
  status: 'draft' | 'generated' | 'applied'
  description: string
}

export default function PolicyFileProcessingPage() {
  const router = useRouter()
  const [activeTab, setActiveTab] = useState('upload')
  const [uploadedFiles, setUploadedFiles] = useState<PolicyFile[]>([])
  const [extractedElements, setExtractedElements] = useState<
    ExtractedElement[]
  >([])
  const [ruleTemplates, setRuleTemplates] = useState<RuleTemplate[]>([])
  const [selectedFile, setSelectedFile] = useState<PolicyFile | null>(null)
  const [isExtracting, setIsExtracting] = useState(false)
  const [isGenerating, setIsGenerating] = useState(false)
  const [showElementDetail, setShowElementDetail] = useState(false)
  const [selectedElement, setSelectedElement] =
    useState<ExtractedElement | null>(null)

  // 模拟文件上传
  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files
    if (!files) return

    Array.from(files).forEach((file) => {
      const newFile: PolicyFile = {
        id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
        name: file.name,
        type: file.type,
        size: (file.size / 1024 / 1024).toFixed(2) + ' MB',
        uploadTime: new Date().toLocaleString(),
        status: 'uploading',
        progress: 0,
      }

      setUploadedFiles((prev) => [...prev, newFile])

      // 模拟上传进度
      const interval = setInterval(() => {
        setUploadedFiles((prev) =>
          prev.map((f) => {
            if (f.id === newFile.id) {
              const newProgress = Math.min(f.progress + 10, 100)
              const newStatus = newProgress === 100 ? 'completed' : 'uploading'
              return { ...f, progress: newProgress, status: newStatus }
            }
            return f
          })
        )

        if (newFile.progress >= 100) {
          clearInterval(interval)
        }
      }, 200)
    })
  }

  // 提取稽查要素
  const handleExtractElements = async (file: PolicyFile) => {
    setIsExtracting(true)
    setSelectedFile(file)

    // 模拟AI提取过程
    await new Promise((resolve) => setTimeout(resolve, 3000))

    const mockElements: ExtractedElement[] = [
      {
        id: '1',
        element: '高额返现',
        type: 'condition',
        confidence: 0.95,
        source: file.name,
        description: '营销活动中返现金额超过规定标准的违规行为',
      },
      {
        id: '2',
        element: '违规返点',
        type: 'condition',
        confidence: 0.88,
        source: file.name,
        description: '向客户提供超出政策允许范围的返点优惠',
      },
      {
        id: '3',
        element: '5000元',
        type: 'threshold',
        confidence: 0.92,
        source: file.name,
        description: '单笔交易返现金额阈值',
      },
      {
        id: '4',
        element: '渠道返利',
        type: 'dimension',
        confidence: 0.85,
        source: file.name,
        description: '通过渠道商进行的返利活动',
      },
    ]

    setExtractedElements(mockElements)
    setIsExtracting(false)
    setActiveTab('elements')
  }

  // 生成规则池
  const handleGenerateRulePool = async () => {
    setIsGenerating(true)

    // 模拟规则生成过程
    await new Promise((resolve) => setTimeout(resolve, 2000))

    const mockTemplates: RuleTemplate[] = [
      {
        id: '1',
        name: '高危营销活动稽查',
        source: selectedFile?.name || '',
        elements: ['高额返现', '违规返点', '5000元'],
        status: 'generated',
        description: '识别营销活动中存在高额返现和违规返点的风险行为',
      },
      {
        id: '2',
        name: '渠道返利稽查',
        source: selectedFile?.name || '',
        elements: ['渠道返利', '违规返点'],
        status: 'generated',
        description: '监控渠道商返利活动的合规性',
      },
    ]

    setRuleTemplates(mockTemplates)
    setIsGenerating(false)
    setActiveTab('rules')
  }

  // 转换为规则
  const handleConvertToRule = (template: RuleTemplate) => {
    // 跳转到规则生成页面，携带模板信息
    router.push(
      `/rule-generator?templateId=${template.id}&source=${encodeURIComponent(
        template.source
      )}`
    )
  }

  // 查看要素详情
  const handleViewElementDetail = (element: ExtractedElement) => {
    setSelectedElement(element)
    setShowElementDetail(true)
  }

  // 获取文件图标
  const getFileIcon = (type: string) => {
    if (type.includes('pdf'))
      return <FileText className="h-5 w-5 text-red-500" />
    if (type.includes('word') || type.includes('document'))
      return <File className="h-5 w-5 text-blue-500" />
    if (type.includes('excel') || type.includes('spreadsheet'))
      return <FileSpreadsheet className="h-5 w-5 text-green-500" />
    return <FileType className="h-5 w-5 text-gray-500" />
  }

  // 获取状态徽章
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'uploading':
        return (
          <Badge className="bg-blue-100 text-blue-800 border-blue-300">
            上传中
          </Badge>
        )
      case 'processing':
        return (
          <Badge className="bg-yellow-100 text-yellow-800 border-yellow-300">
            处理中
          </Badge>
        )
      case 'completed':
        return (
          <Badge className="bg-green-100 text-green-800 border-green-300">
            已完成
          </Badge>
        )
      case 'error':
        return (
          <Badge className="bg-red-100 text-red-800 border-red-300">错误</Badge>
        )
      default:
        return <Badge variant="secondary">{status}</Badge>
    }
  }

  return (
    <div className="flex-1 p-6 space-y-6">
      {/* Page Header */}
      <div className="space-y-2">
        <h1 className="text-3xl font-bold tracking-tight text-gray-900">
          政策文件处理与稽查要素提取
        </h1>
        <p className="text-gray-600">
          上传政策文件，智能提取稽查要素，生成规则模板
        </p>
      </div>

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="upload" className="flex items-center gap-2">
            <Upload className="h-4 w-4" />
            文件上传
          </TabsTrigger>
          <TabsTrigger value="elements" className="flex items-center gap-2">
            <Search className="h-4 w-4" />
            要素提取
          </TabsTrigger>
          <TabsTrigger value="rules" className="flex items-center gap-2">
            <Wand2 className="h-4 w-4" />
            规则池生成
          </TabsTrigger>
        </TabsList>

        {/* 文件上传 Tab */}
        <TabsContent value="upload" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Upload className="h-5 w-5" />
                上传政策文件
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
                <Upload className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                <div className="space-y-2">
                  <p className="text-lg font-medium text-gray-900">
                    选择文件上传
                  </p>
                  <p className="text-sm text-gray-500">
                    支持 PDF、DOC、DOCX 格式的政策文件
                  </p>
                  <div className="mt-4">
                    <Input
                      type="file"
                      multiple
                      accept=".pdf,.doc,.docx"
                      onChange={handleFileUpload}
                      className="hidden"
                      id="file-upload"
                    />
                    <label htmlFor="file-upload">
                      <Button asChild>
                        <span>
                          <Upload className="mr-2 h-4 w-4" />
                          选择文件
                        </span>
                      </Button>
                    </label>
                  </div>
                </div>
              </div>

              {/* 已上传文件列表 */}
              {uploadedFiles.length > 0 && (
                <div className="space-y-4">
                  <h3 className="text-lg font-medium">已上传文件</h3>
                  <div className="space-y-2">
                    {uploadedFiles.map((file) => (
                      <div
                        key={file.id}
                        className="flex items-center justify-between p-4 border rounded-lg"
                      >
                        <div className="flex items-center gap-3">
                          {getFileIcon(file.type)}
                          <div>
                            <p className="font-medium">{file.name}</p>
                            <p className="text-sm text-gray-500">
                              {file.size} • {file.uploadTime}
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center gap-3">
                          {file.status === 'uploading' && (
                            <div className="flex items-center gap-2">
                              <Progress
                                value={file.progress}
                                className="w-20"
                              />
                              <span className="text-sm text-gray-500">
                                {file.progress}%
                              </span>
                            </div>
                          )}
                          {file.status === 'completed' && (
                            <Button
                              onClick={() => handleExtractElements(file)}
                              disabled={isExtracting}
                            >
                              {isExtracting ? (
                                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                              ) : (
                                <Search className="mr-2 h-4 w-4" />
                              )}
                              提取要素
                            </Button>
                          )}
                          {getStatusBadge(file.status)}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* 要素提取 Tab */}
        <TabsContent value="elements" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Search className="h-5 w-5" />
                稽查要素提取结果
              </CardTitle>
              {selectedFile && (
                <p className="text-sm text-gray-600">
                  来源文件：{selectedFile.name}
                </p>
              )}
            </CardHeader>
            <CardContent>
              {extractedElements.length > 0 ? (
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <p className="text-sm text-gray-600">
                      共提取 {extractedElements.length} 个稽查要素
                    </p>
                    <Button
                      onClick={handleGenerateRulePool}
                      disabled={isGenerating}
                    >
                      {isGenerating ? (
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      ) : (
                        <Wand2 className="mr-2 h-4 w-4" />
                      )}
                      生成规则池
                    </Button>
                  </div>

                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>要素名称</TableHead>
                        <TableHead>类型</TableHead>
                        <TableHead>置信度</TableHead>
                        <TableHead>来源</TableHead>
                        <TableHead>操作</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {extractedElements.map((element) => (
                        <TableRow key={element.id}>
                          <TableCell className="font-medium">
                            {element.element}
                          </TableCell>
                          <TableCell>
                            <Badge variant="outline">{element.type}</Badge>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center gap-2">
                              <div className="w-16 bg-gray-200 rounded-full h-2">
                                <div
                                  className="bg-green-500 h-2 rounded-full"
                                  style={{
                                    width: `${element.confidence * 100}%`,
                                  }}
                                ></div>
                              </div>
                              <span className="text-sm">
                                {(element.confidence * 100).toFixed(0)}%
                              </span>
                            </div>
                          </TableCell>
                          <TableCell className="text-sm text-gray-500">
                            {element.source}
                          </TableCell>
                          <TableCell>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleViewElementDetail(element)}
                            >
                              <Eye className="h-3 w-3 mr-1" />
                              查看详情
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              ) : (
                <div className="text-center py-8">
                  <Search className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                  <p className="text-gray-500">暂无提取的稽查要素</p>
                  <p className="text-sm text-gray-400 mt-1">
                    请先上传政策文件并提取要素
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* 规则池生成 Tab */}
        <TabsContent value="rules" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Wand2 className="h-5 w-5" />
                稽查规则池
              </CardTitle>
              <p className="text-sm text-gray-600">
                基于提取的要素生成的规则模板
              </p>
            </CardHeader>
            <CardContent>
              {ruleTemplates.length > 0 ? (
                <div className="space-y-4">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>规则名称</TableHead>
                        <TableHead>来源文件</TableHead>
                        <TableHead>提取要素</TableHead>
                        <TableHead>状态</TableHead>
                        <TableHead>操作</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {ruleTemplates.map((template) => (
                        <TableRow key={template.id}>
                          <TableCell>
                            <div className="space-y-1">
                              <div className="font-medium">{template.name}</div>
                              <div className="text-sm text-gray-500">
                                {template.description}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell className="text-sm text-gray-500">
                            {template.source}
                          </TableCell>
                          <TableCell>
                            <div className="flex flex-wrap gap-1">
                              {template.elements.map((element, index) => (
                                <Badge
                                  key={index}
                                  variant="secondary"
                                  className="text-xs"
                                >
                                  {element}
                                </Badge>
                              ))}
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge className="bg-green-100 text-green-800 border-green-300">
                              {template.status === 'generated'
                                ? '已生成'
                                : template.status}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <Button
                              size="sm"
                              onClick={() => handleConvertToRule(template)}
                            >
                              <ArrowRight className="h-3 w-3 mr-1" />
                              转换为规则
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              ) : (
                <div className="text-center py-8">
                  <Wand2 className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                  <p className="text-gray-500">暂无生成的规则模板</p>
                  <p className="text-sm text-gray-400 mt-1">
                    请先提取稽查要素并生成规则池
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* 要素详情弹窗 */}
      <Dialog open={showElementDetail} onOpenChange={setShowElementDetail}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Search className="h-5 w-5" />
              稽查要素详情
            </DialogTitle>
          </DialogHeader>
          {selectedElement && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-700">
                    要素名称
                  </label>
                  <p className="text-sm text-gray-900 mt-1 font-medium">
                    {selectedElement.element}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">
                    要素类型
                  </label>
                  <div className="mt-1">
                    <Badge variant="outline">{selectedElement.type}</Badge>
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">
                    置信度
                  </label>
                  <div className="flex items-center gap-2 mt-1">
                    <div className="w-20 bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-green-500 h-2 rounded-full"
                        style={{
                          width: `${selectedElement.confidence * 100}%`,
                        }}
                      ></div>
                    </div>
                    <span className="text-sm">
                      {(selectedElement.confidence * 100).toFixed(0)}%
                    </span>
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">
                    来源文件
                  </label>
                  <p className="text-sm text-gray-900 mt-1">
                    {selectedElement.source}
                  </p>
                </div>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-700">
                  要素描述
                </label>
                <p className="text-sm text-gray-900 mt-1 p-3 bg-gray-50 rounded border">
                  {selectedElement.description}
                </p>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}
