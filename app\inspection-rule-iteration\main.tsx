'use client'

import React, { useEffect, useState } from 'react'
import { getDraftRules, DraftRule, saveDraftRule } from '../../utils/draftRule'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card'
import { Tabs, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Badge } from '@/components/ui/badge'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Search,
  Plus,
  Eye,
  RefreshCw,
  Play,
  History,
  TrendingUp,
  GitBranch,
  Target,
} from 'lucide-react'
import RuleValidationModal from './RuleValidationModal'

// 详情弹窗组件
interface RuleDetailModalProps {
  rule: DraftRule | null
  isOpen: boolean
  onClose: () => void
  onValidationClick: (rule: DraftRule) => void
}

const RuleDetailModal: React.FC<RuleDetailModalProps> = ({
  rule,
  isOpen,
  onClose,
  onValidationClick,
}) => {
  if (!isOpen || !rule) return null

  // 模拟SQL解释步骤
  const explainSteps = [
    '1. 从相关数据表中选取关键字段信息。',
    '2. 应用业务逻辑过滤条件。',
    '3. 进行数据关联和计算。',
    '4. 按业务规则进行分组统计。',
    '5. 输出符合条件的数据记录。',
  ]

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Eye className="h-5 w-5" />
            规则详情: {rule.generatedDescription}
          </DialogTitle>
        </DialogHeader>
        <div className="space-y-6">
          {/* 基本信息 */}
          <Card className="bg-gradient-to-br from-blue-50 to-blue-100 border-blue-100 rounded-xl">
            <CardHeader className="pb-3">
              <CardTitle className="text-blue-500 text-sm">基本信息</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="font-medium text-gray-700">规则ID：</span>
                  <span className="text-gray-900">{rule.id}</span>
                </div>
                <div>
                  <span className="font-medium text-gray-700">状态：</span>
                  <span
                    className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      rule.status === '已发布'
                        ? 'bg-green-100 text-green-800'
                        : rule.status === '审核中'
                        ? 'bg-yellow-100 text-yellow-800'
                        : rule.status === '已拒绝'
                        ? 'bg-red-100 text-red-800'
                        : 'bg-blue-100 text-blue-800'
                    }`}
                  >
                    {rule.status}
                  </span>
                </div>
                <div>
                  <span className="font-medium text-gray-700">创建时间：</span>
                  <span className="text-gray-900">
                    {rule.createdAt
                      ? new Date(rule.createdAt).toLocaleString()
                      : '-'}
                  </span>
                </div>
                {rule.publishedAt && (
                  <div>
                    <span className="font-medium text-gray-700">
                      发布时间：
                    </span>
                    <span className="text-gray-900">
                      {new Date(rule.publishedAt).toLocaleString()}
                    </span>
                  </div>
                )}
                <div>
                  <span className="font-medium text-gray-700">执行次数：</span>
                  <span className="text-gray-900">
                    {rule.executionCount || 0}
                  </span>
                </div>
                <div>
                  <span className="font-medium text-gray-700">
                    命中问题数：
                  </span>
                  <span className="text-gray-900">{rule.hitCount || 0}</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 主题规则自然语言定义 */}
          <Card className="bg-gradient-to-br from-blue-50 to-blue-100 border-blue-100 rounded-xl">
            <CardHeader className="pb-3">
              <CardTitle className="text-blue-500 text-sm">
                主题规则自然语言定义
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="bg-blue-100 p-4 rounded-lg border border-blue-200 text-blue-900 whitespace-pre-line">
                {rule.generatedDescription || '暂无描述'}
              </div>
            </CardContent>
          </Card>

          {/* 主题规则语义化解释 */}
          <Card className="bg-gradient-to-br from-blue-50 to-blue-100 border-blue-100 rounded-xl">
            <CardHeader className="pb-3">
              <CardTitle className="text-blue-500 text-sm">
                主题规则语义化解释
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="bg-blue-100 p-4 rounded-lg border border-blue-200 text-blue-900">
                <pre className="text-sm whitespace-pre-line">
                  {rule.semanticResult ||
                    '基于业务需求，该规则将检查数据完整性和准确性，确保业务数据的质量符合预期标准。'}
                </pre>
              </div>
            </CardContent>
          </Card>

          {/* 可执行脚本信息 */}
          <Card className="bg-gradient-to-br from-blue-50 to-blue-100 border-blue-100 rounded-xl">
            <CardHeader className="pb-3">
              <CardTitle className="text-blue-500 text-sm">
                可执行脚本信息
              </CardTitle>
            </CardHeader>
            <CardContent>
              <Tabs defaultValue="sql" className="w-full">
                <TabsList className="grid w-full grid-cols-2">
                  <TabsTrigger value="sql">原始SQL</TabsTrigger>
                  <TabsTrigger value="explain">图谱解释</TabsTrigger>
                </TabsList>
                <TabsContent value="sql" className="mt-4">
                  <div className="bg-blue-100 p-4 rounded-lg border border-blue-200">
                    <pre className="text-sm text-blue-900 overflow-x-auto">
                      {rule.sqlContent ||
                        'SELECT * FROM table WHERE condition = true; -- 示例SQL'}
                    </pre>
                  </div>
                </TabsContent>
                <TabsContent value="explain" className="mt-4">
                  <div className="bg-blue-100 p-4 rounded-lg border border-blue-200">
                    <pre className="text-sm text-blue-900 whitespace-pre-line">
                      {explainSteps.join('\n')}
                    </pre>
                  </div>
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>

          {/* 操作按钮 */}
          <div className="flex gap-4 pt-4 border-t">
            <Button
              onClick={() =>
                (window.location.href = `/rule-generator?ruleId=${rule.id}`)
              }
              className="flex-1"
            >
              <RefreshCw className="mr-2 h-4 w-4" />
              解构与迭代
            </Button>
            <Button 
              onClick={() => onValidationClick(rule)}
              className="flex-1"
            >
              <Target className="mr-2 h-4 w-4" />
              命中范围样本性验证
            </Button>
            <Button variant="outline" disabled className="flex-1">
              <Play className="mr-2 h-4 w-4" />
              立即执行 (未来功能)
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}

const InspectionRuleIterationPage: React.FC = () => {
  const [drafts, setDrafts] = useState<DraftRule[]>([])
  const [selectedRule, setSelectedRule] = useState<DraftRule | null>(null)
  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false)
  const [isValidationModalOpen, setIsValidationModalOpen] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('全部')
  const [currentPage, setCurrentPage] = useState(1)
  const itemsPerPage = 6
  const [publishModalOpen, setPublishModalOpen] = useState(false)
  const [publishRule, setPublishRule] = useState<DraftRule | null>(null)
  const [inspectionDate, setInspectionDate] = useState('')

  useEffect(() => {
    // 获取草稿规则
    const existingDrafts = getDraftRules()

    // 如果没有数据，添加一些示例数据
    if (existingDrafts.length === 0) {
      const sampleDrafts = [
        {
          id: 1,
          generatedDescription:
            '电动汽车充电桩电量异常，每月筛查营销业务用户名称包含"充电"字段或用电地址包含"充电"字段，且电价执行居民合表电价的用户，对符合以下情况的开展稽查监控：抄表周期为"单月"月度发行电量（抄见电量）5000千瓦时以上；抄表周期为"双月"月度发行电量（抄见电量）10000千瓦时以上。',
          status: '已起草',
          createdAt: new Date().toISOString(),
          sqlContent:
            'SELECT user_id, user_name, address, meter_cycle, monthly_power FROM users WHERE (user_name LIKE "%充电%" OR address LIKE "%充电%") AND price_type = "居民合表" AND ((meter_cycle = "单月" AND monthly_power >= 5000) OR (meter_cycle = "双月" AND monthly_power >= 10000))',
          semanticResult:
            '筛查电动汽车充电桩用户，监控异常高电量情况，分单月和双月周期分别设定阈值。',
          executionCount: 0,
          hitCount: 0,
          creator: '稽查专员A',
        },
        {
          id: 2,
          generatedDescription:
            '电动汽车充电桩电量异常规则，待审核。',
          status: '审核中',
          createdAt: new Date().toISOString(),
          sqlContent:
            'SELECT user_id, user_name, address, meter_cycle, monthly_power FROM users WHERE (user_name LIKE "%充电%" OR address LIKE "%充电%") AND price_type = "居民合表" AND ((meter_cycle = "单月" AND monthly_power >= 5000) OR (meter_cycle = "双月" AND monthly_power >= 10000))',
          semanticResult:
            '针对充电桩用户的电量异常进行审核，确保规则准确性。',
          executionCount: 1,
          hitCount: 2,
          creator: '稽查专员B',
        },
        {
          id: 3,
          generatedDescription:
            '电动汽车充电桩电量异常规则，已发布。',
          status: '已发布',
          createdAt: new Date().toISOString(),
          publishedAt: new Date().toISOString(),
          sqlContent:
            'SELECT user_id, user_name, address, meter_cycle, monthly_power FROM users WHERE (user_name LIKE "%充电%" OR address LIKE "%充电%") AND price_type = "居民合表" AND ((meter_cycle = "单月" AND monthly_power >= 5000) OR (meter_cycle = "双月" AND monthly_power >= 10000))',
          semanticResult:
            '已正式用于监控充电桩用户的异常电量。',
          executionCount: 5,
          hitCount: 10,
          creator: '稽查专员C',
        },
        {
          id: 4,
          generatedDescription:
            '电动汽车充电桩电量异常规则，已拒绝。',
          status: '已拒绝',
          createdAt: new Date().toISOString(),
          rejectedAt: new Date().toISOString(),
          sqlContent:
            'SELECT user_id, user_name, address, meter_cycle, monthly_power FROM users WHERE (user_name LIKE "%充电%" OR address LIKE "%充电%") AND price_type = "居民合表" AND ((meter_cycle = "单月" AND monthly_power >= 5000) OR (meter_cycle = "双月" AND monthly_power >= 10000))',
          semanticResult:
            '该规则因不符合最新稽查要求被拒绝。',
          executionCount: 0,
          hitCount: 0,
          creator: '稽查专员D',
        },
        {
          id: 5,
          generatedDescription:
            '电动汽车充电桩电量异常规则，待执行。',
          status: '待执行',
          createdAt: new Date().toISOString(),
          sqlContent:
            'SELECT user_id, user_name, address, meter_cycle, monthly_power FROM users WHERE (user_name LIKE "%充电%" OR address LIKE "%充电%") AND price_type = "居民合表" AND ((meter_cycle = "单月" AND monthly_power >= 5000) OR (meter_cycle = "双月" AND monthly_power >= 10000))',
          semanticResult:
            '规则已准备好，等待执行监控任务。',
          executionCount: 0,
          hitCount: 0,
          creator: '稽查专员E',
        },
      ]

      // 直接保存示例数据到 localStorage，避免ID重复
      localStorage.setItem('inspectionDrafts', JSON.stringify(sampleDrafts))
      setDrafts(sampleDrafts)
    } else {
      setDrafts(existingDrafts)
    }
  }, [])

  // 过滤数据
  const filteredData = drafts.filter((rule) => {
    const matchesSearch =
      searchTerm === '' ||
      rule.generatedDescription
        .toLowerCase()
        .includes(searchTerm.toLowerCase()) ||
      (rule.creator &&
        rule.creator.toLowerCase().includes(searchTerm.toLowerCase()))

    const matchesStatus =
      statusFilter === '全部' || rule.status === statusFilter

    return matchesSearch && matchesStatus
  })

  // 分页
  const totalPages = Math.ceil(filteredData.length / itemsPerPage)
  const startIndex = (currentPage - 1) * itemsPerPage
  const paginatedData = filteredData.slice(
    startIndex,
    startIndex + itemsPerPage
  )

  // 获取状态徽章
  const getStatusBadge = (status: string) => {
    switch (status) {
      case '已发布':
        return (
          <Badge className="bg-green-100 text-green-800 border-green-300">
            已发布
          </Badge>
        )
      case '审核中':
        return (
          <Badge className="bg-yellow-100 text-yellow-800 border-yellow-300">
            审核中
          </Badge>
        )
      case '已拒绝':
        return (
          <Badge className="bg-red-100 text-red-800 border-red-300">
            已拒绝
          </Badge>
        )
      case '待执行':
        return (
          <Badge className="bg-blue-100 text-blue-800 border-blue-300">
            待执行
          </Badge>
        )
      case '草稿':
      case '已起草':
        return (
          <Badge className="bg-gray-100 text-gray-800 border-gray-300">
            草稿
          </Badge>
        )
      default:
        return <Badge variant="secondary">{status}</Badge>
    }
  }

  // 查看详情
  const handleViewDetail = (rule: DraftRule) => {
    setSelectedRule(rule)
    setIsDetailModalOpen(true)
  }

  // 关闭详情弹窗
  const handleCloseDetail = () => {
    setIsDetailModalOpen(false)
    setSelectedRule(null)
  }

  // 新建规则
  const handleCreateNewRule = () => {
    window.location.href = '/rule-generator'
  }

  // 解构与迭代
  const handleDeconstructAndIterate = (rule: DraftRule) => {
    window.location.href = `/rule-generator?ruleId=${rule.id}`
  }

  // 下载规则
  const handleDownload = (rule: DraftRule) => {
    const content = `规则ID: ${rule.id}
规则描述: ${rule.generatedDescription}
状态: ${rule.status}
创建时间: ${
      rule.createdAt ? new Date(rule.createdAt).toLocaleDateString() : '-'
    }
SQL内容: ${rule.sqlContent || '无'}`

    const blob = new Blob([content], { type: 'text/plain;charset=utf-8' })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `规则_${rule.id}_${rule.generatedDescription}.txt`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
  }

  // 审核规则
  const handleReview = (rule: DraftRule) => {
    const updatedDrafts = drafts.map((draft) =>
      draft.id === rule.id ? { ...draft, status: '审核中' } : draft
    )
    setDrafts(updatedDrafts)

    // 更新 localStorage
    localStorage.setItem('inspectionDrafts', JSON.stringify(updatedDrafts))

    alert(`规则 ${rule.id} 已提交审核`)
  }

  // 发布规则
  const handlePublish = (rule: DraftRule) => {
    const updatedDrafts = drafts.map((draft) =>
      draft.id === rule.id
        ? { ...draft, status: '已发布', publishedAt: new Date().toISOString() }
        : draft
    )
    setDrafts(updatedDrafts)

    // 更新 localStorage
    localStorage.setItem('inspectionDrafts', JSON.stringify(updatedDrafts))

    alert(`规则 ${rule.id} 已发布`)
  }

  // 拒绝规则
  const handleReject = (rule: DraftRule) => {
    const updatedDrafts = drafts.map((draft) =>
      draft.id === rule.id
        ? { ...draft, status: '已拒绝', rejectedAt: new Date().toISOString() }
        : draft
    )
    setDrafts(updatedDrafts)

    // 更新 localStorage
    localStorage.setItem('inspectionDrafts', JSON.stringify(updatedDrafts))

    alert(`规则 ${rule.id} 已拒绝`)
  }

  // 删除规则
  const handleDelete = (rule: DraftRule) => {
    if (confirm(`确定要删除规则 ${rule.id} 吗？`)) {
      const updatedDrafts = drafts.filter((draft) => draft.id !== rule.id)
      setDrafts(updatedDrafts)

      // 更新 localStorage
      localStorage.setItem('inspectionDrafts', JSON.stringify(updatedDrafts))

      alert(`规则 ${rule.id} 已删除`)
    }
  }

  // 打开验证模态框
  const handleOpenValidation = (rule: DraftRule) => {
    setSelectedRule(rule)
    setIsValidationModalOpen(true)
  }

  // 关闭验证模态框
  const handleCloseValidation = () => {
    setIsValidationModalOpen(false)
    setSelectedRule(null)
  }

  // 打开发布上线参数弹窗
  const handleOpenPublish = (rule: DraftRule) => {
    setPublishRule(rule)
    setInspectionDate('')
    setPublishModalOpen(true)
  }

  // 关闭发布上线参数弹窗
  const handleClosePublish = () => {
    setPublishModalOpen(false)
    setPublishRule(null)
    setInspectionDate('')
  }

  // 确认发布
  const handleConfirmPublish = () => {
    if (!publishRule || !inspectionDate) return
    // 生成巡查任务（可用localStorage或sessionStorage模拟）
    const tasks = JSON.parse(localStorage.getItem('inspectionTasks') || '[]')
    const newTask = {
      id: Date.now(),
      name: publishRule.generatedDescription?.slice(0, 20) || '巡查任务',
      type: '数据中台离线定时任务',
      schedule: inspectionDate,
      status: '待执行',
      nextRun: inspectionDate,
      log: '',
      description: publishRule.generatedDescription,
      owner: publishRule.creator || '',
      createTime: new Date().toISOString(),
      lastRunTime: '',
      lastRunStatus: '',
      config: JSON.stringify({ sql: publishRule.sqlContent })
    }
    tasks.push(newTask)
    localStorage.setItem('inspectionTasks', JSON.stringify(tasks))
    setPublishModalOpen(false)
    setPublishRule(null)
    setInspectionDate('')
    // 跳转到巡查任务管理
    window.location.href = '/data-inspection'
  }

  return (
    <div className="flex-1 space-y-6 p-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div className="space-y-2">
          <h1 className="text-3xl font-bold tracking-tight text-gray-900">
            稽查主题规则迭代
          </h1>
          <p className="text-gray-600">管理和迭代系统中的稽查主题规则</p>
        </div>
        <Button
          onClick={handleCreateNewRule}
          className="flex items-center gap-2"
        >
          <Plus className="h-4 w-4" />
          新建规则
        </Button>
      </div>

      {/* 控制区域 */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="搜索规则描述或创建者..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-full sm:w-48">
                <SelectValue placeholder="状态" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="全部">全部</SelectItem>
                <SelectItem value="草稿">草稿</SelectItem>
                <SelectItem value="审核中">审核中</SelectItem>
                <SelectItem value="已发布">已发布</SelectItem>
                <SelectItem value="已拒绝">已拒绝</SelectItem>
                <SelectItem value="待执行">待执行</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* 主要内容 - 规则列表表格 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <History className="h-5 w-5" />
            规则列表
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>规则描述</TableHead>
                  <TableHead>状态</TableHead>
                  <TableHead>创建时间</TableHead>
                  <TableHead>执行统计</TableHead>
                  <TableHead>操作</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {paginatedData.map((rule) => (
                  <TableRow key={rule.id}>
                    <TableCell>
                      <div className="space-y-1">
                        <div className="font-medium text-sm">
                          {rule.generatedDescription}
                        </div>
                        <div className="text-xs text-gray-500">
                          创建者: {rule.creator || '未知'} | ID: {rule.id}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>{getStatusBadge(rule.status || '')}</TableCell>
                    <TableCell className="text-sm text-gray-600">
                      {rule.createdAt
                        ? new Date(rule.createdAt).toLocaleDateString()
                        : '-'}
                    </TableCell>
                    <TableCell>
                      <div className="text-xs text-gray-600">
                        <div>执行: {rule.executionCount || 0}</div>
                        <div>命中: {rule.hitCount || 0}</div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleViewDetail(rule)}
                        >
                          <Eye className="h-3 w-3 mr-1" />
                          查看
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleDownload(rule)}
                        >
                          下载
                        </Button>
                        <Button
                          size="sm"
                          onClick={() => handleDeconstructAndIterate(rule)}
                        >
                          <RefreshCw className="h-3 w-3 mr-1" />
                          迭代
                        </Button>
                        {rule.status === '待执行' && (
                          <Button
                            size="sm"
                            variant="default"
                            onClick={() => handleOpenPublish(rule)}
                          >
                            发布上线
                          </Button>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>

          {/* 分页 */}
          <div className="flex items-center justify-between space-x-2 py-4">
            <div className="text-sm text-gray-500">
              显示 {startIndex + 1} 到{' '}
              {Math.min(startIndex + itemsPerPage, filteredData.length)} 条， 共{' '}
              {filteredData.length} 条记录
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
                disabled={currentPage === 1}
              >
                上一页
              </Button>
              <div className="flex items-center space-x-1">
                {Array.from({ length: totalPages }, (_, i) => i + 1).map(
                  (page) => (
                    <Button
                      key={page}
                      variant={currentPage === page ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setCurrentPage(page)}
                      className="w-8 h-8"
                    >
                      {page}
                    </Button>
                  )
                )}
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() =>
                  setCurrentPage((prev) => Math.min(prev + 1, totalPages))
                }
                disabled={currentPage === totalPages}
              >
                下一页
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 扩展功能区域 */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-semibold text-gray-900">扩展功能</h2>
          <Button disabled className="flex items-center gap-2">
            <Play className="h-4 w-4" />
            批量执行规则 (未来功能)
          </Button>
        </div>

        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {/* 规则执行历史 */}
          <Card className="border-2 border-dashed border-gray-300 bg-gray-50/50">
            <CardHeader className="text-center pb-4">
              <div className="mx-auto w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center mb-3">
                <History className="h-6 w-6 text-gray-400" />
              </div>
              <CardTitle className="text-sm font-medium text-gray-600">
                规则执行历史
              </CardTitle>
            </CardHeader>
            <CardContent className="text-center">
              <p className="text-xs text-gray-500 mb-2">未来功能</p>
              <p className="text-xs text-gray-400">
                查看规则的详细执行历史和结果统计
              </p>
              <div className="mt-3 space-y-1">
                <div className="h-2 bg-gray-200 rounded"></div>
                <div className="h-2 bg-gray-200 rounded w-3/4"></div>
                <div className="h-2 bg-gray-200 rounded w-1/2"></div>
              </div>
            </CardContent>
          </Card>

          {/* 规则评估与迭代建议 */}
          <Card className="border-2 border-dashed border-gray-300 bg-gray-50/50">
            <CardHeader className="text-center pb-4">
              <div className="mx-auto w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center mb-3">
                <TrendingUp className="h-6 w-6 text-gray-400" />
              </div>
              <CardTitle className="text-sm font-medium text-gray-600">
                规则评估与迭代建议
              </CardTitle>
            </CardHeader>
            <CardContent className="text-center">
              <p className="text-xs text-gray-500 mb-2">未来功能</p>
              <p className="text-xs text-gray-400">
                基于执行结果提供规则优化建议
              </p>
              <div className="mt-3 space-y-2">
                <div className="flex items-center justify-between text-xs">
                  <span className="text-gray-400">准确率</span>
                  <span className="text-gray-400">--</span>
                </div>
                <div className="flex items-center justify-between text-xs">
                  <span className="text-gray-400">覆盖率</span>
                  <span className="text-gray-400">--</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 规则版本管理 */}
          <Card className="border-2 border-dashed border-gray-300 bg-gray-50/50">
            <CardHeader className="text-center pb-4">
              <div className="mx-auto w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center mb-3">
                <GitBranch className="h-6 w-6 text-gray-400" />
              </div>
              <CardTitle className="text-sm font-medium text-gray-600">
                规则版本管理
              </CardTitle>
            </CardHeader>
            <CardContent className="text-center">
              <p className="text-xs text-gray-500 mb-2">未来功能</p>
              <p className="text-xs text-gray-400">
                管理规则的版本历史和变更记录
              </p>
              <div className="mt-3 space-y-1">
                <div className="flex items-center gap-2 text-xs text-gray-400">
                  <div className="w-2 h-2 bg-gray-300 rounded-full"></div>
                  <span>v1.0</span>
                </div>
                <div className="flex items-center gap-2 text-xs text-gray-400">
                  <div className="w-2 h-2 bg-gray-300 rounded-full"></div>
                  <span>v1.1</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* 详情弹窗 */}
      <RuleDetailModal
        rule={selectedRule}
        isOpen={isDetailModalOpen}
        onClose={handleCloseDetail}
        onValidationClick={handleOpenValidation}
      />

      {/* 验证模态框 */}
      <RuleValidationModal
        rule={selectedRule}
        isOpen={isValidationModalOpen}
        onClose={handleCloseValidation}
      />

      {/* 发布上线参数弹窗 */}
      <Dialog open={publishModalOpen} onOpenChange={setPublishModalOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>发布上线并生成巡查任务</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <label className="block mb-1 font-medium">巡查例日时间</label>
              <Input
                type="datetime-local"
                value={inspectionDate}
                onChange={e => setInspectionDate(e.target.value)}
              />
            </div>
            <div className="flex justify-end gap-2">
              <Button variant="outline" onClick={handleClosePublish}>取消</Button>
              <Button onClick={handleConfirmPublish} disabled={!inspectionDate}>确认发布</Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}

export default InspectionRuleIterationPage
