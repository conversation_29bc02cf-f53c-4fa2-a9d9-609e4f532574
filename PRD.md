# 电力数据大脑 - 智能稽查系统 PRD

```yaml
# === PROJECT METADATA ===
project_name: "电力数据大脑 - 智能稽查系统"
version: "v1.0"
created_date: "2025-01-06"
last_updated: "2025-01-06"
project_type: "web_app"
complexity_level: "complex"
estimated_duration: "24 weeks"
```

## 🎯 产品概览 (Product Overview)

### 核心价值主张
> 基于AI驱动的电力数据治理与营销稽查智能化平台，将业务智慧转化为机器可执行规则，实现稽查自动化，变被动稽查为主动预警。

### 目标用户画像
- **主要用户**: 电力行业业务专家、稽查人员、数据分析师、数据工程师
- **使用场景**: 
  - 营销稽查规则智能生成与管理
  - 数据质量监控与异常检测
  - 政策文件智能解析与要素提取
  - 元数据管理与语义层构建
  - 问题命中分析与处理
- **用户痛点**: 传统稽查规则定义复杂耗时、风险识别滞后被动、知识体系分散老化、数据孤岛与质量问题

### 成功指标
- **北极星指标**: 稽查规则自动化生成率 >80%
- **关键结果**: 
  - 规则生成效率提升 5倍以上
  - 问题发现准确率 >90%
  - 数据质量监控覆盖率 >95%
- **验证假设**: AI驱动的规则生成能够有效替代人工编写，提升稽查效率和准确性

## 🔧 技术架构 (Technical Architecture)

### 技术栈选择
```json
{
  "frontend": {
    "framework": "Next.js 15.2.4",
    "language": "TypeScript",
    "styling": "Tailwind CSS + shadcn/ui",
    "state_management": "React Hooks",
    "visualization": "D3.js + Recharts + Vis Network"
  },
  "backend": {
    "framework": "Spring Boot + Spring Cloud",
    "language": "Java 8+",
    "database": "MySQL 8.0 + Milvus + Redis",
    "ai_services": "OpenAI GPT-4 + LangChain + RAG"
  },
  "infrastructure": {
    "hosting": "Docker + Kubernetes",
    "ci_cd": "Jenkins + GitLab CI",
    "monitoring": "Prometheus + Grafana + ELK Stack",
    "message_queue": "Apache Kafka + RabbitMQ"
  }
}
```

### 架构约束
- **性能要求**: 页面加载<3s，复杂查询/规则生成<10s，并发用户数50+
- **安全要求**: RBAC权限控制，数据传输/存储加密，操作日志审计
- **可扩展性**: 微服务架构，模块化设计，支持后续水平扩展

## 📦 功能需求矩阵 (Feature Requirements Matrix)

### MVP版本 (P0 - 核心功能)
| 功能ID | 功能名称 | 用户故事 | 复杂度 | 工期 | 依赖 |
|--------|----------|----------|---------|------|------|
| F001 | 主导航和仪表盘 | 作为用户，我希望有统一的导航和核心指标概览，以便快速了解系统状态 | M | 5d | - |
| F002 | 稽查规则智能生成 | 作为稽查人员，我希望通过自然语言或可视化方式生成规则，以便提升工作效率 | XL | 15d | F001 |
| F003 | 数据巡查任务管理 | 作为数据工程师，我希望管理数据巡查任务，以便确保数据质量 | L | 8d | F001 |
| F004 | 元数据管理 | 作为数据分析师，我希望查询和管理元数据，以便了解数据资产 | M | 6d | F001 |
| F005 | 问题命中管理 | 作为稽查人员，我希望查看和处理问题命中，以便及时解决业务问题 | M | 5d | F002 |
| F006 | SQL脚本解析增强 | 作为数据工程师，我希望从真实SQL脚本中提取关联关系，以便完善元数据管理 | L | 8d | F004 |
| F007 | 数据字典管理 | 作为数据分析师，我希望管理数据字典信息，以便理解字段的业务含义 | M | 6d | F004 |
| F008 | 数据血缘溯源 | 作为数据工程师，我希望追踪数据的来源和去向，以便进行影响分析 | XL | 10d | F006,F007 |

### 增强版本 (P1 - 重要功能)
| 功能ID | 功能名称 | 用户故事 | 复杂度 | 工期 | 依赖 |
|--------|----------|----------|---------|------|------|
| F006 | 政策文件智能解析 | 作为业务专家，我希望自动提取政策文件要素，以便快速生成规则池 | L | 10d | F002 |
| F007 | 稽查知识管理 | 作为稽查人员，我希望管理稽查知识库，以便支撑智能分析 | M | 7d | F002 |
| F008 | 语义原子管理 | 作为数据工程师，我希望管理语义原子，以便构建语义层 | M | 6d | F004 |

### 完整版本 (P2 - 增值功能)
| 功能ID | 功能名称 | 用户故事 | 复杂度 | 工期 | 依赖 |
|--------|----------|----------|---------|------|------|
| F009 | 数据质量管理 | 作为数据工程师，我希望全面监控数据质量，以便保障数据可靠性 | L | 12d | F003,F004 |
| F010 | 智能分析与推荐 | 作为业务专家，我希望获得智能分析建议，以便优化稽查策略 | XL | 18d | F002,F007 |

**复杂度说明**: S(Simple, 1-2天) | M(Medium, 3-5天) | L(Large, 6-10天) | XL(Extra Large, >10天)

## 📋 功能详细规格 (Detailed Specifications)

### 稽查规则智能生成 - F002
```yaml
# 功能基本信息
feature_id: "F002"
feature_name: "稽查规则智能生成"
priority: "P0"
complexity: "XL"
estimated_effort: "15d"
dependencies: ["F001"]

# 功能描述
description: |
  支持自然语言和可视化拖拽两种模式生成稽查规则，实时预览规则逻辑和SQL脚本，
  提供规则模板选择和智能优化建议，支持规则保存和版本管理。

# 技术规格
technical_specs:
  input_format: "自然语言描述 | 可视化组件配置"
  output_format: "SQL脚本 + 规则描述 + 逻辑流程图"
  api_endpoints:
    - method: "POST"
      path: "/api/v1/rules/generate"
      request_body: |
        {
          "mode": "natural_language | visual_editor",
          "input": "规则描述或配置",
          "template_id": "可选模板ID"
        }
      response_body: |
        {
          "status": "success",
          "data": {
            "rule_id": "生成的规则ID",
            "sql_script": "生成的SQL脚本",
            "description": "规则描述",
            "logic_flow": "逻辑流程图数据"
          }
        }

# 业务逻辑
business_logic:
  - step: "模式选择"
    description: "用户选择自然语言或可视化编排模式"
  - step: "规则构建"
    description: "根据选择的模式进行规则构建和配置"
  - step: "智能转换"
    description: "调用AI服务将输入转换为SQL脚本和规则描述"
  - step: "实时预览"
    description: "实时展示生成的规则逻辑和SQL脚本"
  - step: "规则保存"
    description: "保存规则到规则库，支持版本管理"

# 验收标准
acceptance_criteria:
  - criterion: "支持自然语言输入生成规则，准确率>85%"
    test_method: "功能测试 + AI模型测试"
  - criterion: "可视化编排界面支持拖拽操作，响应时间<500ms"
    test_method: "UI交互测试 + 性能测试"
  - criterion: "生成的SQL脚本语法正确，可执行率>95%"
    test_method: "SQL语法验证 + 数据库执行测试"

# 实现提示
implementation_hints:
  code_generation_prompt: |
    生成稽查规则智能生成功能的实现代码，要求：
    1. 使用Next.js + TypeScript + shadcn/ui
    2. 集成AI服务进行NL2SQL转换
    3. 实现可视化拖拽编排界面
    4. 添加实时预览和验证功能
    5. 包含完整的错误处理和用户反馈
  
  key_considerations:
    - "AI服务调用的错误处理和重试机制"
    - "可视化编排的性能优化"
    - "规则版本管理和冲突检测"
    - "用户体验的流畅性和响应性"
```

### 数据巡查任务管理 - F003
```yaml
# 功能基本信息
feature_id: "F003"
feature_name: "数据巡查任务管理"
priority: "P0"
complexity: "L"
estimated_effort: "8d"
dependencies: ["F001"]

# 功能描述
description: |
  管理MaxCompute DI任务和XXL-Job定时巡查任务，支持任务创建、配置、
  调度、监控和日志查看，提供数据质量监控和异常告警功能。

# 技术规格
technical_specs:
  input_format: "任务配置参数 + 调度设置"
  output_format: "任务状态 + 执行日志 + 监控报告"
  api_endpoints:
    - method: "POST"
      path: "/api/v1/inspection/tasks"
      request_body: |
        {
          "name": "任务名称",
          "type": "maxcompute_di | xxl_job",
          "config": "任务配置",
          "schedule": "调度配置"
        }
      response_body: |
        {
          "status": "success",
          "data": {
            "task_id": "任务ID",
            "status": "任务状态"
          }
        }

# 验收标准
acceptance_criteria:
  - criterion: "支持MaxCompute DI和XXL-Job任务管理"
    test_method: "集成测试"
  - criterion: "任务执行监控实时性<30s"
    test_method: "性能测试"
  - criterion: "异常告警及时性<5min"
    test_method: "告警测试"
```

### 元数据管理 - F004
```yaml
# 功能基本信息
feature_id: "F004"
feature_name: "元数据管理"
priority: "P0"
complexity: "M"
estimated_effort: "6d"
dependencies: ["F001"]

# 功能描述
description: |
  提供元数据概览、查询、同步管理功能，支持数据资产统计、
  结构化展示、字段详情查看和分页浏览。

# 技术规格
technical_specs:
  input_format: "搜索条件 + 筛选参数"
  output_format: "元数据列表 + 统计信息 + 详情数据"
  api_endpoints:
    - method: "GET"
      path: "/api/v1/metadata"
      request_body: |
        {
          "search": "搜索关键词",
          "filters": "筛选条件",
          "page": 1,
          "size": 20
        }
      response_body: |
        {
          "status": "success",
          "data": {
            "items": "元数据列表",
            "total": "总数",
            "page": "当前页"
          }
        }

# 验收标准
acceptance_criteria:
  - criterion: "支持多维度搜索和筛选，响应时间<2s"
    test_method: "功能测试 + 性能测试"
  - criterion: "元数据同步准确率>99%"
    test_method: "数据一致性测试"
  - criterion: "支持分页浏览，每页加载时间<1s"
    test_method: "性能测试"
```

### SQL脚本解析增强 - F006
```yaml
# 功能基本信息
feature_id: "F006"
feature_name: "SQL脚本解析增强"
priority: "P0"
complexity: "L"
estimated_effort: "8d"
dependencies: ["F004"]

# 功能描述
description: |
  从真实SQL脚本中智能解析表间关联关系，补充物理外键约束缺失的关联信息，
  提升元数据管理的完整性和准确性。

# 技术规格
technical_specs:
  input_format: "SQL脚本文件 | SQL文本内容"
  output_format: "关联关系列表 + 解析统计 + 可视化图谱"
  frontend_implementation:
    - parser: "node-sql-parser (前端JavaScript库)"
    - algorithm: "关联关系挖掘算法 (前端实现)"
    - visualization: "基于D3.js的关系图谱展示"
    - mock_data: "丰富的SQL解析结果Mock数据"

# 用户交互流程
user_flow:
  - step: "SQL脚本上传"
    description: "用户上传SQL文件或粘贴SQL内容"
  - step: "智能解析"
    description: "前端解析引擎分析SQL语句结构"
  - step: "关联关系提取"
    description: "从JOIN、WHERE条件中提取表间关联关系"
  - step: "结果展示"
    description: "可视化展示解析结果和关联关系图谱"
  - step: "关系确认"
    description: "用户确认或修正识别的关联关系"

# 验收标准
acceptance_criteria:
  - criterion: "支持常见SQL语法解析，覆盖率>90%"
    test_method: "功能测试 + SQL语法测试"
  - criterion: "关联关系识别准确率>85%"
    test_method: "算法测试 + 人工验证"
  - criterion: "解析结果可视化展示完整"
    test_method: "UI测试 + 用户体验测试"
```

### 数据字典管理 - F007
```yaml
# 功能基本信息
feature_id: "F007"
feature_name: "数据字典管理"
priority: "P0"
complexity: "M"
estimated_effort: "6d"
dependencies: ["F004"]

# 功能描述
description: |
  智能识别和管理数据字典信息，从数据源获取标准代码表数据，
  完善元数据的业务语义，提供字段值域的业务含义解释。

# 技术规格
technical_specs:
  input_format: "数据源连接信息 + 字典表配置"
  output_format: "数据字典列表 + 字段映射 + 业务含义展示"
  frontend_implementation:
    - identifier: "数据字典字段识别算法 (前端JavaScript)"
    - management: "字典管理界面组件"
    - integration: "与元数据图谱的集成展示"
    - mock_data: "标准代码表和字典映射Mock数据"

# 用户交互流程
user_flow:
  - step: "字典源配置"
    description: "配置数据字典来源表和字段映射"
  - step: "自动识别"
    description: "系统自动识别可能的数据字典字段"
  - step: "字典管理"
    description: "管理数据字典条目和业务含义"
  - step: "图谱集成"
    description: "将字典信息集成到元数据图谱展示"
  - step: "业务解释"
    description: "为字段值提供业务含义解释"

# 验收标准
acceptance_criteria:
  - criterion: "数据字典字段识别准确率>80%"
    test_method: "算法测试 + 业务验证"
  - criterion: "字典信息在图谱中正确展示"
    test_method: "集成测试 + 可视化验证"
  - criterion: "支持字典信息的增删改查操作"
    test_method: "功能测试 + 数据一致性测试"
```

### 数据血缘溯源 - F008
```yaml
# 功能基本信息
feature_id: "F008"
feature_name: "数据血缘溯源"
priority: "P1"
complexity: "XL"
estimated_effort: "10d"
dependencies: ["F006", "F007"]

# 功能描述
description: |
  提供字段级和表级的数据血缘溯源分析，支持写入链路和使用链路追踪，
  为数据影响分析和SELECT语句可视化奠定基础。

# 技术规格
technical_specs:
  input_format: "字段/表标识 + 溯源方向 + 深度限制"
  output_format: "血缘路径 + 可视化图谱 + 影响分析报告"
  frontend_implementation:
    - analyzer: "血缘分析算法 (前端图算法实现)"
    - visualization: "血缘关系可视化组件"
    - interaction: "交互式血缘探索界面"
    - mock_data: "复杂的血缘关系Mock数据"

# 用户交互流程
user_flow:
  - step: "目标选择"
    description: "选择要分析的字段或表"
  - step: "溯源配置"
    description: "配置溯源方向(上游/下游)和深度"
  - step: "血缘分析"
    description: "执行血缘关系分析算法"
  - step: "结果可视化"
    description: "以图谱形式展示血缘关系"
  - step: "路径探索"
    description: "交互式探索具体的血缘路径"

# 验收标准
acceptance_criteria:
  - criterion: "支持5层深度的血缘溯源分析"
    test_method: "算法测试 + 性能测试"
  - criterion: "血缘关系可视化清晰易懂"
    test_method: "UI测试 + 用户体验测试"
  - criterion: "溯源结果准确率>85%"
    test_method: "算法验证 + 业务场景测试"
```

## 📊 数据模型 (Data Models)

### 核心实体定义
```typescript
// 稽查规则实体
interface AuditRule {
  id: string;                    // 规则ID
  name: string;                  // 规则名称
  description: string;           // 规则描述
  category: string;              // 规则分类
  sql_script: string;            // SQL脚本
  status: 'draft' | 'pending' | 'active' | 'inactive';
  created_at: Date;
  updated_at: Date;
  created_by: string;
  version: number;
}

// 数据源实体
interface DataSource {
  id: string;
  name: string;
  type: 'PDF' | 'Word' | 'TXT' | 'URL' | 'Database' | 'API';
  description: string;
  url?: string;
  file_path?: string;
  status: 'Active' | 'Inactive' | 'Error' | 'Processing';
  last_update: Date;
  last_sync: Date;
  tags: string[];
}

// SQL解析结果实体
interface SQLParseResult {
  id: string;
  sql_content: string;
  parse_status: 'SUCCESS' | 'FAILED' | 'PARTIAL';
  extracted_tables: string[];
  extracted_relations: Relation[];
  confidence_score: number;
  created_at: Date;
}

// 关联关系实体
interface Relation {
  id: string;
  from_table: string;
  from_column: string;
  to_table: string;
  to_column: string;
  relation_type: 'JOIN' | 'WHERE' | 'INFERRED';
  confidence: number;
  sql_source: string;
  created_at: Date;
}

// 数据字典实体
interface DataDictionary {
  id: string;
  table_name: string;
  code_field: string;
  name_field: string;
  category_field?: string;
  data_source: string;
  entries: DictionaryEntry[];
  last_updated: Date;
}

interface DictionaryEntry {
  code: string | number;
  name: string;
  description?: string;
  category?: string;
  status: 'ACTIVE' | 'INACTIVE';
}

// 血缘关系实体
interface LineageRelation {
  id: string;
  source_table: string;
  source_field: string;
  target_table: string;
  target_field: string;
  transformation_type: 'DIRECT_COPY' | 'FUNCTION_TRANSFORM' | 'AGGREGATION';
  sql_context: string;
  confidence: number;
  created_at: Date;
}

// 问题命中实体
interface IssueHit {
  id: string;
  rule_id: string;
  rule_name: string;
  customer_id: string;
  customer_name: string;
  hit_date: Date;
  status: 'pending' | 'processing' | 'resolved' | 'dismissed';
  description: string;
  severity: 'high' | 'medium' | 'low';
}

// 语义原子实体
interface SemanticAtom {
  id: string;
  name: string;
  type: string;
  category: string;
  description: string;
  definition: string;
  usage_count: number;
  status: 'active' | 'inactive';
  tags: string[];
  physical_mappings: PhysicalMapping[];
}

interface PhysicalMapping {
  table: string;
  field: string;
  field_desc: string;
}
```

### 关系映射
```mermaid
erDiagram
    AuditRule ||--o{ IssueHit : generates
    DataSource ||--o{ AuditRule : supports
    SemanticAtom ||--o{ AuditRule : uses
    User ||--o{ AuditRule : creates
    User ||--o{ IssueHit : handles
```

### API设计规范
```yaml
# RESTful API 设计标准
api_base_url: "https://api.datamind.com/v1"
authentication: "Bearer Token (JWT)"
rate_limiting: "100 requests/minute"

# 统一响应格式
response_format:
  success: |
    {
      "status": "success",
      "data": {...},
      "timestamp": "2025-01-06T00:00:00Z",
      "request_id": "uuid"
    }
  error: |
    {
      "status": "error",
      "error_code": "E001",
      "message": "Error description",
      "timestamp": "2025-01-06T00:00:00Z",
      "request_id": "uuid"
    }
```

## 🗓️ 实施路线图 (Implementation Roadmap)

### 迭代计划
```yaml
sprint_1:
  duration: "2 weeks"
  goal: "基础架构和核心导航"
  deliverables:
    - feature_id: "F001"
      status: "必须完成"
      assignee: "前端团队"
    - infrastructure_setup: "完成"
      assignee: "DevOps团队"

sprint_2:
  duration: "3 weeks"
  goal: "数据管理核心功能"
  deliverables:
    - feature_id: "F003"
      status: "必须完成"
      assignee: "后端团队"
    - feature_id: "F004"
      status: "必须完成"
      assignee: "前端团队"

sprint_3:
  duration: "4 weeks"
  goal: "稽查规则智能生成"
  deliverables:
    - feature_id: "F002"
      status: "必须完成"
      assignee: "全栈团队"
    - ai_service_integration: "完成"
      assignee: "AI团队"

sprint_4:
  duration: "2 weeks"
  goal: "问题管理和系统集成"
  deliverables:
    - feature_id: "F005"
      status: "必须完成"
      assignee: "前端团队"
    - integration_testing: "完成"
      assignee: "测试团队"

# 里程碑检查点
milestones:
  mvp_demo:
    date: "2025-03-15"
    criteria: "核心功能演示，用户可完成完整稽查规则生成流程"
  beta_release:
    date: "2025-04-30"
    criteria: "功能完整，性能达标，可支持50+并发用户"
  production_release:
    date: "2025-06-30"
    criteria: "生产就绪，安全合规，运维监控完善"
```

### 质量保证
- **测试覆盖率**: 单元测试 >80%, 集成测试 >70%, E2E测试 >60%
- **性能基准**: API响应时间 <500ms, 页面加载 <3s, 复杂查询 <10s
- **安全检查**: 代码扫描、依赖检查、渗透测试、RBAC权限验证
- **可用性目标**: 系统可用性 >99.5%, RTO <30min, RPO <15min

## 🤖 AI协作配置 (AI Collaboration Config)

### 代码生成上下文
```yaml
# 项目上下文文件
project_context:
  tech_stack: "Next.js 15 + React 19 + TypeScript + Tailwind CSS + shadcn/ui"
  coding_style: "TypeScript严格模式，函数式组件，Hooks优先"
  project_structure: |
    app/
    ├── dashboard/              # 仪表盘
    ├── rule-generator/         # 规则生成器
    ├── data-inspection/        # 数据巡查
    ├── metadata-*/            # 元数据管理
    ├── metadata-script-extract/ # SQL脚本解析增强
    ├── data-dictionary-management/ # 数据字典管理
    ├── data-lineage-analysis/  # 数据血缘溯源
    ├── semantic-*/            # 语义管理
    ├── audit-*/               # 稽查相关
    └── issue-hits/            # 问题命中
    components/
    ├── layout/                # 布局组件
    ├── ui/                    # UI组件库
    ├── sql-analysis/          # SQL解析相关组件
    ├── dictionary-display/    # 数据字典展示组件
    └── lineage-visualization/ # 血缘关系可视化组件

# AI提示词模板
code_generation_templates:
  component_prompt: |
    基于PRD中的功能规格[功能ID]，生成Next.js React组件代码：
    1. 使用TypeScript和shadcn/ui组件库
    2. 实现所有验收标准中的功能要求
    3. 添加适当的错误处理和加载状态
    4. 包含响应式设计和无障碍访问
    5. 遵循项目的代码规范和目录结构

  api_prompt: |
    基于PRD中的API规格[功能ID]，生成后端API代码：
    1. 使用Spring Boot + MyBatis Plus
    2. 实现所有端点定义和业务逻辑
    3. 包含参数验证和异常处理
    4. 添加API文档注释和日志记录
    5. 遵循RESTful设计原则

# 迭代更新指令
update_instructions:
  feature_change: "更新相关功能规格，保持API兼容性，通知相关团队"
  requirement_addition: "评估影响范围，更新工期估算，调整迭代计划"
  priority_adjustment: "重新排序功能矩阵，调整资源分配，更新里程碑"
```

### 上下文传递规则
- 每次AI交互都包含相关的功能规格和技术约束
- 保持技术栈和架构约束的一致性
- 传递测试标准和验收条件
- 维护代码风格和项目结构的统一性

## 💡 质量保证清单

生成PRD后，确保包含：
- [x] 所有P0功能都有明确的验收标准
- [x] 技术规格可以直接用于代码生成
- [x] API设计符合RESTful规范
- [x] 数据模型定义完整
- [x] 实施计划具有可执行性
- [x] AI协作配置完整可用
- [x] 文档结构清晰、格式规范

---

**注意**: 本PRD文档是活文档，支持持续更新和改进。所有技术规格都经过详细设计，可直接指导AI工具进行代码生成。项目采用敏捷开发模式，支持快速迭代和用户反馈驱动的产品优化。

### 项目特色亮点

1. **AI驱动的智能化**: 集成大语言模型实现自然语言到SQL的转换，支持智能规则生成
2. **双模式规则创建**: 支持自然语言描述和可视化拖拽两种规则创建方式
3. **完整的数据治理**: 从元数据管理到语义层构建，提供全链路数据治理能力
4. **智能SQL解析**: 从真实SQL脚本中提取关联关系，补充物理外键约束缺失
5. **数据字典管理**: 智能识别和管理标准代码，提供业务语义解释
6. **数据血缘溯源**: 支持字段级和表级的完整血缘关系追踪分析
7. **实时监控告警**: 支持数据质量实时监控和异常告警，确保数据可靠性
8. **现代化技术栈**: 采用Next.js 15、React 19、TypeScript等最新技术
9. **微服务架构**: 支持水平扩展和模块化部署
10. **AI协作友好**: 专门设计的AI协作配置，支持代码自动生成和迭代优化
