"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { 
  Search, 
  GitBranch, 
  Database, 
  Target, 
  ArrowRight, 
  ArrowLeft, 
  Loader2,
  AlertCircle,
  CheckCircle,
  Info,
  Settings,
  BarChart3
} from "lucide-react"
import { lineageAnalyzer } from "@/lib/lineage-analyzer"
import LineageGraph from "@/components/lineage-visualization/LineageGraph"
import LineagePathView from "@/components/lineage-visualization/LineagePathView"
import type { 
  FieldReference, 
  TableReference, 
  LineageAnalysisResult, 
  LineageAnalysisConfig,
  LineageGraphNode,
  LineageGraphEdge,
  LineagePath,
  LineageNode
} from "@/types/lineage"

export default function DataLineageAnalysisMain() {
  const [analysisType, setAnalysisType] = useState<'field' | 'table' | 'impact'>('field')
  const [targetField, setTargetField] = useState<FieldReference>({
    schema: 'power_data',
    table: 'customer_info',
    column: 'customer_id'
  })
  const [targetTable, setTargetTable] = useState<TableReference>({
    schema: 'power_data',
    name: 'customer_info'
  })
  const [analysisConfig, setAnalysisConfig] = useState<LineageAnalysisConfig>({
    maxDepth: 5,
    minConfidence: 0.6,
    includeTransformations: true,
    includeSystemTables: false,
    analysisDirection: 'BOTH'
  })
  const [analysisResult, setAnalysisResult] = useState<LineageAnalysisResult | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [selectedNode, setSelectedNode] = useState<LineageGraphNode | null>(null)
  const [selectedPath, setSelectedPath] = useState<LineagePath | null>(null)

  // 预设的示例字段和表
  const exampleFields = [
    { schema: 'power_data', table: 'customer_info', column: 'customer_id', label: '客户ID' },
    { schema: 'power_data', table: 'meter_reading', column: 'reading_value', label: '抄表读数' },
    { schema: 'power_data', table: 'billing_info', column: 'amount', label: '账单金额' },
    { schema: 'audit_data', table: 'inspection_record', column: 'result_status', label: '稽查结果' },
    { schema: 'audit_data', table: 'rule_execution', column: 'confidence_score', label: '置信度分数' }
  ]

  const exampleTables = [
    { schema: 'power_data', name: 'customer_info', label: '客户信息表' },
    { schema: 'power_data', name: 'meter_reading', label: '抄表数据表' },
    { schema: 'power_data', name: 'billing_info', label: '账单信息表' },
    { schema: 'audit_data', name: 'inspection_record', label: '稽查记录表' },
    { schema: 'audit_data', name: 'rule_execution', label: '规则执行表' }
  ]

  const handleAnalyze = async () => {
    setLoading(true)
    setError(null)
    
    try {
      let result: LineageAnalysisResult
      
      switch (analysisType) {
        case 'field':
          result = await lineageAnalyzer.analyzeFieldLineage(targetField, analysisConfig)
          break
        case 'table':
          result = await lineageAnalyzer.analyzeTableLineage(targetTable, analysisConfig)
          break
        case 'impact':
          result = await lineageAnalyzer.analyzeImpact(targetField, analysisConfig)
          break
        default:
          throw new Error('未知的分析类型')
      }
      
      setAnalysisResult(result)
    } catch (err) {
      setError(err instanceof Error ? err.message : '分析失败')
    } finally {
      setLoading(false)
    }
  }

  const handleNodeClick = (node: LineageGraphNode) => {
    setSelectedNode(node)
  }

  const handleEdgeClick = (edge: LineageGraphEdge) => {
    console.log('点击边:', edge)
  }

  const handlePathSelect = (path: LineagePath) => {
    setSelectedPath(path)
  }

  const handleNodeDetailClick = (node: LineageNode) => {
    console.log('点击节点详情:', node)
  }

  const handleQuickSelect = (field: any) => {
    setTargetField({
      schema: field.schema,
      table: field.table,
      column: field.column
    })
  }

  const handleTableQuickSelect = (table: any) => {
    setTargetTable({
      schema: table.schema,
      name: table.name
    })
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold flex items-center gap-2">
            <GitBranch className="h-8 w-8 text-blue-600" />
            数据血缘溯源分析
          </h1>
          <p className="text-gray-600 mt-2">
            追踪数据的来源和去向，分析字段级和表级的血缘关系
          </p>
        </div>
        <Badge variant="outline" className="text-sm">
          DM013 功能模块
        </Badge>
      </div>

      {/* 分析配置面板 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            分析配置
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* 分析类型选择 */}
          <div className="space-y-2">
            <Label>分析类型</Label>
            <Tabs value={analysisType} onValueChange={(value) => setAnalysisType(value as any)}>
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="field" className="flex items-center gap-2">
                  <Database className="h-4 w-4" />
                  字段血缘
                </TabsTrigger>
                <TabsTrigger value="table" className="flex items-center gap-2">
                  <BarChart3 className="h-4 w-4" />
                  表血缘
                </TabsTrigger>
                <TabsTrigger value="impact" className="flex items-center gap-2">
                  <Target className="h-4 w-4" />
                  影响分析
                </TabsTrigger>
              </TabsList>
            </Tabs>
          </div>

          {/* 目标配置 */}
          {(analysisType === 'field' || analysisType === 'impact') && (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label>数据库模式</Label>
                <Input
                  value={targetField.schema}
                  onChange={(e) => setTargetField(prev => ({ ...prev, schema: e.target.value }))}
                  placeholder="例如: power_data"
                />
              </div>
              <div className="space-y-2">
                <Label>表名</Label>
                <Input
                  value={targetField.table}
                  onChange={(e) => setTargetField(prev => ({ ...prev, table: e.target.value }))}
                  placeholder="例如: customer_info"
                />
              </div>
              <div className="space-y-2">
                <Label>字段名</Label>
                <Input
                  value={targetField.column}
                  onChange={(e) => setTargetField(prev => ({ ...prev, column: e.target.value }))}
                  placeholder="例如: customer_id"
                />
              </div>
            </div>
          )}

          {analysisType === 'table' && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>数据库模式</Label>
                <Input
                  value={targetTable.schema}
                  onChange={(e) => setTargetTable(prev => ({ ...prev, schema: e.target.value }))}
                  placeholder="例如: power_data"
                />
              </div>
              <div className="space-y-2">
                <Label>表名</Label>
                <Input
                  value={targetTable.name}
                  onChange={(e) => setTargetTable(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="例如: customer_info"
                />
              </div>
            </div>
          )}

          {/* 快速选择 */}
          <div className="space-y-2">
            <Label>快速选择示例</Label>
            <div className="flex flex-wrap gap-2">
              {(analysisType === 'field' || analysisType === 'impact') && 
                exampleFields.map((field, index) => (
                  <Button
                    key={index}
                    variant="outline"
                    size="sm"
                    onClick={() => handleQuickSelect(field)}
                    className="text-xs"
                  >
                    {field.label}
                  </Button>
                ))
              }
              {analysisType === 'table' && 
                exampleTables.map((table, index) => (
                  <Button
                    key={index}
                    variant="outline"
                    size="sm"
                    onClick={() => handleTableQuickSelect(table)}
                    className="text-xs"
                  >
                    {table.label}
                  </Button>
                ))
              }
            </div>
          </div>

          {/* 分析参数 */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="space-y-2">
              <Label>最大深度</Label>
              <Select 
                value={analysisConfig.maxDepth.toString()} 
                onValueChange={(value) => setAnalysisConfig(prev => ({ ...prev, maxDepth: parseInt(value) }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="3">3层</SelectItem>
                  <SelectItem value="5">5层</SelectItem>
                  <SelectItem value="7">7层</SelectItem>
                  <SelectItem value="10">10层</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label>最小置信度</Label>
              <Select 
                value={analysisConfig.minConfidence.toString()} 
                onValueChange={(value) => setAnalysisConfig(prev => ({ ...prev, minConfidence: parseFloat(value) }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="0.3">30%</SelectItem>
                  <SelectItem value="0.5">50%</SelectItem>
                  <SelectItem value="0.6">60%</SelectItem>
                  <SelectItem value="0.8">80%</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label>分析方向</Label>
              <Select 
                value={analysisConfig.analysisDirection} 
                onValueChange={(value) => setAnalysisConfig(prev => ({ ...prev, analysisDirection: value as any }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="UPSTREAM">上游</SelectItem>
                  <SelectItem value="DOWNSTREAM">下游</SelectItem>
                  <SelectItem value="BOTH">双向</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex items-end">
              <Button 
                onClick={handleAnalyze} 
                disabled={loading}
                className="w-full"
              >
                {loading ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    分析中...
                  </>
                ) : (
                  <>
                    <Search className="h-4 w-4 mr-2" />
                    开始分析
                  </>
                )}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 错误提示 */}
      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="pt-6">
            <div className="flex items-center gap-2 text-red-600">
              <AlertCircle className="h-5 w-5" />
              <span className="font-medium">分析失败</span>
            </div>
            <p className="text-red-600 mt-1">{error}</p>
          </CardContent>
        </Card>
      )}

      {/* 分析结果 */}
      {analysisResult && (
        <div className="space-y-6">
          {/* 结果概览 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CheckCircle className="h-5 w-5 text-green-600" />
                分析结果概览
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">
                    {analysisResult.statistics.totalPaths}
                  </div>
                  <div className="text-sm text-gray-600">总路径数</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">
                    {analysisResult.statistics.averagePathLength.toFixed(1)}
                  </div>
                  <div className="text-sm text-gray-600">平均路径长度</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-600">
                    {analysisResult.statistics.highConfidencePaths}
                  </div>
                  <div className="text-sm text-gray-600">高置信度路径</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-gray-600">
                    {analysisResult.statistics.analysisTimeMs}ms
                  </div>
                  <div className="text-sm text-gray-600">分析耗时</div>
                </div>
              </div>

              {/* 建议和警告 */}
              {(analysisResult.recommendations?.length > 0 || analysisResult.warnings?.length > 0) && (
                <div className="mt-4 space-y-2">
                  {analysisResult.recommendations?.map((rec, index) => (
                    <div key={index} className="flex items-center gap-2 text-blue-600 text-sm">
                      <Info className="h-4 w-4" />
                      {rec}
                    </div>
                  ))}
                  {analysisResult.warnings?.map((warning, index) => (
                    <div key={index} className="flex items-center gap-2 text-yellow-600 text-sm">
                      <AlertCircle className="h-4 w-4" />
                      {warning}
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>

          {/* 可视化结果 */}
          <Tabs defaultValue="graph" className="w-full">
            <TabsList>
              <TabsTrigger value="graph">血缘关系图</TabsTrigger>
              <TabsTrigger value="paths">路径详情</TabsTrigger>
            </TabsList>

            <TabsContent value="graph" className="mt-4">
              <LineageGraph
                data={analysisResult.lineageGraph}
                onNodeClick={handleNodeClick}
                onEdgeClick={handleEdgeClick}
                direction={analysisConfig.analysisDirection.toLowerCase() as any}
              />
            </TabsContent>

            <TabsContent value="paths" className="mt-4">
              <LineagePathView
                paths={analysisResult.paths}
                targetField={analysisResult.targetField}
                onPathSelect={handlePathSelect}
                onNodeClick={handleNodeDetailClick}
              />
            </TabsContent>
          </Tabs>
        </div>
      )}

      {/* 节点详情面板 */}
      {selectedNode && (
        <Card>
          <CardHeader>
            <CardTitle>节点详情</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>节点标识</Label>
                  <p className="text-sm font-mono">{selectedNode.id}</p>
                </div>
                <div>
                  <Label>节点类型</Label>
                  <Badge>{selectedNode.type}</Badge>
                </div>
                <div>
                  <Label>表名</Label>
                  <p className="text-sm">{selectedNode.metadata.schema}.{selectedNode.metadata.table}</p>
                </div>
                <div>
                  <Label>字段名</Label>
                  <p className="text-sm">{selectedNode.metadata.column || 'N/A'}</p>
                </div>
                <div>
                  <Label>数据类型</Label>
                  <p className="text-sm">{selectedNode.metadata.dataType || 'N/A'}</p>
                </div>
                <div>
                  <Label>置信度</Label>
                  <Badge className={`${selectedNode.metadata.confidence > 0.8 ? 'bg-green-100 text-green-800' : selectedNode.metadata.confidence > 0.6 ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800'}`}>
                    {(selectedNode.metadata.confidence * 100).toFixed(0)}%
                  </Badge>
                </div>
              </div>
              {selectedNode.metadata.businessMeaning && (
                <div>
                  <Label>业务含义</Label>
                  <p className="text-sm text-gray-600">{selectedNode.metadata.businessMeaning}</p>
                </div>
              )}
              <div>
                <Label>相关操作</Label>
                <div className="flex gap-1 flex-wrap">
                  {selectedNode.metadata.operations.map((op, index) => (
                    <Badge key={index} variant="outline" className="text-xs">
                      {op}
                    </Badge>
                  ))}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
