"use client"

import { useState } from "react"
import { Search, Database, Table, Columns, Hash, Filter, Download, RefreshCw, GitBranch, Target } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table as DataTable, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogTrigger } from "@/components/ui/dialog"
import { Textarea } from "@/components/ui/textarea"
import dynamic from "next/dynamic"
import { lineageAnalyzer } from "@/lib/lineage-analyzer"
import type { FieldReference } from "@/types/lineage"

// d3关系图组件动态导入（避免SSR）
const D3ERGraph = dynamic(() => import("@/components/ui/er-graph"), { ssr: false })

// Mock data for different metadata types
const mockDatabases = [
  {
    id: 1,
    databaseName: "user_db",
    databaseType: "MySQL",
    host: "*************",
    port: 3306,
    schema: "user_schema",
    description: "用户相关数据存储",
    createTime: "2024-01-15 10:30:00",
    updateTime: "2024-01-20 14:20:00",
    status: "active",
    tableCount: 25,
    size: "2.5GB"
  },
  {
    id: 2,
    databaseName: "power_db",
    databaseType: "MaxCompute",
    host: "maxcompute.aliyun.com",
    port: 443,
    schema: "power_schema",
    description: "电力数据存储",
    createTime: "2024-01-10 09:15:00",
    updateTime: "2024-01-25 16:45:00",
    status: "active",
    tableCount: 18,
    size: "15.8GB"
  },
  {
    id: 3,
    databaseName: "audit_db",
    databaseType: "MySQL",
    host: "*************",
    port: 3306,
    schema: "audit_schema",
    description: "稽查规则和日志存储",
    createTime: "2024-01-12 11:20:00",
    updateTime: "2024-01-22 13:30:00",
    status: "active",
    tableCount: 12,
    size: "1.2GB"
  }
]

const mockTables = [
  {
    id: 1,
    tableName: "user_info",
    databaseName: "user_db",
    tableType: "BASE TABLE",
    engine: "InnoDB",
    rowCount: 15000,
    avgRowLength: 256,
    dataLength: "3.8MB",
    indexLength: "1.2MB",
    createTime: "2024-01-15 10:30:00",
    updateTime: "2024-01-20 14:20:00",
    comment: "用户基本信息表",
    columnCount: 15
  },
  {
    id: 2,
    tableName: "power_consumption",
    databaseName: "power_db",
    tableType: "BASE TABLE",
    engine: "MaxCompute",
    rowCount: 500000,
    avgRowLength: 128,
    dataLength: "64MB",
    indexLength: "8MB",
    createTime: "2024-01-10 09:15:00",
    updateTime: "2024-01-25 16:45:00",
    comment: "电力消耗数据表",
    columnCount: 8
  },
  {
    id: 3,
    tableName: "audit_rules",
    databaseName: "audit_db",
    tableType: "BASE TABLE",
    engine: "InnoDB",
    rowCount: 200,
    avgRowLength: 512,
    dataLength: "100KB",
    indexLength: "50KB",
    createTime: "2024-01-12 11:20:00",
    updateTime: "2024-01-22 13:30:00",
    comment: "稽查规则配置表",
    columnCount: 12
  }
]

const mockColumns = [
  {
    id: 1,
    columnName: "user_id",
    tableName: "user_info",
    databaseName: "user_db",
    dataType: "BIGINT",
    isNullable: "NO",
    columnKey: "PRI",
    columnDefault: null,
    extra: "auto_increment",
    characterSet: "utf8mb4",
    collation: "utf8mb4_unicode_ci",
    comment: "用户唯一标识",
    ordinalPosition: 1
  },
  {
    id: 2,
    columnName: "username",
    tableName: "user_info",
    databaseName: "user_db",
    dataType: "VARCHAR(50)",
    isNullable: "NO",
    columnKey: "UNI",
    columnDefault: null,
    extra: "",
    characterSet: "utf8mb4",
    collation: "utf8mb4_unicode_ci",
    comment: "用户名",
    ordinalPosition: 2
  },
  {
    id: 3,
    columnName: "meter_id",
    tableName: "power_consumption",
    databaseName: "power_db",
    dataType: "STRING",
    isNullable: "NO",
    columnKey: "PRI",
    columnDefault: null,
    extra: "",
    characterSet: "utf8",
    collation: "utf8_general_ci",
    comment: "电表编号",
    ordinalPosition: 1
  },
  {
    id: 4,
    columnName: "consumption_value",
    tableName: "power_consumption",
    databaseName: "power_db",
    dataType: "DECIMAL(10,2)",
    isNullable: "NO",
    columnKey: "",
    columnDefault: "0.00",
    extra: "",
    characterSet: null,
    collation: null,
    comment: "用电量数值",
    ordinalPosition: 2
  }
]

const mockIndexes = [
  {
    id: 1,
    indexName: "PRIMARY",
    tableName: "user_info",
    databaseName: "user_db",
    columnName: "user_id",
    nonUnique: 0,
    seqInIndex: 1,
    cardinality: 15000,
    subPart: null,
    packed: null,
    nullable: "",
    indexType: "BTREE",
    comment: ""
  },
  {
    id: 2,
    indexName: "idx_username",
    tableName: "user_info",
    databaseName: "user_db",
    columnName: "username",
    nonUnique: 0,
    seqInIndex: 1,
    cardinality: 15000,
    subPart: null,
    packed: null,
    nullable: "",
    indexType: "BTREE",
    comment: "用户名唯一索引"
  },
  {
    id: 3,
    indexName: "idx_meter_id",
    tableName: "power_consumption",
    databaseName: "power_db",
    columnName: "meter_id",
    nonUnique: 0,
    seqInIndex: 1,
    cardinality: 500000,
    subPart: null,
    packed: null,
    nullable: "",
    indexType: "BTREE",
    comment: "电表编号主键索引"
  }
]

// mock数据：用于ER图和表结构展示
const tables = [
  {
    name: "user_info",
    comment: "用户基本信息表",
    fields: [
      { name: "user_id", type: "BIGINT", comment: "用户唯一标识", relations: [] },
      { name: "username", type: "VARCHAR(50)", comment: "用户名", relations: [] },
    ],
  },
  {
    name: "power_consumption",
    comment: "电力消耗数据表",
    fields: [
      { name: "meter_id", type: "STRING", comment: "电表编号", relations: [] },
      { name: "user_id", type: "BIGINT", comment: "用户ID", relations: [ { table: "user_info", field: "user_id", type: "多对一" } ] },
      { name: "consumption_value", type: "DECIMAL(10,2)", comment: "用电量数值", relations: [] },
    ],
  },
  {
    name: "audit_rules",
    comment: "稽查规则配置表",
    fields: [
      { name: "rule_id", type: "BIGINT", comment: "规则ID", relations: [] },
      { name: "user_id", type: "BIGINT", comment: "用户ID", relations: [ { table: "user_info", field: "user_id", type: "多对一" } ] },
    ],
  },
]

// mock数据：元数据关系图谱（数据库-表-字段-索引全链路）
const metadataGraph = {
  nodes: [
    // 数据库
    { id: "user_db", type: "database", label: "user_db" },
    { id: "power_db", type: "database", label: "power_db" },
    { id: "audit_db", type: "database", label: "audit_db" },
    // 表
    { id: "user_info", type: "table", label: "user_info", db: "user_db" },
    { id: "power_consumption", type: "table", label: "power_consumption", db: "power_db" },
    { id: "audit_rules", type: "table", label: "audit_rules", db: "audit_db" },
    // 字段
    { id: "user_id_user_info", type: "field", label: "user_id", table: "user_info" },
    { id: "username_user_info", type: "field", label: "username", table: "user_info" },
    { id: "meter_id_power_consumption", type: "field", label: "meter_id", table: "power_consumption" },
    { id: "user_id_power_consumption", type: "field", label: "user_id", table: "power_consumption" },
    { id: "consumption_value_power_consumption", type: "field", label: "consumption_value", table: "power_consumption" },
    { id: "rule_id_audit_rules", type: "field", label: "rule_id", table: "audit_rules" },
    { id: "user_id_audit_rules", type: "field", label: "user_id", table: "audit_rules" },
    // 索引
    { id: "PRIMARY_user_info", type: "index", label: "PRIMARY", field: "user_id_user_info", table: "user_info" },
    { id: "idx_username_user_info", type: "index", label: "idx_username", field: "username_user_info", table: "user_info" },
    { id: "idx_meter_id_power_consumption", type: "index", label: "idx_meter_id", field: "meter_id_power_consumption", table: "power_consumption" },
  ],
  links: [
    // 库-表
    { source: "user_db", target: "user_info", type: "db-table" },
    { source: "power_db", target: "power_consumption", type: "db-table" },
    { source: "audit_db", target: "audit_rules", type: "db-table" },
    // 表-字段
    { source: "user_info", target: "user_id_user_info", type: "table-field" },
    { source: "user_info", target: "username_user_info", type: "table-field" },
    { source: "power_consumption", target: "meter_id_power_consumption", type: "table-field" },
    { source: "power_consumption", target: "user_id_power_consumption", type: "table-field" },
    { source: "power_consumption", target: "consumption_value_power_consumption", type: "table-field" },
    { source: "audit_rules", target: "rule_id_audit_rules", type: "table-field" },
    { source: "audit_rules", target: "user_id_audit_rules", type: "table-field" },
    // 字段-索引
    { source: "user_id_user_info", target: "PRIMARY_user_info", type: "field-index" },
    { source: "username_user_info", target: "idx_username_user_info", type: "field-index" },
    { source: "meter_id_power_consumption", target: "idx_meter_id_power_consumption", type: "field-index" },
  ]
}

// 过滤元数据关系图谱的展示级别
function filterMetadataGraph(level: string) {
  let nodeTypes: string[] = []
  let linkTypes: string[] = []
  if (level === "db-table") {
    nodeTypes = ["database", "table"]
    linkTypes = ["db-table"]
  } else if (level === "db-table-field") {
    nodeTypes = ["database", "table", "field"]
    linkTypes = ["db-table", "table-field"]
  } else {
    nodeTypes = ["database", "table", "field", "index"]
    linkTypes = ["db-table", "table-field", "field-index"]
  }
  const nodes = metadataGraph.nodes.filter((n) => nodeTypes.includes(n.type))
  const links = metadataGraph.links.filter((l) => linkTypes.includes(l.type))
  return { nodes, links }
}

export default function MetadataQuery() {
  const [activeTab, setActiveTab] = useState("databases")
  const [searchTerm, setSearchTerm] = useState("")
  const [databaseFilter, setDatabaseFilter] = useState("全部")
  const [selectedItem, setSelectedItem] = useState<any>(null)
  const [sqlQuery, setSqlQuery] = useState("")
  const [tab, setTab] = useState("table")
  const [selectedField, setSelectedField] = useState<any>(null)
  const [graphView, setGraphView] = useState("er") // "er" | "metadata"
  const [graphLevel, setGraphLevel] = useState("db-table-field-index") // "db-table" | "db-table-field" | "db-table-field-index"
  const [lineageAnalysisLoading, setLineageAnalysisLoading] = useState(false)

  // Filter functions for different metadata types
  const filterDatabases = () => {
    return mockDatabases.filter((db) => {
      const matchesSearch = searchTerm === "" || 
        db.databaseName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        db.description.toLowerCase().includes(searchTerm.toLowerCase())
      const matchesType = databaseFilter === "全部" || db.databaseType === databaseFilter
      return matchesSearch && matchesType
    })
  }

  const filterTables = () => {
    return mockTables.filter((table) => {
      const matchesSearch = searchTerm === "" || 
        table.tableName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        table.comment.toLowerCase().includes(searchTerm.toLowerCase())
      const matchesDatabase = databaseFilter === "全部" || table.databaseName === databaseFilter
      return matchesSearch && matchesDatabase
    })
  }

  // 血缘分析处理函数
  const handleLineageAnalysis = async (field: any, table: any, direction: 'upstream' | 'downstream' | 'both') => {
    setLineageAnalysisLoading(true)

    try {
      const fieldRef: FieldReference = {
        schema: table.databaseName || 'default_schema',
        table: table.name || table.tableName,
        column: field.name
      }

      const config = {
        maxDepth: 5,
        minConfidence: 0.6,
        includeTransformations: true,
        includeSystemTables: false,
        analysisDirection: direction.toUpperCase() as any
      }

      let result
      if (direction === 'upstream') {
        const paths = await lineageAnalyzer.traceWritePath(fieldRef, config)
        console.log('上游血缘路径:', paths)
        alert(`发现 ${paths.length} 条上游血缘路径，详情请查看控制台`)
      } else if (direction === 'downstream') {
        const paths = await lineageAnalyzer.traceUsagePath(fieldRef, config)
        console.log('下游血缘路径:', paths)
        alert(`发现 ${paths.length} 条下游血缘路径，详情请查看控制台`)
      } else {
        result = await lineageAnalyzer.analyzeFieldLineage(fieldRef, config)
        console.log('完整血缘分析结果:', result)
        alert(`血缘分析完成，发现 ${result.paths.length} 条路径，详情请查看控制台`)
      }

      // 可以在这里添加跳转到血缘分析页面的逻辑
      // window.open(`/data-lineage-analysis?field=${encodeURIComponent(JSON.stringify(fieldRef))}`, '_blank')

    } catch (error) {
      console.error('血缘分析失败:', error)
      alert('血缘分析失败，请稍后重试')
    } finally {
      setLineageAnalysisLoading(false)
    }
  }

  const filterColumns = () => {
    return mockColumns.filter((column) => {
      const matchesSearch = searchTerm === "" || 
        column.columnName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        column.tableName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        column.comment.toLowerCase().includes(searchTerm.toLowerCase())
      const matchesDatabase = databaseFilter === "全部" || column.databaseName === databaseFilter
      return matchesSearch && matchesDatabase
    })
  }

  const filterIndexes = () => {
    return mockIndexes.filter((index) => {
      const matchesSearch = searchTerm === "" || 
        index.indexName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        index.tableName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        index.columnName.toLowerCase().includes(searchTerm.toLowerCase())
      const matchesDatabase = databaseFilter === "全部" || index.databaseName === databaseFilter
      return matchesSearch && matchesDatabase
    })
  }

  const handleItemClick = (item: any) => {
    setSelectedItem(item)
  }

  const generateSQLQuery = () => {
    const queries = {
      databases: "SELECT * FROM information_schema.SCHEMATA WHERE SCHEMA_NAME LIKE '%'",
      tables: "SELECT * FROM information_schema.TABLES WHERE TABLE_SCHEMA LIKE '%'",
      columns: "SELECT * FROM information_schema.COLUMNS WHERE TABLE_SCHEMA LIKE '%'",
      indexes: "SELECT * FROM information_schema.STATISTICS WHERE TABLE_SCHEMA LIKE '%'"
    }
    setSqlQuery(queries[activeTab as keyof typeof queries])
  }

  const exportData = () => {
    // Mock export functionality
    console.log("Exporting data...")
  }

  return (
    <div className="flex-1 space-y-6 p-6">
      {/* Page Header */}
      <div className="space-y-2">
        <h1 className="text-3xl font-bold tracking-tight text-gray-900">元数据查询</h1>
        <p className="text-gray-600">查询和管理系统中的各种元数据信息</p>
      </div>

      {/* Controls Section */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="搜索元数据..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={databaseFilter} onValueChange={setDatabaseFilter}>
              <SelectTrigger className="w-full sm:w-48">
                <SelectValue placeholder="数据库筛选" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="全部">全部数据库</SelectItem>
                <SelectItem value="user_db">user_db</SelectItem>
                <SelectItem value="power_db">power_db</SelectItem>
                <SelectItem value="audit_db">audit_db</SelectItem>
              </SelectContent>
            </Select>
            <Button onClick={generateSQLQuery} variant="outline">
              <RefreshCw className="mr-2 h-4 w-4" />
              生成SQL
            </Button>
            <Button onClick={exportData} variant="outline">
              <Download className="mr-2 h-4 w-4" />
              导出
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* SQL Query Display */}
      {sqlQuery && (
        <Card>
          <CardHeader>
            <CardTitle>生成的SQL查询</CardTitle>
          </CardHeader>
          <CardContent>
            <Textarea
              value={sqlQuery}
              readOnly
              className="font-mono text-sm"
              rows={3}
            />
          </CardContent>
        </Card>
      )}

      {/* Main Content - Tabs */}
      <Card>
        <CardHeader>
          <CardTitle>元数据查询</CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs value={tab} onValueChange={setTab}>
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="databases" className="flex items-center gap-2">
                <Database className="h-4 w-4" />
                数据库
              </TabsTrigger>
              <TabsTrigger value="tables" className="flex items-center gap-2">
                <Table className="h-4 w-4" />
                数据表
              </TabsTrigger>
              <TabsTrigger value="columns" className="flex items-center gap-2">
                <Columns className="h-4 w-4" />
                字段
              </TabsTrigger>
              <TabsTrigger value="indexes" className="flex items-center gap-2">
                <Hash className="h-4 w-4" />
                索引
              </TabsTrigger>
            </TabsList>

            <TabsContent value="databases" className="space-y-4">
              <div className="rounded-md border">
                <DataTable>
                  <TableHeader>
                    <TableRow>
                      <TableHead>数据库名称</TableHead>
                      <TableHead>类型</TableHead>
                      <TableHead>主机</TableHead>
                      <TableHead>端口</TableHead>
                      <TableHead>描述</TableHead>
                      <TableHead>表数量</TableHead>
                      <TableHead>大小</TableHead>
                      <TableHead>状态</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filterDatabases().map((db) => (
                      <TableRow key={db.id} className="cursor-pointer hover:bg-gray-50" onClick={() => handleItemClick(db)}>
                        <TableCell className="font-medium">{db.databaseName}</TableCell>
                        <TableCell>
                          <Badge variant={db.databaseType === "MySQL" ? "default" : "secondary"}>
                            {db.databaseType}
                          </Badge>
                        </TableCell>
                        <TableCell>{db.host}</TableCell>
                        <TableCell>{db.port}</TableCell>
                        <TableCell>{db.description}</TableCell>
                        <TableCell>{db.tableCount}</TableCell>
                        <TableCell>{db.size}</TableCell>
                        <TableCell>
                          <Badge variant={db.status === "active" ? "default" : "destructive"}>
                            {db.status}
                          </Badge>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </DataTable>
              </div>
            </TabsContent>

            <TabsContent value="tables" className="space-y-4">
              <div className="rounded-md border">
                <DataTable>
                  <TableHeader>
                    <TableRow>
                      <TableHead>表名称</TableHead>
                      <TableHead>数据库</TableHead>
                      <TableHead>类型</TableHead>
                      <TableHead>引擎</TableHead>
                      <TableHead>行数</TableHead>
                      <TableHead>数据大小</TableHead>
                      <TableHead>索引大小</TableHead>
                      <TableHead>描述</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filterTables().map((table) => (
                      <TableRow key={table.id} className="cursor-pointer hover:bg-gray-50" onClick={() => handleItemClick(table)}>
                        <TableCell className="font-medium">{table.tableName}</TableCell>
                        <TableCell>{table.databaseName}</TableCell>
                        <TableCell>{table.tableType}</TableCell>
                        <TableCell>{table.engine}</TableCell>
                        <TableCell>{table.rowCount.toLocaleString()}</TableCell>
                        <TableCell>{table.dataLength}</TableCell>
                        <TableCell>{table.indexLength}</TableCell>
                        <TableCell>{table.comment}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </DataTable>
              </div>
            </TabsContent>

            <TabsContent value="columns" className="space-y-4">
              <div className="rounded-md border">
                <DataTable>
                  <TableHeader>
                    <TableRow>
                      <TableHead>字段名</TableHead>
                      <TableHead>表名</TableHead>
                      <TableHead>数据库</TableHead>
                      <TableHead>数据类型</TableHead>
                      <TableHead>是否为空</TableHead>
                      <TableHead>键类型</TableHead>
                      <TableHead>默认值</TableHead>
                      <TableHead>描述</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filterColumns().map((column) => (
                      <TableRow key={column.id} className="cursor-pointer hover:bg-gray-50" onClick={() => handleItemClick(column)}>
                        <TableCell className="font-medium">{column.columnName}</TableCell>
                        <TableCell>{column.tableName}</TableCell>
                        <TableCell>{column.databaseName}</TableCell>
                        <TableCell>
                          <code className="bg-gray-100 px-2 py-1 rounded text-sm">{column.dataType}</code>
                        </TableCell>
                        <TableCell>
                          <Badge variant={column.isNullable === "NO" ? "destructive" : "default"}>
                            {column.isNullable}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          {column.columnKey && (
                            <Badge variant={column.columnKey === "PRI" ? "default" : "secondary"}>
                              {column.columnKey}
                            </Badge>
                          )}
                        </TableCell>
                        <TableCell>{column.columnDefault || "-"}</TableCell>
                        <TableCell>{column.comment}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </DataTable>
              </div>
            </TabsContent>

            <TabsContent value="indexes" className="space-y-4">
              <div className="rounded-md border">
                <DataTable>
                  <TableHeader>
                    <TableRow>
                      <TableHead>索引名</TableHead>
                      <TableHead>表名</TableHead>
                      <TableHead>数据库</TableHead>
                      <TableHead>字段名</TableHead>
                      <TableHead>唯一性</TableHead>
                      <TableHead>基数</TableHead>
                      <TableHead>索引类型</TableHead>
                      <TableHead>注释</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filterIndexes().map((index) => (
                      <TableRow key={index.id} className="cursor-pointer hover:bg-gray-50" onClick={() => handleItemClick(index)}>
                        <TableCell className="font-medium">{index.indexName}</TableCell>
                        <TableCell>{index.tableName}</TableCell>
                        <TableCell>{index.databaseName}</TableCell>
                        <TableCell>{index.columnName}</TableCell>
                        <TableCell>
                          <Badge variant={index.nonUnique === 0 ? "default" : "secondary"}>
                            {index.nonUnique === 0 ? "唯一" : "非唯一"}
                          </Badge>
                        </TableCell>
                        <TableCell>{index.cardinality.toLocaleString()}</TableCell>
                        <TableCell>{index.indexType}</TableCell>
                        <TableCell>{index.comment || "-"}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </DataTable>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Detail Dialog */}
      <Dialog open={!!selectedItem} onOpenChange={() => setSelectedItem(null)}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>元数据详情</DialogTitle>
          </DialogHeader>
          {selectedItem && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                {Object.entries(selectedItem).map(([key, value]) => (
                  <div key={key} className="space-y-1">
                    <label className="text-sm font-medium text-gray-500 capitalize">
                      {key.replace(/([A-Z])/g, ' $1').trim()}
                    </label>
                    <p className="text-sm">{String(value)}</p>
                  </div>
                ))}
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      <Tabs value={tab} onValueChange={setTab}>
        <TabsList>
          <TabsTrigger value="table">表结构列表</TabsTrigger>
          <TabsTrigger value="er">图谱</TabsTrigger>
        </TabsList>
        <TabsContent value="table">
          <Card>
            <CardHeader>
              <CardTitle>表结构与字段关联</CardTitle>
            </CardHeader>
            <CardContent>
              {tables.map((table) => (
                <div key={table.name} className="mb-6">
                  <div className="font-bold text-lg mb-2">{table.name} <span className="text-gray-500 text-sm">{table.comment}</span></div>
                  <table className="w-full text-sm border mb-2">
                    <thead>
                      <tr className="bg-gray-50">
                        <th className="p-2 border">字段名</th>
                        <th className="p-2 border">类型</th>
                        <th className="p-2 border">说明</th>
                        <th className="p-2 border">关联字段</th>
                        <th className="p-2 border">操作</th>
                      </tr>
                    </thead>
                    <tbody>
                      {table.fields.map((field) => (
                        <tr key={field.name}>
                          <td className="p-2 border">{field.name}</td>
                          <td className="p-2 border">{field.type}</td>
                          <td className="p-2 border">{field.comment}</td>
                          <td className="p-2 border">
                            {field.relations.length > 0 ? field.relations.map((rel, idx) => (
                              <span key={idx} className="inline-block bg-blue-50 text-blue-700 px-2 py-0.5 rounded mr-1">
                                {rel.table}.{rel.field} <span className="text-xs">({rel.type})</span>
                              </span>
                            )) : <span className="text-gray-400">-</span>}
                          </td>
                          <td className="p-2 border">
                            <Dialog>
                              <DialogTrigger asChild>
                                <Button size="sm" variant="outline" onClick={() => setSelectedField({ ...field, table: table.name })}>详情</Button>
                              </DialogTrigger>
                              <DialogContent>
                                <DialogHeader>
                                  <DialogTitle>字段详情：{field.name}</DialogTitle>
                                  <DialogDescription>{field.comment}</DialogDescription>
                                </DialogHeader>
                                <div className="mt-2 text-sm space-y-3">
                                  <div>所属表：<b>{table.name}</b></div>
                                  <div>类型：{field.type}</div>
                                  <div>说明：{field.comment}</div>
                                  <div className="mt-2">本字段关联：</div>
                                  <ul className="list-disc ml-6">
                                    {field.relations.length > 0 ? field.relations.map((rel, idx) => (
                                      <li key={idx}>{rel.table}.{rel.field} <span className="text-xs">({rel.type})</span></li>
                                    )) : <li className="text-gray-400">无</li>}
                                  </ul>

                                  {/* 血缘分析功能 */}
                                  <div className="border-t pt-3 mt-3">
                                    <div className="font-medium mb-2 flex items-center gap-2">
                                      <GitBranch className="h-4 w-4" />
                                      数据血缘分析
                                    </div>
                                    <div className="flex gap-2 flex-wrap">
                                      <Button
                                        size="sm"
                                        variant="outline"
                                        onClick={() => handleLineageAnalysis(field, table, 'upstream')}
                                        className="flex items-center gap-1"
                                      >
                                        <Target className="h-3 w-3" />
                                        上游分析
                                      </Button>
                                      <Button
                                        size="sm"
                                        variant="outline"
                                        onClick={() => handleLineageAnalysis(field, table, 'downstream')}
                                        className="flex items-center gap-1"
                                      >
                                        <Target className="h-3 w-3" />
                                        下游分析
                                      </Button>
                                      <Button
                                        size="sm"
                                        variant="outline"
                                        onClick={() => handleLineageAnalysis(field, table, 'both')}
                                        className="flex items-center gap-1"
                                      >
                                        <GitBranch className="h-3 w-3" />
                                        完整血缘
                                      </Button>
                                    </div>
                                  </div>
                                </div>
                              </DialogContent>
                            </Dialog>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ))}
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="er">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <CardTitle>元数据关系图谱</CardTitle>
                <div className="flex gap-2">
                  <Select value={graphView} onValueChange={setGraphView}>
                    <SelectTrigger className="w-36"><SelectValue /></SelectTrigger>
                    <SelectContent>
                      <SelectItem value="er">表关系图谱</SelectItem>
                      <SelectItem value="metadata">元数据关系图谱</SelectItem>
                    </SelectContent>
                  </Select>
                  {graphView === "metadata" && (
                    <Select value={graphLevel} onValueChange={setGraphLevel}>
                      <SelectTrigger className="w-44"><SelectValue /></SelectTrigger>
                      <SelectContent>
                        <SelectItem value="db-table">仅库-表</SelectItem>
                        <SelectItem value="db-table-field">库-表-字段</SelectItem>
                        <SelectItem value="db-table-field-index">库-表-字段-索引</SelectItem>
                      </SelectContent>
                    </Select>
                  )}
                </div>
              </div>
            </CardHeader>
            <CardContent>
              {graphView === "er" ? (
                <D3ERGraph tables={tables} />
              ) : (
                <D3ERGraph graph={filterMetadataGraph(graphLevel)} />
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
} 