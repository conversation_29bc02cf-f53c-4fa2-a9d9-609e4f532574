/*
 * @Author: king<PERSON>y
 * @Date: 2025-06-27 15:28:27
 * @LastEditTime: 2025-06-27 15:34:41
 * @LastEditors: kingasky
 * @Description: 
 * @FilePath: \datamind-web\app\dashboard\main.tsx
 */
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { BarChart3, Database, FileText, Plus, TrendingUp } from "lucide-react"

export default function Main() {
  return (
    <div className="flex-1 space-y-6 p-6">
      {/* Welcome Message */}
      <div className="space-y-2">
        <h1 className="text-3xl font-bold tracking-tight text-gray-900">欢迎使用电力数据大脑！</h1>
        <p className="text-gray-600">智能化数据稽查与治理平台</p>
      </div>
      {/* Summary Cards */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card className="border-l-4 border-l-blue-500">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-700">今日稽查概览</CardTitle>
            <FileText className="h-4 w-4 text-blue-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-900">1条</div>
            <p className="text-xs text-gray-600 mt-1">今日新增稽查规则</p>
          </CardContent>
        </Card>
        <Card className="border-l-4 border-l-green-500">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-700">今日执行情况</CardTitle>
            <TrendingUp className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-900">5条</div>
            <p className="text-xs text-gray-600 mt-1">今日执行规则</p>
          </CardContent>
        </Card>
        <Card className="border-l-4 border-l-orange-500">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-700">问题命中汇总</CardTitle>
            <BarChart3 className="h-4 w-4 text-orange-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-900">120个</div>
            <p className="text-xs text-gray-600 mt-1">命中问题总数</p>
          </CardContent>
        </Card>
      </div>
      {/* Action Buttons */}
      <div className="flex flex-col sm:flex-row gap-4">
        <Button size="lg" className="flex-1 h-12 bg-blue-600 hover:bg-blue-700 text-white font-medium">
          <Plus className="mr-2 h-5 w-5" />
          开始设计新稽查规则
        </Button>
        <Button
          size="lg"
          variant="outline"
          className="flex-1 h-12 border-gray-300 text-gray-700 hover:bg-gray-50 font-medium"
        >
          <Database className="mr-2 h-5 w-5" />
          查看所有元数据
        </Button>
      </div>
      {/* Future Features Placeholders */}
      <div className="space-y-4">
        <h2 className="text-xl font-semibold text-gray-900">即将推出</h2>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          <Card className="border-2 border-dashed border-gray-300 bg-gray-50/50">
            <CardHeader className="text-center pb-4">
              <div className="mx-auto w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center mb-3">
                <BarChart3 className="h-6 w-6 text-gray-400" />
              </div>
              <CardTitle className="text-sm font-medium text-gray-600">数据治理健康度评分</CardTitle>
            </CardHeader>
            <CardContent className="text-center">
              <p className="text-xs text-gray-500">未来功能</p>
              <p className="text-xs text-gray-400 mt-1">全面评估数据质量状况</p>
            </CardContent>
          </Card>
          <Card className="border-2 border-dashed border-gray-300 bg-gray-50/50">
            <CardHeader className="text-center pb-4">
              <div className="mx-auto w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center mb-3">
                <TrendingUp className="h-6 w-6 text-gray-400" />
              </div>
              <CardTitle className="text-sm font-medium text-gray-600">稽查风险Top N</CardTitle>
            </CardHeader>
            <CardContent className="text-center">
              <p className="text-xs text-gray-500">未来功能</p>
              <p className="text-xs text-gray-400 mt-1">识别高风险数据问题</p>
            </CardContent>
          </Card>
          <Card className="border-2 border-dashed border-gray-300 bg-gray-50/50 md:col-span-2 lg:col-span-1">
            <CardHeader className="text-center pb-4">
              <div className="mx-auto w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center mb-3">
                <FileText className="h-6 w-6 text-gray-400" />
              </div>
              <CardTitle className="text-sm font-medium text-gray-600">最新稽查报告摘要</CardTitle>
            </CardHeader>
            <CardContent className="text-center">
              <p className="text-xs text-gray-500">未来功能</p>
              <p className="text-xs text-gray-400 mt-1">自动生成稽查报告</p>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
} 