"use client"

import type React from "react"
import { useState } from "react"
import type { Issue, IssueClue, TraceFlowData } from "@/types/issue"
import { Eye, Search, AlertTriangle, Clock, TrendingUp } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import TraceFlowModal from "@/components/trace-flow-modal"

interface IssueHitsProps {
  issue: Issue
}

const IssueHits: React.FC<IssueHitsProps> = ({ issue }) => {
  const [isTraceModalOpen, setIsTraceModalOpen] = useState(false)
  const [selectedClue, setSelectedClue] = useState<IssueClue | null>(null)

  const handleClueClick = (clue: IssueClue) => {
    setSelectedClue(clue)
    setIsTraceModalOpen(true)
  }

  const generateTraceFlowData = (clue: IssueClue): TraceFlowData => {
    return {
      issue,
      selectedClue: clue,
      flowSteps: [
        {
          id: "1",
          stepType: "rule_trigger",
          title: "规则触发",
          description: `规则 "${clue.ruleHit.ruleName}" 被触发，开始执行数据检查`,
          details: {
            ruleId: clue.ruleHit.ruleId,
            themeName: clue.ruleHit.themeName,
            triggerTime: clue.ruleHit.hitTime
          },
          timestamp: clue.ruleHit.hitTime,
          order: 1
        },
        {
          id: "2",
          stepType: "logic_evaluation",
          title: "逻辑评估",
          description: "执行规则中的逻辑步骤，逐条验证条件",
          details: {
            totalSteps: clue.ruleHit.logicSteps.length,
            hitSteps: clue.ruleHit.logicSteps.filter(step => step.isHit).length
          },
          timestamp: clue.ruleHit.hitTime,
          order: 2
        },
        {
          id: "3",
          stepType: "data_validation",
          title: "数据验证",
          description: `验证数据源 "${clue.ruleHit.dataSource}" 中的 "${clue.ruleHit.tableName}.${clue.ruleHit.columnName}"`,
          details: {
            dataSource: clue.ruleHit.dataSource,
            tableName: clue.ruleHit.tableName,
            columnName: clue.ruleHit.columnName,
            condition: clue.ruleHit.condition
          },
          timestamp: clue.ruleHit.hitTime,
          order: 3
        },
        {
          id: "4",
          stepType: "clue_generation",
          title: "线索生成",
          description: "基于规则命中结果生成问题线索",
          details: {
            confidence: clue.confidence,
            evidence: clue.evidence,
            clueId: clue.id
          },
          timestamp: clue.createdAt,
          order: 4
        }
      ]
    }
  }

  return (
    <>
      <Card className="mb-6">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <AlertTriangle className="h-5 w-5 text-red-500" />
              <CardTitle className="text-lg">{issue.title}</CardTitle>
              <Badge variant={issue.severity === 'critical' ? 'destructive' : 'secondary'}>
                {issue.severity}
              </Badge>
              <Badge variant={issue.status === 'active' ? 'destructive' : 'default'}>
                {issue.status}
              </Badge>
            </div>
            <div className="flex items-center space-x-2 text-sm text-gray-500">
              <Clock className="h-4 w-4" />
              <span>{issue.createdAt}</span>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <p className="text-gray-600 mb-4">{issue.description}</p>
          
          {/* 线索列表 */}
          <div className="space-y-3">
            <div className="flex items-center space-x-2">
              <Search className="h-4 w-4 text-gray-500" />
              <h4 className="font-medium">问题线索 ({issue.clues.length})</h4>
            </div>
            
            {issue.clues.length > 0 ? (
              <div className="space-y-2">
                {issue.clues.map((clue) => (
                  <div
                    key={clue.id}
                    className="p-3 border rounded-lg hover:bg-gray-50 cursor-pointer transition-colors"
                    onClick={() => handleClueClick(clue)}
                  >
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center space-x-2">
                        <TrendingUp className="h-4 w-4 text-blue-500" />
                        <h5 className="font-medium text-sm">{clue.title}</h5>
                        <Badge variant="outline" className="text-xs">
                          置信度: {clue.confidence}%
                        </Badge>
                      </div>
                      <div className="text-xs text-gray-500">
                        {clue.createdAt}
                      </div>
                    </div>
                    <p className="text-sm text-gray-600 mb-2">{clue.description}</p>
                    <div className="flex items-center space-x-4 text-xs text-gray-500">
                      <span>规则: {clue.ruleHit.ruleName}</span>
                      <span>主题: {clue.ruleHit.themeName}</span>
                      <span>数据源: {clue.ruleHit.dataSource}</span>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-4 text-gray-500">
                暂无问题线索
              </div>
            )}
          </div>

          <Separator className="my-4" />
          
          <div className="flex justify-between items-center">
            <div className="text-sm text-gray-500">
              最后更新: {issue.updatedAt}
            </div>
            <Button
              size="sm"
              variant="outline"
              onClick={() => (window.location.href = `/problem-report?problemId=${issue.id}`)}
            >
              <Eye className="h-3 w-3 mr-1" />
              查看详情
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* 线索溯源流程图弹窗 */}
      {selectedClue && (
        <TraceFlowModal
          isOpen={isTraceModalOpen}
          onClose={() => {
            setIsTraceModalOpen(false)
            setSelectedClue(null)
          }}
          data={generateTraceFlowData(selectedClue)}
        />
      )}
    </>
  )
}

export default IssueHits
