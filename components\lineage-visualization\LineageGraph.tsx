"use client"

import { useEffect, useRef, useState } from "react"
import * as d3 from "d3"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Slider } from "@/components/ui/slider"
import { 
  Network, 
  ZoomIn, 
  ZoomOut, 
  RotateCcw, 
  Download,
  Filter,
  Eye,
  EyeOff,
  GitBranch,
  Database,
  ArrowRight,
  ArrowLeft,
  Target
} from "lucide-react"
import type { LineageGraph, LineageGraphNode, LineageGraphEdge } from "@/types/lineage"

interface LineageGraphProps {
  data: LineageGraph
  width?: number
  height?: number
  onNodeClick?: (node: LineageGraphNode) => void
  onEdgeClick?: (edge: LineageGraphEdge) => void
  direction?: 'upstream' | 'downstream' | 'both'
}

export default function LineageGraph({
  data,
  width = 1000,
  height = 700,
  onNodeClick,
  onEdgeClick,
  direction = 'both'
}: LineageGraphProps) {
  const svgRef = useRef<SVGSVGElement>(null)
  const [confidenceThreshold, setConfidenceThreshold] = useState([0.5])
  const [selectedNodeTypes, setSelectedNodeTypes] = useState<string[]>(['TABLE', 'FIELD', 'TRANSFORMATION'])
  const [zoomLevel, setZoomLevel] = useState(1)
  const [showLabels, setShowLabels] = useState(true)
  const [selectedNode, setSelectedNode] = useState<string | null>(null)

  // 过滤数据
  const filteredData = {
    ...data,
    nodes: data.nodes.filter(node => {
      const confidenceMatch = node.metadata.confidence >= confidenceThreshold[0]
      const typeMatch = selectedNodeTypes.includes(node.type)
      return confidenceMatch && typeMatch
    }),
    edges: data.edges.filter(edge => {
      const confidenceMatch = edge.confidence >= confidenceThreshold[0]
      const sourceExists = data.nodes.some(n => n.id === edge.source && n.metadata.confidence >= confidenceThreshold[0])
      const targetExists = data.nodes.some(n => n.id === edge.target && n.metadata.confidence >= confidenceThreshold[0])
      return confidenceMatch && sourceExists && targetExists
    })
  }

  useEffect(() => {
    if (!svgRef.current || !filteredData.nodes.length) return

    // 清除之前的内容
    d3.select(svgRef.current).selectAll("*").remove()

    const svg = d3.select(svgRef.current)
    const container = svg.append("g")

    // 定义箭头标记
    svg.append("defs").selectAll("marker")
      .data(["arrow"])
      .enter().append("marker")
      .attr("id", d => d)
      .attr("viewBox", "0 -5 10 10")
      .attr("refX", 15)
      .attr("refY", 0)
      .attr("markerWidth", 6)
      .attr("markerHeight", 6)
      .attr("orient", "auto")
      .append("path")
      .attr("d", "M0,-5L10,0L0,5")
      .attr("fill", "#666")

    // 设置缩放行为
    const zoom = d3.zoom<SVGSVGElement, unknown>()
      .scaleExtent([0.1, 4])
      .on("zoom", (event) => {
        container.attr("transform", event.transform)
        setZoomLevel(event.transform.k)
      })

    svg.call(zoom)

    // 创建力导向图
    const simulation = d3.forceSimulation(filteredData.nodes as any)
      .force("link", d3.forceLink(filteredData.edges)
        .id((d: any) => d.id)
        .distance(120)
        .strength(0.6)
      )
      .force("charge", d3.forceManyBody().strength(-400))
      .force("center", d3.forceCenter(width / 2, height / 2))
      .force("collision", d3.forceCollide().radius(60))

    // 绘制连接线
    const links = container.append("g")
      .selectAll("line")
      .data(filteredData.edges)
      .enter()
      .append("line")
      .attr("stroke", (d: LineageGraphEdge) => d.style?.color || "#999")
      .attr("stroke-width", (d: LineageGraphEdge) => d.style?.width || 2)
      .attr("stroke-dasharray", (d: LineageGraphEdge) => d.style?.dashArray || null)
      .attr("marker-end", "url(#arrow)")
      .style("cursor", "pointer")
      .on("click", (event, d) => {
        event.stopPropagation()
        onEdgeClick?.(d)
      })
      .on("mouseover", function(event, d) {
        d3.select(this).attr("stroke-width", (d.style?.width || 2) + 1)
        
        // 显示tooltip
        const tooltip = d3.select("body").append("div")
          .attr("class", "tooltip")
          .style("position", "absolute")
          .style("background", "rgba(0, 0, 0, 0.8)")
          .style("color", "white")
          .style("padding", "8px")
          .style("border-radius", "4px")
          .style("font-size", "12px")
          .style("pointer-events", "none")
          .style("z-index", "1000")
          .html(`
            <div><strong>${d.type}</strong></div>
            <div>置信度: ${(d.confidence * 100).toFixed(1)}%</div>
            <div>${d.metadata.relationshipType}</div>
          `)
          .style("left", (event.pageX + 10) + "px")
          .style("top", (event.pageY - 10) + "px")
      })
      .on("mouseout", function(event, d) {
        d3.select(this).attr("stroke-width", d.style?.width || 2)
        d3.selectAll(".tooltip").remove()
      })

    // 绘制节点
    const nodes = container.append("g")
      .selectAll("circle")
      .data(filteredData.nodes)
      .enter()
      .append("circle")
      .attr("r", (d: LineageGraphNode) => d.style?.size || 25)
      .attr("fill", (d: LineageGraphNode) => d.style?.color || "#69b3a2")
      .attr("stroke", (d: LineageGraphNode) => selectedNode === d.id ? "#000" : "#fff")
      .attr("stroke-width", (d: LineageGraphNode) => selectedNode === d.id ? 3 : 2)
      .style("cursor", "pointer")
      .call(d3.drag<any, any>()
        .on("start", dragstarted)
        .on("drag", dragged)
        .on("end", dragended)
      )
      .on("click", (event, d) => {
        event.stopPropagation()
        setSelectedNode(selectedNode === d.id ? null : d.id)
        onNodeClick?.(d)
      })
      .on("mouseover", function(event, d) {
        d3.select(this).attr("r", (d.style?.size || 25) + 5)
        
        // 显示tooltip
        const tooltip = d3.select("body").append("div")
          .attr("class", "tooltip")
          .style("position", "absolute")
          .style("background", "rgba(0, 0, 0, 0.8)")
          .style("color", "white")
          .style("padding", "12px")
          .style("border-radius", "4px")
          .style("font-size", "12px")
          .style("pointer-events", "none")
          .style("z-index", "1000")
          .html(`
            <div><strong>${d.label}</strong></div>
            <div>类型: ${d.type}</div>
            <div>置信度: ${(d.metadata.confidence * 100).toFixed(1)}%</div>
            <div>数据类型: ${d.metadata.dataType || 'N/A'}</div>
            <div>操作: ${d.metadata.operations.join(', ')}</div>
            ${d.metadata.businessMeaning ? `<div>业务含义: ${d.metadata.businessMeaning}</div>` : ''}
          `)
          .style("left", (event.pageX + 10) + "px")
          .style("top", (event.pageY - 10) + "px")
      })
      .on("mouseout", function(event, d) {
        d3.select(this).attr("r", d.style?.size || 25)
        d3.selectAll(".tooltip").remove()
      })

    // 添加节点标签
    const labels = container.append("g")
      .selectAll("text")
      .data(filteredData.nodes)
      .enter()
      .append("text")
      .text((d: LineageGraphNode) => showLabels ? d.label : "")
      .attr("font-size", "12px")
      .attr("font-weight", "bold")
      .attr("text-anchor", "middle")
      .attr("dy", ".35em")
      .attr("fill", "#333")
      .style("pointer-events", "none")

    // 添加边标签
    const edgeLabels = container.append("g")
      .selectAll("text")
      .data(filteredData.edges)
      .enter()
      .append("text")
      .text((d: LineageGraphEdge) => showLabels && d.label ? d.label : "")
      .attr("font-size", "10px")
      .attr("text-anchor", "middle")
      .attr("fill", "#666")
      .style("pointer-events", "none")

    // 更新位置
    simulation.on("tick", () => {
      links
        .attr("x1", (d: any) => d.source.x)
        .attr("y1", (d: any) => d.source.y)
        .attr("x2", (d: any) => d.target.x)
        .attr("y2", (d: any) => d.target.y)

      nodes
        .attr("cx", (d: any) => d.x)
        .attr("cy", (d: any) => d.y)

      labels
        .attr("x", (d: any) => d.x)
        .attr("y", (d: any) => d.y)

      edgeLabels
        .attr("x", (d: any) => (d.source.x + d.target.x) / 2)
        .attr("y", (d: any) => (d.source.y + d.target.y) / 2)
    })

    // 拖拽函数
    function dragstarted(event: any) {
      if (!event.active) simulation.alphaTarget(0.3).restart()
      event.subject.fx = event.subject.x
      event.subject.fy = event.subject.y
    }

    function dragged(event: any) {
      event.subject.fx = event.x
      event.subject.fy = event.y
    }

    function dragended(event: any) {
      if (!event.active) simulation.alphaTarget(0)
      event.subject.fx = null
      event.subject.fy = null
    }

    // 清理函数
    return () => {
      simulation.stop()
      d3.selectAll(".tooltip").remove()
    }
  }, [filteredData, width, height, showLabels, selectedNode])

  const handleZoomIn = () => {
    const svg = d3.select(svgRef.current)
    svg.transition().call(
      d3.zoom<SVGSVGElement, unknown>().scaleBy as any,
      1.5
    )
  }

  const handleZoomOut = () => {
    const svg = d3.select(svgRef.current)
    svg.transition().call(
      d3.zoom<SVGSVGElement, unknown>().scaleBy as any,
      1 / 1.5
    )
  }

  const handleReset = () => {
    const svg = d3.select(svgRef.current)
    svg.transition().call(
      d3.zoom<SVGSVGElement, unknown>().transform as any,
      d3.zoomIdentity
    )
    setZoomLevel(1)
    setSelectedNode(null)
  }

  const handleExport = () => {
    const svgElement = svgRef.current
    if (!svgElement) return

    const serializer = new XMLSerializer()
    const svgString = serializer.serializeToString(svgElement)
    const blob = new Blob([svgString], { type: 'image/svg+xml' })
    const url = URL.createObjectURL(blob)
    
    const link = document.createElement('a')
    link.href = url
    link.download = 'lineage-graph.svg'
    link.click()
    
    URL.revokeObjectURL(url)
  }

  const toggleNodeType = (type: string) => {
    setSelectedNodeTypes(prev => 
      prev.includes(type) 
        ? prev.filter(t => t !== type)
        : [...prev, type]
    )
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <GitBranch className="h-5 w-5" />
            数据血缘关系图
          </CardTitle>
          <div className="flex items-center gap-2">
            <Badge variant="outline">
              节点: {filteredData.nodes.length}
            </Badge>
            <Badge variant="outline">
              边: {filteredData.edges.length}
            </Badge>
            <Badge variant="outline">
              缩放: {(zoomLevel * 100).toFixed(0)}%
            </Badge>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {/* 控制面板 */}
        <div className="mb-4 space-y-4">
          {/* 工具栏 */}
          <div className="flex items-center gap-2 flex-wrap">
            <Button variant="outline" size="sm" onClick={handleZoomIn}>
              <ZoomIn className="h-4 w-4" />
            </Button>
            <Button variant="outline" size="sm" onClick={handleZoomOut}>
              <ZoomOut className="h-4 w-4" />
            </Button>
            <Button variant="outline" size="sm" onClick={handleReset}>
              <RotateCcw className="h-4 w-4" />
            </Button>
            <Button variant="outline" size="sm" onClick={handleExport}>
              <Download className="h-4 w-4" />
            </Button>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={() => setShowLabels(!showLabels)}
            >
              {showLabels ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
            </Button>
          </div>

          {/* 过滤器 */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* 置信度过滤 */}
            <div className="space-y-2">
              <label className="text-sm font-medium">
                置信度阈值: {(confidenceThreshold[0] * 100).toFixed(0)}%
              </label>
              <Slider
                value={confidenceThreshold}
                onValueChange={setConfidenceThreshold}
                max={1}
                min={0}
                step={0.1}
                className="w-full"
              />
            </div>

            {/* 节点类型过滤 */}
            <div className="space-y-2">
              <label className="text-sm font-medium">节点类型</label>
              <div className="flex gap-2 flex-wrap">
                {['TABLE', 'FIELD', 'TRANSFORMATION'].map(type => (
                  <Button
                    key={type}
                    variant={selectedNodeTypes.includes(type) ? "default" : "outline"}
                    size="sm"
                    onClick={() => toggleNodeType(type)}
                  >
                    {type}
                  </Button>
                ))}
              </div>
            </div>

            {/* 方向指示 */}
            <div className="space-y-2">
              <label className="text-sm font-medium">分析方向</label>
              <div className="flex items-center gap-2">
                {direction === 'upstream' && (
                  <Badge variant="secondary" className="flex items-center gap-1">
                    <ArrowLeft className="h-3 w-3" />
                    上游分析
                  </Badge>
                )}
                {direction === 'downstream' && (
                  <Badge variant="secondary" className="flex items-center gap-1">
                    <ArrowRight className="h-3 w-3" />
                    下游分析
                  </Badge>
                )}
                {direction === 'both' && (
                  <Badge variant="secondary" className="flex items-center gap-1">
                    <Target className="h-3 w-3" />
                    双向分析
                  </Badge>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* 图表容器 */}
        <div className="border rounded-lg overflow-hidden bg-gray-50">
          <svg
            ref={svgRef}
            width={width}
            height={height}
            className="w-full h-auto"
            style={{ minHeight: '400px' }}
          />
        </div>

        {/* 统计信息 */}
        <div className="mt-4 grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
          <div className="text-center">
            <div className="font-semibold text-lg">{data.metadata.totalNodes}</div>
            <div className="text-gray-600">总节点数</div>
          </div>
          <div className="text-center">
            <div className="font-semibold text-lg">{data.metadata.totalEdges}</div>
            <div className="text-gray-600">总边数</div>
          </div>
          <div className="text-center">
            <div className="font-semibold text-lg">{data.metadata.maxDepth}</div>
            <div className="text-gray-600">最大深度</div>
          </div>
          <div className="text-center">
            <div className="font-semibold text-lg">
              {(data.metadata.confidenceDistribution.high * 100).toFixed(0)}%
            </div>
            <div className="text-gray-600">高置信度</div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
