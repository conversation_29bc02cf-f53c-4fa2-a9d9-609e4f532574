"use client";

import { Card } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";

interface TrendData {
  date: string;
  averageScore: number;
  nonComplianceRate: number;
  totalInspections: number;
}

interface CategoryData {
  category: string;
  count: number;
  percentage: number;
}

interface DepartmentData {
  department: string;
  averageScore: number;
  nonComplianceCount: number;
  totalInspections: number;
}

const mockTrendData: TrendData[] = [
  {
    date: "2024-03-01",
    averageScore: 92,
    nonComplianceRate: 8,
    totalInspections: 150,
  },
  {
    date: "2024-03-08",
    averageScore: 94,
    nonComplianceRate: 6,
    totalInspections: 180,
  },
  {
    date: "2024-03-15",
    averageScore: 91,
    nonComplianceRate: 9,
    totalInspections: 160,
  },
  {
    date: "2024-03-22",
    averageScore: 95,
    nonComplianceRate: 5,
    totalInspections: 200,
  },
];

const mockCategoryData: CategoryData[] = [
  { category: "响应不完整", count: 45, percentage: 30 },
  { category: "附件不规范", count: 35, percentage: 23 },
  { category: "处理方案不合理", count: 30, percentage: 20 },
  { category: "未按时处理", count: 25, percentage: 17 },
  { category: "其他", count: 15, percentage: 10 },
];

const mockDepartmentData: DepartmentData[] = [
  {
    department: "系统运维部",
    averageScore: 94,
    nonComplianceCount: 12,
    totalInspections: 200,
  },
  {
    department: "网络安全部",
    averageScore: 92,
    nonComplianceCount: 15,
    totalInspections: 180,
  },
  {
    department: "应用开发部",
    averageScore: 90,
    nonComplianceCount: 18,
    totalInspections: 150,
  },
];

export default function InspectionAnalytics() {
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold">质检统计分析</h2>
        <div className="flex items-center gap-4">
          <div>
            <Label>时间范围</Label>
            <Select defaultValue="30">
              <SelectTrigger className="w-32">
                <SelectValue placeholder="选择时间范围" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="7">近7天</SelectItem>
                <SelectItem value="30">近30天</SelectItem>
                <SelectItem value="90">近90天</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-3 gap-4">
        <Card className="p-4">
          <h3 className="text-lg font-semibold mb-2">平均质检得分</h3>
          <div className="text-3xl font-bold text-blue-600">93.5</div>
          <p className="text-sm text-gray-500 mt-1">较上期上升2.1分</p>
        </Card>
        <Card className="p-4">
          <h3 className="text-lg font-semibold mb-2">不合规率</h3>
          <div className="text-3xl font-bold text-red-600">7.2%</div>
          <p className="text-sm text-gray-500 mt-1">较上期下降1.5%</p>
        </Card>
        <Card className="p-4">
          <h3 className="text-lg font-semibold mb-2">质检总数</h3>
          <div className="text-3xl font-bold">690</div>
          <p className="text-sm text-gray-500 mt-1">较上期增加52个</p>
        </Card>
      </div>

      <Card className="p-6">
        <Tabs defaultValue="trend">
          <TabsList>
            <TabsTrigger value="trend">趋势分析</TabsTrigger>
            <TabsTrigger value="category">问题分类</TabsTrigger>
            <TabsTrigger value="department">部门分析</TabsTrigger>
          </TabsList>

          <TabsContent value="trend">
            <div className="mt-4">
              <div className="grid grid-cols-2 gap-4">
                {mockTrendData.map((data, index) => (
                  <Card key={index} className="p-4">
                    <div className="text-gray-500 mb-2">{data.date}</div>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span>平均得分</span>
                        <span className="font-semibold">{data.averageScore}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>不合规率</span>
                        <span className="font-semibold">{data.nonComplianceRate}%</span>
                      </div>
                      <div className="flex justify-between">
                        <span>质检数量</span>
                        <span className="font-semibold">{data.totalInspections}</span>
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="category">
            <div className="mt-4">
              <div className="space-y-4">
                {mockCategoryData.map((data, index) => (
                  <Card key={index} className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="font-medium">{data.category}</div>
                        <div className="text-sm text-gray-500">
                          发生次数：{data.count}
                        </div>
                      </div>
                      <div className="text-lg font-semibold">{data.percentage}%</div>
                    </div>
                  </Card>
                ))}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="department">
            <div className="mt-4">
              <div className="space-y-4">
                {mockDepartmentData.map((data, index) => (
                  <Card key={index} className="p-4">
                    <div className="flex items-center justify-between mb-2">
                      <div className="font-medium">{data.department}</div>
                      <div className="text-lg font-semibold">{data.averageScore}分</div>
                    </div>
                    <div className="grid grid-cols-2 gap-4 text-sm text-gray-500">
                      <div>不合规数：{data.nonComplianceCount}</div>
                      <div>总质检数：{data.totalInspections}</div>
                    </div>
                  </Card>
                ))}
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </Card>
    </div>
  );
} 