"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>it<PERSON> } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Slider } from "@/components/ui/slider"
import { 
  Network, 
  ZoomIn, 
  ZoomOut, 
  RotateCcw, 
  Download,
  Filter,
  Eye,
  EyeOff
} from "lucide-react"
import type { RelationGraph, GraphNode, GraphEdge } from "@/types/sql-analysis"

interface RelationGraphVisualizationProps {
  data: RelationGraph
  width?: number
  height?: number
  onNodeClick?: (node: GraphNode) => void
  onEdgeClick?: (edge: GraphEdge) => void
}

export default function RelationGraphVisualization({
  data,
  width = 800,
  height = 600,
  onNodeClick,
  onEdgeClick
}: RelationGraphVisualizationProps) {
  const [confidenceThreshold, setConfidenceThreshold] = useState([0.5])
  const [selectedRelationTypes, setSelectedRelationTypes] = useState<string[]>([])
  const [showLabe<PERSON>, setShowLabels] = useState(true)
  const [selectedNode, setSelectedNode] = useState<string | null>(null)

  // 过滤数据
  const filteredData = {
    ...data,
    edges: data.edges.filter(edge => {
      const confidenceMatch = edge.confidence >= confidenceThreshold[0]
      const typeMatch = selectedRelationTypes.length === 0 || 
                       selectedRelationTypes.includes(edge.type)
      return confidenceMatch && typeMatch
    })
  }

  useEffect(() => {
    if (!svgRef.current || !data.nodes.length) return

    // 清除之前的内容
    d3.select(svgRef.current).selectAll("*").remove()

    const svg = d3.select(svgRef.current)
    const container = svg.append("g")

    // 设置缩放行为
    const zoom = d3.zoom<SVGSVGElement, unknown>()
      .scaleExtent([0.1, 4])
      .on("zoom", (event) => {
        container.attr("transform", event.transform)
        setZoomLevel(event.transform.k)
      })

    svg.call(zoom)

    // 创建力导向图
    const simulation = d3.forceSimulation(filteredData.nodes as any)
      .force("link", d3.forceLink(filteredData.edges)
        .id((d: any) => d.id)
        .distance(100)
        .strength(0.5)
      )
      .force("charge", d3.forceManyBody().strength(-300))
      .force("center", d3.forceCenter(width / 2, height / 2))
      .force("collision", d3.forceCollide().radius(50))

    // 创建箭头标记
    const defs = svg.append("defs")
    
    const arrowMarker = defs.append("marker")
      .attr("id", "arrowhead")
      .attr("viewBox", "0 -5 10 10")
      .attr("refX", 25)
      .attr("refY", 0)
      .attr("markerWidth", 6)
      .attr("markerHeight", 6)
      .attr("orient", "auto")
    
    arrowMarker.append("path")
      .attr("d", "M0,-5L10,0L0,5")
      .attr("fill", "#666")

    // 绘制连接线
    const links = container.append("g")
      .selectAll("line")
      .data(filteredData.edges)
      .enter()
      .append("line")
      .attr("stroke", (d: GraphEdge) => getEdgeColor(d.type, d.confidence))
      .attr("stroke-width", (d: GraphEdge) => Math.max(1, d.confidence * 4))
      .attr("stroke-dasharray", (d: GraphEdge) => 
        d.type === 'WHERE_CONDITION' ? "5,5" : null
      )
      .attr("marker-end", "url(#arrowhead)")
      .style("cursor", "pointer")
      .on("click", (event, d) => {
        event.stopPropagation()
        onEdgeClick?.(d)
      })
      .on("mouseover", function(event, d) {
        // 显示边的详细信息
        d3.select(this).attr("stroke-width", Math.max(3, d.confidence * 6))
        
        // 创建tooltip
        const tooltip = d3.select("body").append("div")
          .attr("class", "tooltip")
          .style("position", "absolute")
          .style("background", "rgba(0,0,0,0.8)")
          .style("color", "white")
          .style("padding", "8px")
          .style("border-radius", "4px")
          .style("font-size", "12px")
          .style("pointer-events", "none")
          .style("z-index", "1000")
          .html(`
            <div><strong>${d.type}</strong></div>
            <div>置信度: ${Math.round(d.confidence * 100)}%</div>
            <div>频次: ${d.metadata.frequency}</div>
          `)
          .style("left", (event.pageX + 10) + "px")
          .style("top", (event.pageY - 10) + "px")
      })
      .on("mouseout", function(event, d) {
        d3.select(this).attr("stroke-width", Math.max(1, d.confidence * 4))
        d3.selectAll(".tooltip").remove()
      })

    // 绘制节点
    const nodes = container.append("g")
      .selectAll("g")
      .data(filteredData.nodes)
      .enter()
      .append("g")
      .style("cursor", "pointer")
      .call(d3.drag<any, any>()
        .on("start", (event, d: any) => {
          if (!event.active) simulation.alphaTarget(0.3).restart()
          d.fx = d.x
          d.fy = d.y
        })
        .on("drag", (event, d: any) => {
          d.fx = event.x
          d.fy = event.y
        })
        .on("end", (event, d: any) => {
          if (!event.active) simulation.alphaTarget(0)
          d.fx = null
          d.fy = null
        })
      )
      .on("click", (event, d) => {
        event.stopPropagation()
        onNodeClick?.(d)
      })

    // 节点圆圈
    nodes.append("circle")
      .attr("r", (d: GraphNode) => Math.max(20, Math.sqrt(d.metadata.frequency) * 8))
      .attr("fill", (d: GraphNode) => getNodeColor(d.type))
      .attr("stroke", "#fff")
      .attr("stroke-width", 2)
      .on("mouseover", function(event, d) {
        d3.select(this).attr("r", Math.max(25, Math.sqrt(d.metadata.frequency) * 10))
      })
      .on("mouseout", function(event, d) {
        d3.select(this).attr("r", Math.max(20, Math.sqrt(d.metadata.frequency) * 8))
      })

    // 节点标签
    if (showLabels) {
      nodes.append("text")
        .text((d: GraphNode) => d.label)
        .attr("text-anchor", "middle")
        .attr("dy", "0.35em")
        .attr("font-size", "12px")
        .attr("font-weight", "bold")
        .attr("fill", "#333")
        .attr("pointer-events", "none")
    }

    // 更新位置
    simulation.on("tick", () => {
      links
        .attr("x1", (d: any) => d.source.x)
        .attr("y1", (d: any) => d.source.y)
        .attr("x2", (d: any) => d.target.x)
        .attr("y2", (d: any) => d.target.y)

      nodes.attr("transform", (d: any) => `translate(${d.x},${d.y})`)
    })

    // 清理函数
    return () => {
      simulation.stop()
    }
  }, [filteredData, width, height, showLabels, onNodeClick, onEdgeClick])

  // 获取边的颜色
  const getEdgeColor = (type: string, confidence: number) => {
    const colors = {
      'INNER_JOIN': '#2563eb',
      'LEFT_JOIN': '#059669',
      'RIGHT_JOIN': '#dc2626',
      'FULL_JOIN': '#7c3aed',
      'WHERE_CONDITION': '#ea580c'
    }
    const baseColor = colors[type as keyof typeof colors] || '#6b7280'
    const alpha = Math.max(0.3, confidence)
    return `${baseColor}${Math.round(alpha * 255).toString(16).padStart(2, '0')}`
  }

  // 获取节点颜色
  const getNodeColor = (type: string) => {
    const colors = {
      'table': '#3b82f6',
      'view': '#10b981',
      'column': '#f59e0b'
    }
    return colors[type as keyof typeof colors] || '#6b7280'
  }

  // 重置视图
  const resetView = () => {
    if (!svgRef.current) return
    const svg = d3.select(svgRef.current)
    svg.transition().duration(750).call(
      d3.zoom<SVGSVGElement, unknown>().transform,
      d3.zoomIdentity
    )
  }

  // 缩放控制
  const zoomIn = () => {
    if (!svgRef.current) return
    const svg = d3.select(svgRef.current)
    svg.transition().duration(300).call(
      d3.zoom<SVGSVGElement, unknown>().scaleBy,
      1.5
    )
  }

  const zoomOut = () => {
    if (!svgRef.current) return
    const svg = d3.select(svgRef.current)
    svg.transition().duration(300).call(
      d3.zoom<SVGSVGElement, unknown>().scaleBy,
      1 / 1.5
    )
  }

  // 获取所有关系类型
  const relationTypes = Array.from(new Set(data.edges.map(edge => edge.type)))

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Network className="w-5 h-5" />
            关系图谱可视化
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" onClick={zoomOut}>
              <ZoomOut className="w-4 h-4" />
            </Button>
            <Button variant="outline" size="sm" onClick={zoomIn}>
              <ZoomIn className="w-4 h-4" />
            </Button>
            <Button variant="outline" size="sm" onClick={resetView}>
              <RotateCcw className="w-4 h-4" />
            </Button>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={() => setShowLabels(!showLabels)}
            >
              {showLabels ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
            </Button>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* 控制面板 */}
        <div className="flex flex-wrap gap-4 p-4 bg-gray-50 rounded-lg">
          <div className="flex-1 min-w-48">
            <label className="text-sm font-medium mb-2 block">
              置信度阈值: {confidenceThreshold[0].toFixed(2)}
            </label>
            <Slider
              value={confidenceThreshold}
              onValueChange={setConfidenceThreshold}
              max={1}
              min={0}
              step={0.1}
              className="w-full"
            />
          </div>
          
          <div className="flex-1 min-w-48">
            <label className="text-sm font-medium mb-2 block">关系类型过滤</label>
            <div className="flex flex-wrap gap-1">
              {relationTypes.map(type => (
                <Badge
                  key={type}
                  variant={selectedRelationTypes.includes(type) ? "default" : "outline"}
                  className="cursor-pointer text-xs"
                  onClick={() => {
                    setSelectedRelationTypes(prev => 
                      prev.includes(type) 
                        ? prev.filter(t => t !== type)
                        : [...prev, type]
                    )
                  }}
                >
                  {type}
                </Badge>
              ))}
            </div>
          </div>
        </div>

        {/* 图谱统计 */}
        <div className="grid grid-cols-4 gap-4 text-center">
          <div className="p-2 bg-blue-50 rounded">
            <div className="text-lg font-bold text-blue-600">{filteredData.nodes.length}</div>
            <div className="text-xs text-gray-600">节点数</div>
          </div>
          <div className="p-2 bg-green-50 rounded">
            <div className="text-lg font-bold text-green-600">{filteredData.edges.length}</div>
            <div className="text-xs text-gray-600">关系数</div>
          </div>
          <div className="p-2 bg-purple-50 rounded">
            <div className="text-lg font-bold text-purple-600">{zoomLevel.toFixed(1)}x</div>
            <div className="text-xs text-gray-600">缩放级别</div>
          </div>
          <div className="p-2 bg-orange-50 rounded">
            <div className="text-lg font-bold text-orange-600">
              {Math.round(filteredData.edges.reduce((sum, e) => sum + e.confidence, 0) / filteredData.edges.length * 100) || 0}%
            </div>
            <div className="text-xs text-gray-600">平均置信度</div>
          </div>
        </div>

        {/* SVG图谱 */}
        <div className="border rounded-lg overflow-hidden bg-white">
          <svg
            ref={svgRef}
            width={width}
            height={height}
            className="w-full h-auto"
            style={{ minHeight: '400px' }}
          />
        </div>

        {/* 图例 */}
        <div className="flex flex-wrap gap-4 text-sm">
          <div className="flex items-center gap-2">
            <div className="w-4 h-4 rounded-full bg-blue-500"></div>
            <span>数据表</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-8 h-0.5 bg-blue-500"></div>
            <span>INNER JOIN</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-8 h-0.5 bg-green-500"></div>
            <span>LEFT JOIN</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-8 h-0.5 bg-orange-500 border-dashed border-t-2 border-orange-500"></div>
            <span>WHERE条件</span>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
