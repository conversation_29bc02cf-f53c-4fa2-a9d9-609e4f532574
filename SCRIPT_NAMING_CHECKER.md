# 脚本命名规范检查功能

## 功能概述

脚本命名规范检查功能是规则生成器步骤3中的新增功能，专门针对"数据巡查"稽查场景的SQL脚本进行命名规范识别与修正建议。该功能能够自动检查脚本的命名规范性，提供详细的评分和建议，并支持一键修正功能。

## 主要特性

### 1. 自动规范检查
- **表名检查**: 验证表名是否使用 `dm_` 前缀（数据巡查专用）
- **字段名检查**: 检查字段名是否符合小写字母+下划线的规范
- **别名检查**: 验证别名命名规范
- **函数名检查**: 确保函数名使用大写字母和下划线
- **注释检查**: 验证注释格式是否符合 `-- 描述` 规范

### 2. 智能评分系统
- **评分范围**: 0-100分
- **评分等级**: 
  - 90-100分: 优秀 ⭐
  - 70-89分: 良好 ✅
  - 50-69分: 一般 ⚠️
  - 0-49分: 较差 ❌
- **扣分规则**:
  - 缺少注释: -20分
  - 表名不规范: -15分
  - 注释格式错误: -5分
  - 字段名不规范: -3分
  - 别名不规范: -2分
  - 函数名不规范: -1分

### 3. 详细问题报告
- **问题分类**: 错误、警告、信息三个级别
- **位置定位**: 精确到行号
- **对比显示**: 当前值 vs 建议值
- **影响评估**: 高、中、低三个影响程度

### 4. 一键修正功能
- **自动修正**: 根据检查结果自动生成修正后的脚本
- **批量处理**: 一次性修正所有可自动修正的问题
- **注释补充**: 自动添加缺失的脚本注释

## 命名规范标准

### 表名规范
- **前缀要求**: 必须使用 `dm_` 前缀
- **命名格式**: 小写字母 + 下划线
- **示例**: `dm_customer_audit`, `dm_power_consumption`, `dm_billing_anomaly`

### 字段名规范
- **格式要求**: 小写字母 + 下划线
- **示例**: `customer_id`, `power_consumption`, `audit_date`, `anomaly_score`

### 别名规范
- **格式要求**: 小写字母 + 下划线
- **示例**: `cust`, `power`, `audit`, `score`

### 函数名规范
- **格式要求**: 大写字母 + 下划线
- **示例**: `COUNT`, `SUM`, `AVG`, `MAX`, `MIN`, `DATE_TRUNC`

### 注释规范
- **格式要求**: `-- 描述内容`
- **示例**: `-- 数据巡查：充电桩电量异常检测`, `-- 统计异常用户数量`

## 使用流程

### 1. 触发检查
- 在步骤3中生成SQL脚本后，系统自动显示命名规范检查组件
- 点击"开始检查"按钮启动检查流程

### 2. 查看结果
- **评分展示**: 查看规范性评分和等级
- **问题列表**: 浏览发现的所有命名规范问题
- **修正建议**: 查看系统提供的改进建议

### 3. 应用修正
- 点击"一键修正"按钮自动应用所有修正建议
- 系统自动更新SQL脚本内容
- 可重新检查确认修正效果

## 技术实现

### 组件结构
```
ScriptNamingChecker/
├── 评分展示区域
├── 问题统计区域
├── 修正建议区域
├── 一键修正按钮
└── 规范说明区域
```

### 核心算法
1. **正则表达式匹配**: 使用正则表达式识别SQL中的各种命名元素
2. **规则引擎**: 基于预定义规则进行规范性检查
3. **智能修正**: 根据问题类型自动生成修正方案
4. **评分计算**: 基于问题严重程度计算规范性评分

### 状态管理
- `checkResult`: 检查结果状态
- `isChecking`: 检查进行中状态
- `showDetails`: 详情展示状态

## 集成方式

### 在Step3SQLConfirm中的集成
```tsx
{/* 脚本命名规范检查 */}
{props.sqlResult && (
  <ScriptNamingChecker
    sqlScript={props.sqlResult}
    scenario={props.generatedDescription.split('，')[0] || '数据巡查'}
    onScriptUpdate={props.setSqlResult}
  />
)}
```

### 触发条件
- 仅在SQL脚本生成后显示
- 自动从主题描述中提取场景信息
- 支持脚本内容实时更新

## 用户体验优化

### 视觉设计
- **渐变背景**: 橙色渐变主题，突出检查功能
- **图标系统**: 使用语义化图标区分问题类型
- **进度条**: 直观显示规范性评分
- **颜色编码**: 不同严重程度使用不同颜色

### 交互设计
- **渐进式展示**: 默认显示前3个问题，支持展开全部
- **一键操作**: 简化修正流程，减少用户操作步骤
- **实时反馈**: 检查过程中显示加载状态
- **结果对比**: 清晰显示修正前后的差异

## 扩展性设计

### 规则配置
- 支持自定义命名规范规则
- 可配置不同场景的规范标准
- 支持规则权重调整

### 检查范围
- 可扩展检查更多SQL元素
- 支持存储过程、视图等复杂对象
- 可集成语法检查功能

### 报告功能
- 支持生成详细的检查报告
- 可导出修正建议
- 支持历史检查记录

## 未来规划

### 短期目标
1. 优化正则表达式匹配精度
2. 增加更多命名规范规则
3. 改进修正算法的准确性

### 中期目标
1. 支持自定义规范配置
2. 集成语法检查功能
3. 添加批量检查功能

### 长期目标
1. 支持多数据库方言
2. 集成AI辅助命名建议
3. 建立命名规范知识库

## 总结

脚本命名规范检查功能为数据巡查场景提供了专业的SQL脚本质量保障，通过自动化的检查、评分和修正功能，显著提高了脚本的规范性和可维护性。该功能不仅提升了开发效率，也为团队协作和代码管理提供了有力支持。 