import { useEffect, useRef, useState } from "react"
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Drawer, DrawerContent, DrawerHeader, DrawerTitle, DrawerDescription } from "@/components/ui/drawer"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { BookOpen, X, Link2 } from "lucide-react"
import { Network } from "vis-network/standalone/esm/vis-network"
import "vis-network/styles/vis-network.css"
import domtoimage from 'dom-to-image'

// mock 数据
const mockNodes: Array<{ id: number; label: string; type: string; desc?: string; related?: number[]; group?: string }> = [
  { id: 1, label: "业扩报装", type: "主题", desc: "业扩报装业务主题，包含多个风险点。" },
  { id: 2, label: "低压报装", type: "风险点", desc: "低压报装环节的风险点。", related: [8] },
  { id: 3, label: "小区项目用电报装", type: "风险点", desc: "小区项目用电报装的风险点。" },
  { id: 4, label: "低压居民用电", type: "规则", desc: "低压居民用电相关规则。" },
  { id: 5, label: "个人用电充值", type: "规则", desc: "个人用电充值相关规则。" },
  { id: 6, label: "电动汽车充电", type: "规则", desc: "电动汽车充电相关规则。" },
  { id: 7, label: "居民业务", type: "规则", desc: "居民业务相关规则。" },
  { id: 8, label: "进一歩优化", type: "政策", desc: "优化相关政策。" },
]
const mockEdges: Array<{ from: number; to: number; label: string; type: string }> = [
  { from: 1, to: 2, label: "包含", type: "包含" },
  { from: 1, to: 3, label: "包含", type: "包含" },
  { from: 2, to: 4, label: "关联", type: "关联" },
  { from: 2, to: 5, label: "关联", type: "关联" },
  { from: 2, to: 6, label: "关联", type: "关联" },
  { from: 2, to: 7, label: "关联", type: "关联" },
  { from: 2, to: 8, label: "政策依据", type: "政策依据" },
  { from: 3, to: 4, label: "规则", type: "规则" },
]

const typeColor: Record<string, string> = {
  "主题": "#ff9800",
  "风险点": "#1976d2",
  "规则": "#43a047",
  "政策": "#8e24aa",
}

const edgeTypeColor: Record<string, string> = {
  "包含": "#1976d2",
  "关联": "#43a047",
  "政策依据": "#8e24aa",
  "规则": "#ff9800",
}

const allEdgeTypes = ["包含", "关联", "政策依据", "规则"]

export default function AuditElementGraph() {
  const containerRef = useRef<HTMLDivElement>(null)
  const [network, setNetwork] = useState<Network | null>(null)
  const [selectedType, setSelectedType] = useState<string>("all")
  const [selectedEdgeTypes, setSelectedEdgeTypes] = useState<string[]>(allEdgeTypes)
  const [selectedNode, setSelectedNode] = useState<any>(null)
  const [drawerOpen, setDrawerOpen] = useState(false)
  const [pathNodes, setPathNodes] = useState<number[]>([])
  const [pathSelect, setPathSelect] = useState<number[]>([])
  const [searchValue, setSearchValue] = useState("")
  const [multiSelectMode, setMultiSelectMode] = useState(false)
  const [selectedNodes, setSelectedNodes] = useState<number[]>([])
  const [nodes, setNodes] = useState<typeof mockNodes>(mockNodes)
  const [edges, setEdges] = useState<typeof mockEdges>(mockEdges)
  const [batchEditOpen, setBatchEditOpen] = useState(false)
  const [batchEditType, setBatchEditType] = useState<string>("")
  const [batchEditDesc, setBatchEditDesc] = useState<string>("")
  const [batchGroupOpen, setBatchGroupOpen] = useState(false)
  const [batchGroupName, setBatchGroupName] = useState("")

  // 过滤节点
  const filteredNodes = selectedType === "all"
    ? nodes
    : nodes.filter(n => n.type === selectedType)
  // 过滤边（只显示两端都在当前节点集的边，且类型在过滤中）
  const filteredNodeIds = new Set(filteredNodes.map(n => n.id))
  const filteredEdges = edges.filter(e => filteredNodeIds.has(e.from) && filteredNodeIds.has(e.to) && selectedEdgeTypes.includes(e.type))

  // 路径高亮算法（BFS最短路径）
  function findShortestPath(start: number, end: number): number[] {
    if (start === end) return [start]
    const adj: Record<number, number[]> = {}
    filteredEdges.forEach(e => {
      if (!adj[e.from]) adj[e.from] = []
      adj[e.from].push(e.to)
      // 若为无向图可加：adj[e.to].push(e.from)
    })
    const queue: [number, number[]][] = [[start, [start]]]
    const visited = new Set<number>([start])
    while (queue.length) {
      const [node, path] = queue.shift()!
      if (node === end) return path
      for (const next of adj[node] || []) {
        if (!visited.has(next)) {
          visited.add(next)
          queue.push([next, [...path, next]])
        }
      }
    }
    return []
  }

  useEffect(() => {
    if (containerRef.current) {
      const nodes = filteredNodes.map(n => ({
        ...n,
        color: {
          background: typeColor[n.type] || "#90caf9",
          border: pathNodes.includes(n.id) ? "#f44336" : "#fff",
          highlight: { background: typeColor[n.type], border: "#f44336" },
        },
        font: { color: "#222", size: 16, face: "inherit", vadjust: 2 },
        shape: n.type === "主题" ? "ellipse" : n.type === "风险点" ? "circle" : n.type === "规则" ? "box" : "hexagon",
      }))
      const edges = filteredEdges.map(e => ({
        ...e,
        color: {
          color: pathNodes.length && pathNodes.includes(e.from) && pathNodes.includes(e.to) && Math.abs(pathNodes.indexOf(e.from) - pathNodes.indexOf(e.to)) === 1 ? "#f44336" : (edgeTypeColor[e.type] || "#bdbdbd"),
          highlight: "#1976d2"
        },
        font: { align: "middle" }
      }))
      const data = { nodes, edges }
      const options = {
        nodes: { borderWidth: 2, shadow: true },
        edges: { arrows: { to: { enabled: true, scaleFactor: 0.7 } }, smooth: true },
        physics: { stabilization: false },
        interaction: { hover: true, zoomView: true, dragView: true, dragNodes: true },
        layout: { improvedLayout: true },
      }
      let net: Network
      if (network) {
        network.setData(data)
        net = network
      } else {
        net = new Network(containerRef.current, data, options)
        setNetwork(net)
      }
      net.off("click")
      net.on("click", params => {
        if (params.nodes.length) {
          const nodeId = params.nodes[0]
          const node = filteredNodes.find(n => n.id === nodeId)
          if (multiSelectMode) {
            setSelectedNodes(prev => prev.includes(nodeId) ? prev.filter(id => id !== nodeId) : [...prev, nodeId])
            setDrawerOpen(false)
            setSelectedNode(null)
            setPathSelect([])
            setPathNodes([])
          } else {
            setSelectedNode(node)
            setDrawerOpen(true)
            // 路径选择
            if (pathSelect.length === 0) {
              setPathSelect([nodeId])
              setPathNodes([])
            } else if (pathSelect.length === 1 && pathSelect[0] !== nodeId) {
              const path = findShortestPath(pathSelect[0], nodeId)
              setPathSelect([])
              setPathNodes(path)
            } else {
              setPathSelect([])
              setPathNodes([])
            }
          }
        }
      })
      // 框选支持
      net.off("select")
      net.on("select", params => {
        if (multiSelectMode && params.nodes) {
          setSelectedNodes(params.nodes as number[])
        }
      })
      // 退出多选时清空
      if (!multiSelectMode) setSelectedNodes([])
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [containerRef, selectedType, selectedEdgeTypes, pathNodes, multiSelectMode])

  // 关系类型多选过滤
  function handleEdgeTypeChange(type: string) {
    setSelectedEdgeTypes(prev => prev.includes(type) ? prev.filter(t => t !== type) : [...prev, type])
    setPathNodes([])
    setPathSelect([])
  }

  // 邻域聚焦
  function showNeighborhood(nodeId: number) {
    if (!network) return
    const neighborIds = new Set<number>([nodeId])
    filteredEdges.forEach(e => {
      if (e.from === nodeId) neighborIds.add(e.to)
      if (e.to === nodeId) neighborIds.add(e.from)
    })
    network.selectNodes(Array.from(neighborIds))
    network.focus(nodeId, { scale: 1.2, animation: true })
  }

  // 搜索并高亮节点
  function handleSearch(e: React.FormEvent<HTMLFormElement>) {
    e.preventDefault()
    if (!searchValue.trim() || !network) return
    const node = nodes.find(n => n.label.includes(searchValue.trim()))
    if (node) {
      network.selectNodes([node.id])
      network.focus(node.id, { scale: 1.3, animation: true })
      setDrawerOpen(true)
      setSelectedNode(node)
    }
  }

  // 导出图片
  function handleExport() {
    if (!containerRef.current) return
    domtoimage.toPng(containerRef.current).then((dataUrl: string) => {
      const link = document.createElement('a')
      link.download = 'audit-knowledge-graph.png'
      link.href = dataUrl
      link.click()
    })
  }

  // 批量高亮
  function handleBatchHighlight() {
    if (!network) return
    network.selectNodes(selectedNodes)
    selectedNodes.forEach(id => network.focus(id, { scale: 1.1, animation: true }))
  }
  // 批量导出
  function handleBatchExport() {
    const nodes = nodes.filter(n => selectedNodes.includes(n.id))
    const dataStr = "data:text/json;charset=utf-8," + encodeURIComponent(JSON.stringify(nodes, null, 2))
    const link = document.createElement('a')
    link.href = dataStr
    link.download = 'selected-nodes.json'
    link.click()
  }

  // 批量删除
  function handleBatchDelete() {
    setNodes(prev => prev.filter(n => !selectedNodes.includes(n.id)))
    setEdges(prev => prev.filter(e => !selectedNodes.includes(e.from) && !selectedNodes.includes(e.to)))
    setSelectedNodes([])
  }
  // 批量编辑
  function handleBatchEditSubmit() {
    setNodes(prev => prev.map(n => selectedNodes.includes(n.id) ? {
      ...n,
      type: batchEditType || n.type,
      desc: batchEditDesc || n.desc
    } : n))
    setBatchEditOpen(false)
    setBatchEditType("")
    setBatchEditDesc("")
  }
  // 批量分组
  function handleBatchGroupSubmit() {
    setNodes(prev => prev.map(n => selectedNodes.includes(n.id) ? {
      ...n,
      group: batchGroupName
    } : n))
    setBatchGroupOpen(false)
    setBatchGroupName("")
  }

  return (
    <div className="flex flex-col md:flex-row gap-6 p-6">
      <div className="flex-1 min-w-0">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BookOpen className="h-5 w-5" />
              稽查要素知识图谱
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-4 mb-4 flex-wrap">
              <Button size="sm" variant={multiSelectMode ? "default" : "outline"} onClick={() => setMultiSelectMode(m => !m)}>
                {multiSelectMode ? "退出多选" : "批量操作"}
              </Button>
              {multiSelectMode && (
                <div className="flex items-center gap-2">
                  <span className="text-sm text-blue-600">已选 {selectedNodes.length} 个节点</span>
                  <Button size="sm" variant="outline" onClick={handleBatchHighlight} disabled={selectedNodes.length === 0}>批量高亮</Button>
                  <Button size="sm" variant="outline" onClick={handleBatchExport} disabled={selectedNodes.length === 0}>导出JSON</Button>
                  <Button size="sm" variant="outline" onClick={handleBatchDelete} disabled={selectedNodes.length === 0}>批量删除</Button>
                  <Button size="sm" variant="outline" onClick={() => setBatchEditOpen(true)} disabled={selectedNodes.length === 0}>批量编辑</Button>
                  <Button size="sm" variant="outline" onClick={() => setBatchGroupOpen(true)} disabled={selectedNodes.length === 0}>批量分组</Button>
                </div>
              )}
              <form onSubmit={handleSearch} className="flex items-center gap-2">
                <Input
                  placeholder="搜索节点名称..."
                  value={searchValue}
                  onChange={e => setSearchValue(e.target.value)}
                  className="w-44"
                />
                <Button type="submit" size="sm" variant="outline">搜索</Button>
              </form>
              <Button size="sm" variant="outline" onClick={handleExport}>导出图片</Button>
              <Select value={selectedType} onValueChange={setSelectedType}>
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="筛选节点类型" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部类型</SelectItem>
                  <SelectItem value="主题">主题</SelectItem>
                  <SelectItem value="风险点">风险点</SelectItem>
                  <SelectItem value="规则">规则</SelectItem>
                  <SelectItem value="政策">政策</SelectItem>
                </SelectContent>
              </Select>
              <div className="flex items-center gap-2">
                <span className="text-gray-500 text-sm">关系类型：</span>
                {allEdgeTypes.map(type => (
                  <Button key={type} size="sm" variant={selectedEdgeTypes.includes(type) ? "default" : "outline"} onClick={() => handleEdgeTypeChange(type)}>
                    <span className="flex items-center gap-1">
                      <Link2 className="h-3 w-3" style={{ color: edgeTypeColor[type] }} />
                      {type}
                    </span>
                  </Button>
                ))}
              </div>
              <span className="text-gray-400 text-sm">可缩放、拖拽、点击节点查看详情，连续点击两个节点可高亮路径</span>
            </div>
            <div ref={containerRef} style={{ height: 520, background: "#f8fafc", borderRadius: 8 }} />
          </CardContent>
        </Card>
      </div>
      {/* 右侧节点详情抽屉 */}
      <Drawer open={drawerOpen} onOpenChange={setDrawerOpen} direction="right">
        <DrawerContent className="w-full max-w-md">
          <DrawerHeader className="flex flex-row items-center justify-between">
            <div>
              <DrawerTitle>节点详情</DrawerTitle>
              <DrawerDescription>展示稽查要素的详细信息</DrawerDescription>
            </div>
            <Button variant="ghost" size="icon" onClick={() => setDrawerOpen(false)}>
              <X className="h-5 w-5" />
            </Button>
          </DrawerHeader>
          {selectedNode && (
            <div className="p-6 space-y-4">
              <div className="flex items-center gap-2">
                <span className="font-bold text-lg">{selectedNode.label}</span>
                <Badge style={{ background: typeColor[selectedNode.type], color: "#fff" }}>{selectedNode.type}</Badge>
              </div>
              <div className="text-gray-600">节点ID: {selectedNode.id}</div>
              <div className="text-gray-600">类型: {selectedNode.type}</div>
              <div className="text-gray-600">描述: {selectedNode.desc || '无'}</div>
              {selectedNode.related && (
                <div className="text-gray-600">相关政策/规则: {selectedNode.related.map((id: number) => {
                  const n = nodes.find((n) => n.id === id)
                  return n ? n.label : id
                }).join('、')}</div>
              )}
              <Button size="sm" variant="outline" onClick={() => showNeighborhood(selectedNode.id)}>
                查看邻域
              </Button>
            </div>
          )}
        </DrawerContent>
      </Drawer>
      {/* 批量编辑弹窗 */}
      {batchEditOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/30">
          <div className="bg-white rounded-lg p-6 w-96 space-y-4">
            <div className="font-bold text-lg mb-2">批量编辑节点</div>
            <div>
              <label className="block text-sm mb-1">类型</label>
              <Select value={batchEditType} onValueChange={setBatchEditType}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="不修改则留空" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">不修改</SelectItem>
                  <SelectItem value="主题">主题</SelectItem>
                  <SelectItem value="风险点">风险点</SelectItem>
                  <SelectItem value="规则">规则</SelectItem>
                  <SelectItem value="政策">政策</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <label className="block text-sm mb-1">描述</label>
              <Input value={batchEditDesc} onChange={e => setBatchEditDesc(e.target.value)} placeholder="不修改则留空" />
            </div>
            <div className="flex gap-2 justify-end">
              <Button size="sm" variant="outline" onClick={() => setBatchEditOpen(false)}>取消</Button>
              <Button size="sm" onClick={handleBatchEditSubmit}>保存</Button>
            </div>
          </div>
        </div>
      )}
      {/* 批量分组弹窗 */}
      {batchGroupOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/30">
          <div className="bg-white rounded-lg p-6 w-80 space-y-4">
            <div className="font-bold text-lg mb-2">批量分组</div>
            <div>
              <label className="block text-sm mb-1">分组名</label>
              <Input value={batchGroupName} onChange={e => setBatchGroupName(e.target.value)} placeholder="请输入分组名" />
            </div>
            <div className="flex gap-2 justify-end">
              <Button size="sm" variant="outline" onClick={() => setBatchGroupOpen(false)}>取消</Button>
              <Button size="sm" onClick={handleBatchGroupSubmit} disabled={!batchGroupName.trim()}>保存</Button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
} 