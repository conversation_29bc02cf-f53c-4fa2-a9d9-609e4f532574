"use client"

import { useState } from "react"
import { <PERSON>, CardHeader, CardTitle, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Table, TableHeader, TableRow, TableHead, TableBody, TableCell } from "@/components/ui/table"
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON><PERSON>rigger, TabsContent } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { RefreshCw, Play, FileText } from "lucide-react"

const mockSources = [
  { id: 1, name: "用户中心", type: "MySQL", status: "正常", lastSync: "2024-05-01 10:00:00" },
  { id: 2, name: "电力业务", type: "MaxCompute", status: "异常", lastSync: "2024-05-01 09:30:00" },
  { id: 3, name: "稽查规则库", type: "MySQL", status: "正常", lastSync: "2024-04-30 22:00:00" },
]

const mockTasks = [
  { id: 1, name: "用户表全量同步", source: "用户中心", status: "完成", progress: 100, time: "2024-05-01 10:01:00" },
  { id: 2, name: "电表数据增量", source: "电力业务", status: "进行中", progress: 60, time: "2024-05-01 09:40:00" },
  { id: 3, name: "规则表同步", source: "稽查规则库", status: "失败", progress: 0, time: "2024-04-30 22:10:00" },
]

export default function Main() {
  const [tab, setTab] = useState("source")

  return (
    <div className="flex-1 space-y-6 p-6">
      <div className="space-y-2">
        <h1 className="text-3xl font-bold tracking-tight text-gray-900">元数据同步管理</h1>
        <p className="text-gray-600">管理各类数据源的元数据同步任务，支持手动同步与日志查看</p>
      </div>
      <Card>
        <CardHeader>
          <CardTitle>同步管理</CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs value={tab} onValueChange={setTab}>
            <TabsList>
              <TabsTrigger value="source">数据源列表</TabsTrigger>
              <TabsTrigger value="task">同步任务</TabsTrigger>
            </TabsList>
            <TabsContent value="source">
              <div className="mb-4 flex justify-end">
                <Button variant="outline" size="sm" className="mr-2"><RefreshCw className="w-4 h-4 mr-1" />刷新</Button>
                <Button size="sm"><Play className="w-4 h-4 mr-1" />手动同步</Button>
              </div>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>数据源名称</TableHead>
                    <TableHead>类型</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead>上次同步时间</TableHead>
                    <TableHead>操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {mockSources.map(src => (
                    <TableRow key={src.id}>
                      <TableCell>{src.name}</TableCell>
                      <TableCell>{src.type}</TableCell>
                      <TableCell><Badge variant={src.status === "正常" ? "default" : "destructive"}>{src.status}</Badge></TableCell>
                      <TableCell>{src.lastSync}</TableCell>
                      <TableCell>
                        <Button size="sm" variant="outline"><Play className="w-4 h-4 mr-1" />同步</Button>
                        <Button size="sm" variant="ghost"><FileText className="w-4 h-4 mr-1" />日志</Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TabsContent>
            <TabsContent value="task">
              <div className="mb-4 flex justify-end">
                <Button variant="outline" size="sm"><RefreshCw className="w-4 h-4 mr-1" />刷新</Button>
              </div>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>任务名称</TableHead>
                    <TableHead>数据源</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead>进度</TableHead>
                    <TableHead>时间</TableHead>
                    <TableHead>操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {mockTasks.map(task => (
                    <TableRow key={task.id}>
                      <TableCell>{task.name}</TableCell>
                      <TableCell>{task.source}</TableCell>
                      <TableCell><Badge variant={task.status === "完成" ? "default" : task.status === "进行中" ? "secondary" : "destructive"}>{task.status}</Badge></TableCell>
                      <TableCell>
                        <div className="w-24 bg-gray-100 rounded-full h-2">
                          <div className={`h-2 rounded-full ${task.status === "失败" ? "bg-red-400" : "bg-blue-500"}`} style={{ width: `${task.progress}%` }} />
                        </div>
                        <span className="ml-2 text-xs text-gray-500">{task.progress}%</span>
                      </TableCell>
                      <TableCell>{task.time}</TableCell>
                      <TableCell>
                        <Button size="sm" variant="ghost"><FileText className="w-4 h-4 mr-1" />日志</Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  )
} 