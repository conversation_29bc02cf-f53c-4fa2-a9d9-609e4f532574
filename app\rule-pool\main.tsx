/*
 * @Author: king<PERSON>y
 * @Date: 2025-06-24 12:10:06
 * @LastEditTime: 2025-06-24 13:27:50
 * @LastEditors: kingasky
 * @Description: 
 * @FilePath: \datamind-web\rule-pool.tsx
 */
"use client"
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Table, TableHeader, TableRow, TableHead, TableBody, TableCell } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { useRouter } from "next/navigation"

const mockRules = [
  {
    id: 1,
    name: "高危营销活动稽查",
    source: "政策文件A.pdf",
    element: "高额返现、违规返点",
    status: "未解构"
  },
  {
    id: 2,
    name: "渠道返利稽查",
    source: "政策文件B.docx",
    element: "渠道返利、返现门槛",
    status: "已解构"
  }
]

export default function RulePool() {
  const router = useRouter()
  return (
    <div className="flex-1 space-y-6 p-6">
      <div className="space-y-2">
        <h1 className="text-3xl font-bold tracking-tight text-gray-900">
          稽查规则候选池
        </h1>
        <p className="text-gray-600">
          从非结构化政策文件中提取稽查要素，形成规则池，支持解构和可视化编排
        </p>
      </div>
      <Card>
        <CardHeader>
          <CardTitle>规则候选池列表</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>规则名称</TableHead>
                <TableHead>来源文件</TableHead>
                <TableHead>提取要素</TableHead>
                <TableHead>状态</TableHead>
                <TableHead>操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {mockRules.map((rule) => (
                <TableRow key={rule.id}>
                  <TableCell>{rule.name}</TableCell>
                  <TableCell>{rule.source}</TableCell>
                  <TableCell>{rule.element}</TableCell>
                  <TableCell>
                    <Badge>{rule.status}</Badge>
                  </TableCell>
                  <TableCell>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() =>
                        router.push('/rule-management?id=' + rule.id)
                      }
                    >
                      解构
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      className="ml-2"
                      onClick={() =>
                        router.push('/rule-generator?id=' + rule.id)
                      }
                    >
                      可视化编排
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  )
} 