/*
 * @Author: king<PERSON>y
 * @Date: 2025-06-27 14:43:46
 * @LastEditTime: 2025-06-27 15:49:35
 * @LastEditors: kingasky
 * @Description: 
 * @FilePath: \datamind-web\app\data-inspection\main.tsx
 */
"use client"
import { useState } from "react"
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Table, TableHeader, TableRow, TableHead, TableBody, TableCell } from "@/components/ui/table"
import { Tabs, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Separator } from "@/components/ui/separator"

interface Task {
  id: number
  name: string
  type: string
  schedule: string
  status: string
  nextRun: string
  log: string
  description?: string
  owner?: string
  createTime?: string
  lastRunTime?: string
  lastRunStatus?: string
  config?: string
}

const mockTasks: Task[] = [
  {
    id: 1,
    name: "电动汽车充电桩电量异常",
    type: "数据中台离线定时任务",
    schedule: "每天 02:00",
    status: "运行中",
    nextRun: "2024-06-10 02:00",
    log: "无异常",
    description: "负责管理数据中台离线数据处理的电动汽车充电桩电量异常任务的执行情况",
    owner: "张三",
    createTime: "2024-01-15 10:30:00",
    lastRunTime: "2024-06-09 02:00:00",
    lastRunStatus: "成功",
    config: "{\"timeout\": 3600, \"retry\": 3, \"alert\": true}"
  },
  {
    id: 2,
    name: "业务中台定时任务巡查示例",
    type: "业务中台定时任务",
    schedule: "每小时",
    status: "已暂停",
    nextRun: "2024-06-09 15:00",
    log: "上次执行成功",
    description: "监控业务中台定时任务的运行状态，及时发现异常情况",
    owner: "李四",
    createTime: "2024-02-20 14:15:00",
    lastRunTime: "2024-06-09 14:00:00",
    lastRunStatus: "成功",
    config: "{\"timeout\": 1800, \"retry\": 2, \"alert\": true}"
  }
]

export default function DataInspection() {
  const [tab, setTab] = useState("all")
  const [tasks, setTasks] = useState<Task[]>(mockTasks)
  const [detailModalOpen, setDetailModalOpen] = useState(false)
  const [editModalOpen, setEditModalOpen] = useState(false)
  const [selectedTask, setSelectedTask] = useState<Task | null>(null)
  const [editForm, setEditForm] = useState<Partial<Task>>({})

  const handleDetail = (task: Task) => {
    setSelectedTask(task)
    setDetailModalOpen(true)
  }

  const handleEdit = (task: Task) => {
    setSelectedTask(task)
    setEditForm({
      name: task.name,
      type: task.type,
      schedule: task.schedule,
      status: task.status,
      description: task.description,
      owner: task.owner,
      config: task.config
    })
    setEditModalOpen(true)
  }

  const handleSave = () => {
    if (selectedTask && editForm) {
      const updatedTasks = tasks.map(task => 
        task.id === selectedTask.id 
          ? { ...task, ...editForm }
          : task
      )
      setTasks(updatedTasks)
      setEditModalOpen(false)
      setSelectedTask(null)
      setEditForm({})
    }
  }

  const handleDelete = (taskId: number) => {
    if (confirm("确定要删除这个巡查任务吗？")) {
      setTasks(tasks.filter(task => task.id !== taskId))
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "运行中":
        return "bg-green-100 text-green-800"
      case "已暂停":
        return "bg-yellow-100 text-yellow-800"
      case "异常":
        return "bg-red-100 text-red-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  return (
    <div className="flex-1 space-y-6 p-6">
      <div className="space-y-2">
        <h1 className="text-3xl font-bold tracking-tight text-gray-900">巡查任务管理</h1>
        <p className="text-gray-600">统一管理数据中台离线定时任务、业务中台定时任务等数据巡查任务</p>
      </div>
      <Card>
        <CardHeader>
          <CardTitle>巡查任务列表</CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs value={tab} onValueChange={setTab} className="mb-4">
            <TabsList>
              <TabsTrigger value="all">全部</TabsTrigger>
              <TabsTrigger value="数据中台离线定时任务">数据中台离线定时任务</TabsTrigger>
              <TabsTrigger value="业务中台定时任务">业务中台定时任务</TabsTrigger>
            </TabsList>
          </Tabs>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>任务名称</TableHead>
                <TableHead>类型</TableHead>
                <TableHead>调度</TableHead>
                <TableHead>状态</TableHead>
                <TableHead>下次执行</TableHead>
                <TableHead>操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {tasks.filter(t => tab === "all" || t.type === tab).map(task => (
                <TableRow key={task.id}>
                  <TableCell>{task.name}</TableCell>
                  <TableCell><Badge>{task.type}</Badge></TableCell>
                  <TableCell>{task.schedule}</TableCell>
                  <TableCell>
                    <Badge className={getStatusColor(task.status)}>
                      {task.status}
                    </Badge>
                  </TableCell>
                  <TableCell>{task.nextRun}</TableCell>
                  <TableCell>
                    <Button size="sm" variant="outline" onClick={() => handleDetail(task)}>
                      详情
                    </Button>
                    <Button size="sm" variant="outline" className="ml-2" onClick={() => handleEdit(task)}>
                      编辑
                    </Button>
                    <Button size="sm" variant="destructive" className="ml-2" onClick={() => handleDelete(task.id)}>
                      删除
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* 详情模态框 */}
      <Dialog open={detailModalOpen} onOpenChange={setDetailModalOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>任务详情</DialogTitle>
          </DialogHeader>
          {selectedTask && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium text-gray-500">任务名称</Label>
                  <p className="mt-1">{selectedTask.name}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-500">任务类型</Label>
                  <p className="mt-1">{selectedTask.type}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-500">调度时间</Label>
                  <p className="mt-1">{selectedTask.schedule}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-500">当前状态</Label>
                  <Badge className={`mt-1 ${getStatusColor(selectedTask.status)}`}>
                    {selectedTask.status}
                  </Badge>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-500">下次执行</Label>
                  <p className="mt-1">{selectedTask.nextRun}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-500">负责人</Label>
                  <p className="mt-1">{selectedTask.owner}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-500">创建时间</Label>
                  <p className="mt-1">{selectedTask.createTime}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-500">上次执行</Label>
                  <p className="mt-1">{selectedTask.lastRunTime}</p>
                </div>
              </div>
              <Separator />
              <div>
                <Label className="text-sm font-medium text-gray-500">任务描述</Label>
                <p className="mt-1 text-sm text-gray-600">{selectedTask.description}</p>
              </div>
              <div>
                <Label className="text-sm font-medium text-gray-500">执行日志</Label>
                <p className="mt-1 text-sm text-gray-600">{selectedTask.log}</p>
              </div>
              <div>
                <Label className="text-sm font-medium text-gray-500">配置信息</Label>
                <pre className="mt-1 text-xs bg-gray-50 p-2 rounded border overflow-auto">
                  {selectedTask.config}
                </pre>
              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setDetailModalOpen(false)}>
              关闭
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 编辑模态框 */}
      <Dialog open={editModalOpen} onOpenChange={setEditModalOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>编辑任务</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="name">任务名称</Label>
                <Input
                  id="name"
                  value={editForm.name || ""}
                  onChange={(e) => setEditForm({...editForm, name: e.target.value})}
                />
              </div>
              <div>
                <Label htmlFor="type">任务类型</Label>
                <Select value={editForm.type} onValueChange={(value) => setEditForm({...editForm, type: value})}>
                  <SelectTrigger>
                    <SelectValue placeholder="选择任务类型" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="数据中台离线定时任务">数据中台离线定时任务</SelectItem>
                    <SelectItem value="业务中台定时任务">业务中台定时任务</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="schedule">调度时间</Label>
                <Input
                  id="schedule"
                  value={editForm.schedule || ""}
                  onChange={(e) => setEditForm({...editForm, schedule: e.target.value})}
                  placeholder="例如：每天 02:00"
                />
              </div>
              <div>
                <Label htmlFor="status">状态</Label>
                <Select value={editForm.status} onValueChange={(value) => setEditForm({...editForm, status: value})}>
                  <SelectTrigger>
                    <SelectValue placeholder="选择状态" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="运行中">运行中</SelectItem>
                    <SelectItem value="已暂停">已暂停</SelectItem>
                    <SelectItem value="异常">异常</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="owner">负责人</Label>
                <Input
                  id="owner"
                  value={editForm.owner || ""}
                  onChange={(e) => setEditForm({...editForm, owner: e.target.value})}
                />
              </div>
            </div>
            <div>
              <Label htmlFor="description">任务描述</Label>
              <Textarea
                id="description"
                value={editForm.description || ""}
                onChange={(e) => setEditForm({...editForm, description: e.target.value})}
                placeholder="请输入任务描述"
                rows={3}
              />
            </div>
            <div>
              <Label htmlFor="config">配置信息</Label>
              <Textarea
                id="config"
                value={editForm.config || ""}
                onChange={(e) => setEditForm({...editForm, config: e.target.value})}
                placeholder="请输入JSON格式的配置信息"
                rows={4}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setEditModalOpen(false)}>
              取消
            </Button>
            <Button onClick={handleSave}>
              保存
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
} 