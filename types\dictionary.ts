// 数据字典类型定义
export interface DictionaryValue {
  value: string;
  label: string;
  description?: string;
}

export interface DictionaryField {
  name: string;
  type: string;
  description?: string;
  values?: DictionaryValue[];
  businessMeaning?: string;
}

export interface DictionaryEntry {
  table: string;
  fields: DictionaryField[];
  description?: string;
}

export type Dictionary = DictionaryEntry[]; 