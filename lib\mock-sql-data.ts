import type { SQLParseResult, Relation, TableReference, ColumnReference } from '@/types/sql-analysis'

// 真实的电力行业SQL示例
export const mockSQLStatements = [
  {
    id: 'sql_001',
    name: '用户电量异常检测',
    description: '检测用电量异常的用户',
    sql: `SELECT 
      u.user_id,
      u.user_name,
      u.user_address,
      m.meter_id,
      m.meter_type,
      c.consumption_value,
      c.reading_date,
      p.price_type
    FROM dm_user_info u
    INNER JOIN dm_meter_info m ON u.user_id = m.user_id
    INNER JOIN dm_consumption_data c ON m.meter_id = c.meter_id
    LEFT JOIN dm_price_policy p ON u.price_policy_id = p.policy_id
    WHERE c.consumption_value > 5000
      AND c.reading_date >= DATE_SUB(CURDATE(), INTERVAL 1 MONTH)
      AND u.user_type = '01'  -- 居民用户
    ORDER BY c.consumption_value DESC`,
    category: '数据巡查',
    complexity: 'medium'
  },
  {
    id: 'sql_002',
    name: '充电桩用户识别',
    description: '识别可能的充电桩用户',
    sql: `SELECT DISTINCT
      u.user_id,
      u.user_name,
      u.user_address,
      AVG(c.consumption_value) as avg_consumption,
      COUNT(*) as reading_count
    FROM dm_user_info u
    JOIN dm_meter_info m ON u.user_id = m.user_id
    JOIN dm_consumption_data c ON m.meter_id = c.meter_id
    WHERE (u.user_name LIKE '%充电%' OR u.user_address LIKE '%充电%')
      AND u.price_type = '居民合表电价'
      AND c.reading_date >= '2024-01-01'
    GROUP BY u.user_id, u.user_name, u.user_address
    HAVING AVG(c.consumption_value) > 3000`,
    category: '营销稽查',
    complexity: 'high'
  },
  {
    id: 'sql_003',
    name: '电表档案完整性检查',
    description: '检查电表档案信息完整性',
    sql: `SELECT 
      m.meter_id,
      m.meter_no,
      m.meter_type,
      m.install_date,
      u.user_id,
      u.user_name,
      CASE 
        WHEN m.meter_status IS NULL THEN '状态缺失'
        WHEN u.user_name IS NULL THEN '用户信息缺失'
        WHEN m.install_date IS NULL THEN '安装日期缺失'
        ELSE '完整'
      END as data_quality_status
    FROM dm_meter_info m
    LEFT JOIN dm_user_info u ON m.user_id = u.user_id
    WHERE m.meter_status IN ('01', '02', '03')  -- 01:正常 02:停用 03:故障
    ORDER BY m.install_date DESC`,
    category: '数据质量',
    complexity: 'low'
  },
  {
    id: 'sql_004',
    name: '台区负荷分析',
    description: '分析台区负荷分布情况',
    sql: `WITH transformer_load AS (
      SELECT 
        t.transformer_id,
        t.transformer_name,
        t.capacity,
        SUM(c.consumption_value) as total_consumption,
        COUNT(DISTINCT m.meter_id) as meter_count
      FROM dm_transformer_info t
      LEFT JOIN dm_meter_info m ON t.transformer_id = m.transformer_id
      LEFT JOIN dm_consumption_data c ON m.meter_id = c.meter_id
      WHERE c.reading_date >= DATE_SUB(CURDATE(), INTERVAL 1 MONTH)
      GROUP BY t.transformer_id, t.transformer_name, t.capacity
    )
    SELECT 
      tl.*,
      ROUND((tl.total_consumption / tl.capacity) * 100, 2) as load_rate,
      CASE 
        WHEN (tl.total_consumption / tl.capacity) > 0.8 THEN '高负荷'
        WHEN (tl.total_consumption / tl.capacity) > 0.6 THEN '中负荷'
        ELSE '低负荷'
      END as load_level
    FROM transformer_load tl
    ORDER BY load_rate DESC`,
    category: '运行分析',
    complexity: 'high'
  },
  {
    id: 'sql_005',
    name: '客户投诉关联分析',
    description: '分析客户投诉与用电异常的关联',
    sql: `SELECT 
      comp.complaint_id,
      comp.complaint_date,
      comp.complaint_type,
      u.user_id,
      u.user_name,
      u.user_address,
      m.meter_id,
      AVG(c.consumption_value) as avg_consumption,
      MAX(c.consumption_value) as max_consumption
    FROM dm_complaint_info comp
    INNER JOIN dm_user_info u ON comp.user_id = u.user_id
    INNER JOIN dm_meter_info m ON u.user_id = m.user_id
    LEFT JOIN dm_consumption_data c ON m.meter_id = c.meter_id 
      AND c.reading_date BETWEEN DATE_SUB(comp.complaint_date, INTERVAL 7 DAY) 
                              AND DATE_ADD(comp.complaint_date, INTERVAL 7 DAY)
    WHERE comp.complaint_type IN ('电量异常', '电费异常', '停电')
      AND comp.complaint_date >= '2024-01-01'
    GROUP BY comp.complaint_id, comp.complaint_date, comp.complaint_type, 
             u.user_id, u.user_name, u.user_address, m.meter_id
    ORDER BY comp.complaint_date DESC`,
    category: '客户服务',
    complexity: 'high'
  }
]

// Mock解析结果
export const mockParseResults: SQLParseResult[] = [
  {
    id: 'parse_001',
    sql_content: mockSQLStatements[0].sql,
    parse_status: 'SUCCESS',
    extracted_tables: [
      {
        name: 'dm_user_info',
        alias: 'u',
        type: 'BASE_TABLE',
        operations: ['SELECT']
      },
      {
        name: 'dm_meter_info',
        alias: 'm',
        type: 'BASE_TABLE',
        operations: ['JOIN']
      },
      {
        name: 'dm_consumption_data',
        alias: 'c',
        type: 'BASE_TABLE',
        operations: ['JOIN']
      },
      {
        name: 'dm_price_policy',
        alias: 'p',
        type: 'BASE_TABLE',
        operations: ['JOIN']
      }
    ],
    extracted_columns: [
      { name: 'user_id', table: 'dm_user_info', isCalculated: false },
      { name: 'user_name', table: 'dm_user_info', isCalculated: false },
      { name: 'user_address', table: 'dm_user_info', isCalculated: false },
      { name: 'meter_id', table: 'dm_meter_info', isCalculated: false },
      { name: 'meter_type', table: 'dm_meter_info', isCalculated: false },
      { name: 'consumption_value', table: 'dm_consumption_data', isCalculated: false },
      { name: 'reading_date', table: 'dm_consumption_data', isCalculated: false },
      { name: 'price_type', table: 'dm_price_policy', isCalculated: false }
    ],
    extracted_relations: [
      {
        id: 'rel_001',
        from_table: 'dm_user_info',
        from_column: 'user_id',
        to_table: 'dm_meter_info',
        to_column: 'user_id',
        relation_type: 'INNER_JOIN',
        confidence: 0.95,
        sql_source: mockSQLStatements[0].sql,
        join_condition: 'u.user_id = m.user_id',
        created_at: new Date()
      },
      {
        id: 'rel_002',
        from_table: 'dm_meter_info',
        from_column: 'meter_id',
        to_table: 'dm_consumption_data',
        to_column: 'meter_id',
        relation_type: 'INNER_JOIN',
        confidence: 0.95,
        sql_source: mockSQLStatements[0].sql,
        join_condition: 'm.meter_id = c.meter_id',
        created_at: new Date()
      },
      {
        id: 'rel_003',
        from_table: 'dm_user_info',
        from_column: 'price_policy_id',
        to_table: 'dm_price_policy',
        to_column: 'policy_id',
        relation_type: 'LEFT_JOIN',
        confidence: 0.90,
        sql_source: mockSQLStatements[0].sql,
        join_condition: 'u.price_policy_id = p.policy_id',
        created_at: new Date()
      }
    ],
    confidence_score: 0.93,
    created_at: new Date(),
    parsing_time_ms: 45
  }
]

// 数据字典Mock数据
export const mockDataDictionaries = [
  {
    table_name: 'dm_user_info',
    field_name: 'user_type',
    dictionary_table: 'sys_user_type_dict',
    code_field: 'type_code',
    name_field: 'type_name',
    entries: [
      { code: '01', name: '居民用户', description: '普通居民用电用户' },
      { code: '02', name: '工商业用户', description: '工商业用电用户' },
      { code: '03', name: '大工业用户', description: '大工业用电用户' },
      { code: '04', name: '临时用电用户', description: '临时用电用户' }
    ]
  },
  {
    table_name: 'dm_meter_info',
    field_name: 'meter_status',
    dictionary_table: 'sys_meter_status_dict',
    code_field: 'status_code',
    name_field: 'status_name',
    entries: [
      { code: '01', name: '正常', description: '电表运行正常' },
      { code: '02', name: '停用', description: '电表已停用' },
      { code: '03', name: '故障', description: '电表存在故障' },
      { code: '04', name: '维修', description: '电表正在维修' }
    ]
  },
  {
    table_name: 'dm_complaint_info',
    field_name: 'complaint_type',
    dictionary_table: 'sys_complaint_type_dict',
    code_field: 'type_code',
    name_field: 'type_name',
    entries: [
      { code: 'ELEC_ABNORMAL', name: '电量异常', description: '用电量异常投诉' },
      { code: 'BILL_ABNORMAL', name: '电费异常', description: '电费计算异常投诉' },
      { code: 'POWER_OUTAGE', name: '停电', description: '停电相关投诉' },
      { code: 'SERVICE_QUALITY', name: '服务质量', description: '服务质量投诉' }
    ]
  }
]

// 表结构元数据
export const mockTableMetadata = [
  {
    table_name: 'dm_user_info',
    table_comment: '用户基本信息表',
    fields: [
      { name: 'user_id', type: 'BIGINT', comment: '用户唯一标识', is_primary: true },
      { name: 'user_name', type: 'VARCHAR(100)', comment: '用户姓名', is_primary: false },
      { name: 'user_address', type: 'VARCHAR(200)', comment: '用电地址', is_primary: false },
      { name: 'user_type', type: 'VARCHAR(10)', comment: '用户类型', is_primary: false, has_dictionary: true },
      { name: 'price_policy_id', type: 'BIGINT', comment: '电价政策ID', is_primary: false },
      { name: 'create_time', type: 'DATETIME', comment: '创建时间', is_primary: false }
    ]
  },
  {
    table_name: 'dm_meter_info',
    table_comment: '电表信息表',
    fields: [
      { name: 'meter_id', type: 'BIGINT', comment: '电表唯一标识', is_primary: true },
      { name: 'meter_no', type: 'VARCHAR(50)', comment: '电表编号', is_primary: false },
      { name: 'meter_type', type: 'VARCHAR(20)', comment: '电表类型', is_primary: false },
      { name: 'user_id', type: 'BIGINT', comment: '用户ID', is_primary: false },
      { name: 'transformer_id', type: 'BIGINT', comment: '台区ID', is_primary: false },
      { name: 'meter_status', type: 'VARCHAR(10)', comment: '电表状态', is_primary: false, has_dictionary: true },
      { name: 'install_date', type: 'DATE', comment: '安装日期', is_primary: false }
    ]
  },
  {
    table_name: 'dm_consumption_data',
    table_comment: '用电量数据表',
    fields: [
      { name: 'consumption_id', type: 'BIGINT', comment: '用电记录ID', is_primary: true },
      { name: 'meter_id', type: 'BIGINT', comment: '电表ID', is_primary: false },
      { name: 'consumption_value', type: 'DECIMAL(10,2)', comment: '用电量', is_primary: false },
      { name: 'reading_date', type: 'DATE', comment: '抄表日期', is_primary: false },
      { name: 'reading_type', type: 'VARCHAR(10)', comment: '抄表类型', is_primary: false }
    ]
  },
  {
    table_name: 'dm_price_policy',
    table_comment: '电价政策表',
    fields: [
      { name: 'policy_id', type: 'BIGINT', comment: '政策ID', is_primary: true },
      { name: 'policy_name', type: 'VARCHAR(100)', comment: '政策名称', is_primary: false },
      { name: 'price_type', type: 'VARCHAR(50)', comment: '电价类型', is_primary: false },
      { name: 'unit_price', type: 'DECIMAL(8,4)', comment: '单价', is_primary: false }
    ]
  }
]

// 解析统计数据
export const mockAnalysisStats = {
  total_sql_parsed: 25,
  success_rate: 92,
  total_relations_found: 67,
  total_tables_analyzed: 15,
  avg_confidence_score: 0.87,
  parsing_errors: [
    {
      sql_content: 'SELECT * FROM invalid_table WHERE',
      error_type: 'SYNTAX_ERROR' as const,
      error_message: 'Incomplete WHERE clause',
      line_number: 1,
      column_number: 35,
      timestamp: new Date()
    }
  ]
}

// 关系图谱数据
export const mockRelationGraphData = {
  nodes: [
    { id: 'dm_user_info', label: '用户信息', type: 'table' as const, metadata: { operations: ['SELECT', 'JOIN'], frequency: 15 } },
    { id: 'dm_meter_info', label: '电表信息', type: 'table' as const, metadata: { operations: ['JOIN'], frequency: 12 } },
    { id: 'dm_consumption_data', label: '用电量数据', type: 'table' as const, metadata: { operations: ['JOIN'], frequency: 10 } },
    { id: 'dm_price_policy', label: '电价政策', type: 'table' as const, metadata: { operations: ['JOIN'], frequency: 8 } },
    { id: 'dm_transformer_info', label: '台区信息', type: 'table' as const, metadata: { operations: ['JOIN'], frequency: 5 } },
    { id: 'dm_complaint_info', label: '投诉信息', type: 'table' as const, metadata: { operations: ['SELECT'], frequency: 3 } }
  ],
  edges: [
    {
      id: 'edge_001',
      source: 'dm_user_info',
      target: 'dm_meter_info',
      type: 'INNER_JOIN',
      confidence: 0.95,
      metadata: { sql_sources: ['用户电量异常检测'], join_conditions: ['u.user_id = m.user_id'], frequency: 8 }
    },
    {
      id: 'edge_002',
      source: 'dm_meter_info',
      target: 'dm_consumption_data',
      type: 'INNER_JOIN',
      confidence: 0.95,
      metadata: { sql_sources: ['用户电量异常检测'], join_conditions: ['m.meter_id = c.meter_id'], frequency: 10 }
    },
    {
      id: 'edge_003',
      source: 'dm_user_info',
      target: 'dm_price_policy',
      type: 'LEFT_JOIN',
      confidence: 0.90,
      metadata: { sql_sources: ['用户电量异常检测'], join_conditions: ['u.price_policy_id = p.policy_id'], frequency: 5 }
    }
  ],
  metadata: {
    total_nodes: 6,
    total_edges: 3,
    confidence_distribution: { high: 2, medium: 1, low: 0 },
    relation_types: { 'INNER_JOIN': 2, 'LEFT_JOIN': 1 }
  }
}
