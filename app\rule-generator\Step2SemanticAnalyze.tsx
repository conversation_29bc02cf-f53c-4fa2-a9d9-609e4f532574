import React, { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>ontent } from "@/components/ui/card"
import { Textarea } from "@/components/ui/textarea"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ger, <PERSON><PERSON><PERSON>ontent } from "@/components/ui/tabs"
import { HelpCircle, Star } from "lucide-react"
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd'
import Mermaid from 'react-mermaid2'

interface Step2SemanticAnalyzeProps {
  generatedDescription: string
  setGeneratedDescription: (d: string) => void
  semanticResult: string
  setSemanticResult: (s: string) => void
  prevStep: () => void
  nextStep: () => void
  setSemanticNLResult: (s: string) => void
  resultFields: { name: string }[]
  setResultFields: (fields: { name: string }[]) => void
  semanticAtoms: { name: string }[]
}

const Step2SemanticAnalyze: React.FC<Step2SemanticAnalyzeProps> = (props) => {
  const [view, setView] = useState("nl")
  const [isEdit, setIsEdit] = React.useState(false)
  // 编辑模式下的临时字段
  const [editFields, setEditFields] = React.useState<{ name: string }[]>(props.resultFields)
  // 编辑模式下的批量选择
  const [leftSelected, setLeftSelected] = React.useState<string[]>([])
  const [rightSelected, setRightSelected] = React.useState<string[]>([])
  const explanations = {
    target: "稽查目标对象是指本规则适用的用户或设备范围，例如：工商业用户、居民用户等。",
    period: "执行周期是指本规则进行核查的时间频率，如每月、每周、每日等。",
    points: "核查要点是指本规则关注的具体条件、阈值或异常点，是稽查的核心内容。"
  }
  const [helpOpen, setHelpOpen] = useState<string | null>(null)
  const [pointExplainOpen, setPointExplainOpen] = useState<number | null>(null)
  const [pointExplainText, setPointExplainText] = useState<string>("")
  const [pointExplainLoading, setPointExplainLoading] = useState(false)

  // 智能解析
  let target = "-"
  let period = "-"
  let points: string[] = []
  if (props.generatedDescription) {
    const m1 = props.generatedDescription.match(/(.*?)(每[月周日]筛查)/)
    if (m1) {
      target = m1[1].replace(/[，,]/g, "").trim()
      period = m1[2].replace(/[，,]/g, "").trim()
    }
    const m2 = props.generatedDescription.match(/开展符合以下情况的稽查监控：([\s\S]*)/)
    if (m2) {
      points = m2[1].split(/\n|。|；/).map(s => s.trim()).filter(Boolean)
    }
  }

  React.useEffect(() => {
    if (!isEdit) setEditFields(props.resultFields)
  }, [isEdit, props.resultFields])

  // 只读模式下的拖拽排序
  const onReadOnlyDragEnd = (result: any) => {
    if (!result.destination) return
    const newOrder = Array.from(props.resultFields)
    const [removed] = newOrder.splice(result.source.index, 1)
    newOrder.splice(result.destination.index, 0, removed)
    props.setResultFields(newOrder)
  }

  // 编辑模式下的拖拽排序
  const onEditDragEnd = (result: any) => {
    if (!result.destination) return
    const newOrder = Array.from(editFields)
    const [removed] = newOrder.splice(result.source.index, 1)
    newOrder.splice(result.destination.index, 0, removed)
    setEditFields(newOrder)
  }

  // 编辑模式下穿梭框数据
  const allFields = props.semanticAtoms
  const selectedNames = editFields.map(f => f.name)
  const availableFields = allFields.filter(f => !selectedNames.includes(f.name))
  const allLeftChecked = availableFields.length > 0 && leftSelected.length === availableFields.length
  const allRightChecked = editFields.length > 0 && rightSelected.length === editFields.length

  // 批量添加
  const handleAdd = () => {
    const toAdd = leftSelected.filter(name => !selectedNames.includes(name))
    if (toAdd.length > 0) {
      setEditFields([
        ...editFields,
        ...toAdd.map(name => ({ name }))
      ])
      setLeftSelected([])
    }
  }
  // 批量移除
  const handleRemove = () => {
    setEditFields(editFields.filter(f => !rightSelected.includes(f.name)))
    setRightSelected([])
  }

  // 保存/取消
  const handleSave = () => {
    props.setResultFields(editFields)
    setIsEdit(false)
  }
  const handleCancel = () => {
    setEditFields(props.resultFields)
    setIsEdit(false)
  }

  // 导出Excel（CSV实现）
  const handleExportExcel = () => {
    const csvContent = '字段名\n' + props.resultFields.map(f => f.name).join('\n')
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.setAttribute('download', '主题字段配置.csv')
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
  }

  // 模拟后端LLM+RAG接口（实际应调用后端API）
  const fetchPointExplanation = async (point: string) => {
    setPointExplainLoading(true)
    setPointExplainText("")
    // TODO: 替换为真实后端LLM+RAG接口
    await new Promise(r => setTimeout(r, 800))
    setPointExplainText(
      `<b>通俗化解释：</b><br/>"${point}"是本规则关注的关键核查要点，具体含义和业务背景可根据实际场景进一步解释。<br/><br/>` +
      `<b>举例说明：</b><br/>如：某用户在一个月内用电量超过5000千瓦时，即触发该要点。<br/><br/>` +
      `<b>政策依据：</b><br/>
      <a href='https://www.example.com/policy1' target='_blank' class='text-blue-600 underline'>《国家电网用电稽查管理办法》第12条</a>：对月用电量异常增长的用户应重点核查。<br/>
      <a href='https://www.example.com/policy2' target='_blank' class='text-blue-600 underline'>《地方电力条例》第8条</a>：用电异常行为需依据相关标准进行核查。`
    )
    setPointExplainLoading(false)
  }

  // 生成语义化表达逻辑（mock）
  const handleSemanticAnalyze = () => {
    props.setSemanticResult(`{
  "rule_id": "RULE_001",
  "rule_name": "${props.generatedDescription.slice(0, 10)}...",
  "description": "${props.generatedDescription}",
  "base_conditions": []
}`)
  }

  // 生成自然语言解释字符串
  const getNLString = () => {
    let target = "-"
    let period = "-"
    let points: string[] = []
    if (props.generatedDescription) {
      const m1 = props.generatedDescription.match(/(.*?)(每[月周日]筛查)/)
      if (m1) {
        target = m1[1].replace(/[，,]/g, "").trim()
        period = m1[2].replace(/[，,]/g, "").trim()
      }
      const m2 = props.generatedDescription.match(/开展符合以下情况的稽查监控：([\s\S]*)/)
      if (m2) {
        points = m2[1].split(/\n|。|；/).map(s => s.trim()).filter(Boolean)
      }
    }
    return `稽查对象：${target}\n执行周期：${period}\n核查要点：${points.length > 0 ? points.join('；') : '无'}`
  }

  // 目标结果字段自动分析（mock接口）
  const fetchResultFields = async (description: string): Promise<{ name: string }[]> => {
    // TODO: 替换为真实后端接口
    await new Promise(r => setTimeout(r, 600))
    // 简单规则：如果描述包含"充电设施"，返回示例，否则返回通用字段
    if (description.includes('充电设施')) {
      return [
        { name: '异常标识' },
        { name: '客户编号' },
        { name: '客户名称' },
        { name: '客户地址' },
        { name: '上月度数' },
        { name: '电价类型' },
        { name: '计量点编号' },
        { name: '容量' },
        { name: '供电单位' },
        { name: '用户分类' }
      ]
    }
    return [
      { name: '异常标识' },
      { name: '客户编号' },
      { name: '客户名称' },
    ]
  }

  // 目标结果字段自动分析副作用
  React.useEffect(() => {
    if (props.generatedDescription) {
      fetchResultFields(props.generatedDescription).then(fields => {
        // 仅在当前字段为空时自动填充，避免覆盖用户手动调整
        if (!props.resultFields || props.resultFields.length === 0) {
          props.setResultFields(fields)
        }
      })
    }
    // eslint-disable-next-line
  }, [props.generatedDescription])

  // 自然语言解释
  const renderNL = () => (
    <div className="p-4 bg-blue-50 rounded-lg text-blue-900 text-base leading-relaxed space-y-4">
      <div className="flex items-center justify-between">
        <span className="font-semibold">稽查对象</span>
        <button onClick={() => setHelpOpen('target')}><HelpCircle className="w-4 h-4 text-blue-400" /></button>
      </div>
      <div className="pl-2 mb-2">{target}</div>
      <div className="flex items-center justify-between">
        <span className="font-semibold">执行周期</span>
        <button onClick={() => setHelpOpen('period')}><HelpCircle className="w-4 h-4 text-blue-400" /></button>
      </div>
      <div className="pl-2 mb-2">{period}</div>
      <div className="flex items-center justify-between">
        <span className="font-semibold">核查要点</span>
        <button onClick={() => setHelpOpen('points')}><HelpCircle className="w-4 h-4 text-blue-400" /></button>
      </div>
      <ul className="list-none pl-0">
        {points.length > 0 ? points.map((p, i) => (
          <li key={i} className="flex items-center gap-2 mb-2">
            <button
              className="text-yellow-400 hover:text-yellow-500 flex-shrink-0"
              onClick={async () => {
                setPointExplainOpen(i)
                await fetchPointExplanation(p)
              }}
              title="AI智能解释"
              style={{ marginRight: 4 }}
            >
              <Star className="w-4 h-4" />
            </button>
            <span className="break-words">{p}</span>
            {/* AI解释弹窗 */}
            <Dialog open={pointExplainOpen === i} onOpenChange={() => setPointExplainOpen(null)}>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>要点智能解释</DialogTitle>
                </DialogHeader>
                <div className="text-blue-900 text-base p-2 min-h-12" dangerouslySetInnerHTML={{__html: pointExplainLoading ? 'AI 正在生成解释...' : pointExplainText}} />
              </DialogContent>
            </Dialog>
          </li>
        )) : <li>无</li>}
      </ul>
      <Dialog open={!!helpOpen} onOpenChange={() => setHelpOpen(null)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>说明</DialogTitle>
          </DialogHeader>
          <div className="text-blue-900 text-base p-2">
            {helpOpen && explanations[helpOpen as keyof typeof explanations]}
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )

  // 表格视图
  const renderTable = () => {
    let data: any = {}
    try {
      data = props.semanticResult ? JSON.parse(props.semanticResult) : {}
    } catch {}
    return (
      <div className="overflow-x-auto">
        {data && Object.keys(data).length > 0 ? (
          <table className="min-w-[400px] text-sm border rounded-lg bg-white">
            <tbody>
              {Object.entries(data).map(([key, value]) => (
                <tr key={key} className="border-b last:border-b-0">
                  <td className="p-2 font-semibold text-blue-700 whitespace-nowrap">{key}</td>
                  <td className="p-2 text-blue-900">{typeof value === 'object' ? JSON.stringify(value) : String(value)}</td>
                </tr>
              ))}
            </tbody>
          </table>
        ) : (
          <div className="text-blue-400">暂无结构化数据</div>
        )}
      </div>
    )
  }

  // 只读模式渲染
  const renderReadOnlyFields = () => (
    <Card className="bg-gradient-to-br from-blue-50 to-blue-100 border-blue-100 rounded-xl shadow-sm hover:shadow-lg h-full">
      <CardHeader className="pb-3 flex flex-row items-center justify-between">
        <CardTitle className="text-blue-500 text-sm flex items-center gap-2">
          目标结果字段 <span className="text-xs text-blue-400">（可拖拽排序）</span>
        </CardTitle>
        <div className="flex gap-2">
          <Button size="sm" variant="outline" className="text-blue-500 border-blue-200" onClick={handleExportExcel}>导出Excel</Button>
          <Button size="sm" variant="outline" className="text-blue-500 border-blue-200" onClick={() => setIsEdit(true)}>编辑</Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="max-h-[360px] overflow-auto">
          <DragDropContext onDragEnd={onReadOnlyDragEnd}>
            <Droppable droppableId="readonly-droppable">
              {(provided) => (
                <div ref={provided.innerRef} {...provided.droppableProps}>
                  {props.resultFields.map((f, idx) => (
                    <Draggable key={f.name} draggableId={f.name} index={idx}>
                      {(provided, snapshot) => (
                        <div
                          ref={provided.innerRef}
                          {...provided.draggableProps}
                          {...provided.dragHandleProps}
                          className={`flex items-center gap-2 py-1 px-2 mb-1 bg-white rounded border border-blue-100 select-none ${snapshot.isDragging ? 'shadow-lg' : ''}`}
                          style={{ cursor: 'move', ...provided.draggableProps.style }}
                        >
                          <span>{f.name}</span>
                        </div>
                      )}
                    </Draggable>
                  ))}
                  {provided.placeholder}
                  {props.resultFields.length === 0 && <div className="text-xs text-blue-300 py-2">无</div>}
                </div>
              )}
            </Droppable>
          </DragDropContext>
        </div>
      </CardContent>
    </Card>
  )

  // 编辑模式渲染
  const renderEditFields = () => (
    <Card className="bg-gradient-to-br from-blue-50 to-blue-100 border-blue-100 rounded-xl shadow-sm hover:shadow-lg h-full">
      <CardHeader className="pb-3 flex flex-row items-center justify-between">
        <CardTitle className="text-blue-500 text-sm flex items-center gap-2">
          目标结果字段 <span className="text-xs text-blue-400">（穿梭框批量+右侧拖拽排序）</span>
        </CardTitle>
        <div className="flex gap-2">
          <Button size="sm" variant="outline" className="text-blue-500 border-blue-200" onClick={handleExportExcel}>导出Excel</Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="flex gap-4 max-h-[360px] overflow-auto">
          {/* 左侧：待选字段 */}
          <div className="flex-1 min-w-[120px] border rounded bg-white p-2 flex flex-col">
            <div className="font-bold text-xs mb-2 flex items-center">
              <input type="checkbox" checked={allLeftChecked} onChange={e => setLeftSelected(e.target.checked ? availableFields.map(f => f.name) : [])} />
              <span className="ml-1">待选字段</span>
              <span className="ml-auto text-blue-300 text-xs">{leftSelected.length}/{availableFields.length}</span>
            </div>
            <div className="flex-1 overflow-auto">
              {availableFields.map(f => (
                <label key={f.name} className="flex items-center gap-2 py-1 cursor-pointer select-none">
                  <input
                    type="checkbox"
                    checked={leftSelected.includes(f.name)}
                    onChange={e => setLeftSelected(e.target.checked ? [...leftSelected, f.name] : leftSelected.filter(n => n !== f.name))}
                  />
                  <span>{f.name}</span>
                </label>
              ))}
              {availableFields.length === 0 && <div className="text-xs text-blue-300 py-2">无</div>}
            </div>
          </div>
          {/* 中间按钮区 */}
          <div className="flex flex-col justify-center gap-2">
            <Button size="sm" variant="outline" className="px-2" onClick={handleAdd} disabled={leftSelected.length === 0}>→</Button>
            <Button size="sm" variant="outline" className="px-2" onClick={handleRemove} disabled={rightSelected.length === 0}>←</Button>
          </div>
          {/* 右侧：已选字段（可拖拽排序） */}
          <div className="flex-1 min-w-[120px] border rounded bg-white p-2 flex flex-col">
            <div className="font-bold text-xs mb-2 flex items-center">
              <input type="checkbox" checked={allRightChecked} onChange={e => setRightSelected(e.target.checked ? editFields.map(f => f.name) : [])} />
              <span className="ml-1">已选字段</span>
              <span className="ml-auto text-blue-300 text-xs">{rightSelected.length}/{editFields.length}</span>
            </div>
            <div className="flex-1 overflow-auto">
              <DragDropContext onDragEnd={onEditDragEnd}>
                <Droppable droppableId="selected-droppable">
                  {(provided) => (
                    <div ref={provided.innerRef} {...provided.droppableProps}>
                      {editFields.map((f, idx) => (
                        <Draggable key={f.name} draggableId={f.name} index={idx}>
                          {(provided, snapshot) => (
                            <label
                              ref={provided.innerRef}
                              {...provided.draggableProps}
                              {...provided.dragHandleProps}
                              className={`flex items-center gap-2 py-1 px-2 mb-1 bg-white rounded border border-blue-100 select-none ${snapshot.isDragging ? 'shadow-lg' : ''}`}
                              style={{ cursor: 'move', ...provided.draggableProps.style }}
                            >
                              <input
                                type="checkbox"
                                checked={rightSelected.includes(f.name)}
                                onChange={e => setRightSelected(e.target.checked ? [...rightSelected, f.name] : rightSelected.filter(n => n !== f.name))}
                              />
                              <span>{f.name}</span>
                            </label>
                          )}
                        </Draggable>
                      ))}
                      {provided.placeholder}
                      {editFields.length === 0 && <div className="text-xs text-blue-300 py-2">无</div>}
                    </div>
                  )}
                </Droppable>
              </DragDropContext>
            </div>
          </div>
        </div>
        <div className="flex justify-end gap-2 mt-4">
          <Button size="sm" variant="outline" className="text-blue-500 border-blue-200" onClick={handleCancel}>取消</Button>
          <Button size="sm" className="bg-blue-500 text-white" onClick={handleSave}>保存</Button>
        </div>
      </CardContent>
    </Card>
  )

  // 生成流程图的 Mermaid DSL（静态示例，可后续自动生成）
  function generateMermaidFromRule(rule: any): string {
    return `
flowchart TD
  Start["开始评估"]
  A["充电业务相关用户"]
  B["执行居民合表电价"]
  C["抄表周期类型"]
  D1["月发行电量 >= 5000 kWh"]
  D2["月发行电量 >= 10000 kWh"]
  E1["保留不稽查"]
  E2["开展稽查"]
  End["流程结束"]

  Start --> A
  A -- 否 --> End
  A -- 是 --> B
  B -- 否 --> End
  B -- 是 --> C
  C -- 单月 --> D1
  C -- 双月 --> D2
  D1 -- 否 --> E1
  D1 -- 是 --> E2
  D2 -- 否 --> E1
  D2 -- 是 --> E2
  E1 --> End
  E2 --> End
  `;
  }

  return (
    <div className="space-y-6">
      {/* 横向排列：规则描述+目标字段 */}
      <div className="flex flex-col md:flex-row gap-4">
        <div className="flex-1 md:basis-1/3 min-w-0">{/* 左侧：规则描述，稍窄 */}
          <Card className="bg-gradient-to-br from-blue-50 to-blue-100 border-blue-100 rounded-xl transition-colors duration-300 shadow-sm hover:shadow-lg h-full">
            <CardHeader className="pb-3">
              <CardTitle className="text-blue-500 text-sm flex items-center gap-2">
                主题规则描述
              </CardTitle>
            </CardHeader>
            <CardContent>
              <Textarea
                className="bg-blue-100 p-4 rounded-lg border min-h-28 border-blue-200 text-blue-900 transition-colors duration-300 focus:ring-2 focus:ring-blue-200"
                value={props.generatedDescription}
                onChange={(e) => props.setGeneratedDescription(e.target.value)}
              />
            </CardContent>
          </Card>
        </div>
        <div className="flex-1 md:basis-2/3 min-w-0">{/* 右侧：目标字段，稍宽 */}
          {isEdit ? renderEditFields() : renderReadOnlyFields()}
        </div>
      </div>
      {/* 语义化解释卡片 */}
      <Card className="bg-gradient-to-br from-blue-50 to-blue-100 border-blue-100 rounded-xl transition-colors duration-300 shadow-sm hover:shadow-lg">
        <CardHeader className="pb-3">
          <CardTitle className="text-blue-500 text-sm flex items-center gap-2">
            主题语义化解释
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs value={view} onValueChange={setView} className="w-full">
            <TabsList className="mb-2">
              <TabsTrigger value="nl">自然语言解释</TabsTrigger>
              {/* <TabsTrigger value="table">语义逻辑可视化</TabsTrigger> */}
              <TabsTrigger value="flow">语义逻辑可视化</TabsTrigger>
            </TabsList>
            <TabsContent value="nl">{renderNL()}</TabsContent>
            {/* <TabsContent value="table">{renderTable()}</TabsContent> */}
            <TabsContent value="flow">
              <div className="p-4 bg-white rounded-lg border border-blue-100 overflow-auto">
                <Mermaid value={generateMermaidFromRule(props)} />
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
      <div className="flex gap-4 pt-4 border-t">
        <Button
          variant="outline"
          className="flex-1 rounded-full bg-blue-100 text-blue-700 border-blue-200 transition-colors duration-300 hover:bg-blue-200"
          onClick={props.prevStep}
        >
          上一步
        </Button>
        <Button
          className="flex-1 bg-blue-400/80 text-white hover:bg-blue-400 rounded-full transition-colors duration-300 shadow-sm hover:shadow-lg"
          onClick={handleSemanticAnalyze}
        >
          更新语义化表达
        </Button>
        <Button
          className="flex-1 bg-blue-500 text-white hover:bg-blue-600 rounded-full transition-colors duration-300 shadow-sm hover:shadow-lg"
          onClick={props.nextStep}
        >
          下一步
        </Button>
      </div>
    </div>
  )
}

export default Step2SemanticAnalyze 