# 命中范围样本性验证功能说明

## 功能概述

命中范围样本性验证功能为稽查主题规则迭代页面提供了规则验证环境，支持手动或自动化验证，帮助用户评估规则的有效性和准确性，并提供是否需要调整主题逻辑的反馈。

## 功能特性

### 1. 验证控制面板
- **验证模式选择**：支持自动化验证和手动验证两种模式
  - 自动化验证：批量验证多个样本数据
  - 手动验证：逐个验证样本数据
- **样本数量显示**：实时显示当前验证模式下的样本数量
- **验证进度**：可视化显示验证进度，提供实时反馈

### 2. 验证结果分析
- **概览统计**：提供命中样本数、未命中样本数、准确率、F1分数等关键指标
- **详细结果**：展示每个样本的验证结果，包括命中状态、置信度、原因分析
- **分析建议**：基于验证结果提供规则优化建议和下一步行动指导

### 3. 结果过滤和搜索
- **状态过滤**：按命中/未命中状态过滤结果
- **关键词搜索**：支持按样本ID或原因进行搜索
- **结果导出**：支持将验证结果导出为文本文件

## 技术实现

### 组件结构
```
RuleValidationModal.tsx
├── 验证控制面板 (ValidationControlPanel)
├── 验证结果展示 (ValidationResults)
│   ├── 概览统计 (OverviewStats)
│   ├── 详细结果 (DetailedResults)
│   └── 分析建议 (AnalysisSuggestions)
└── 结果导出功能 (ExportResults)
```

### 核心功能模块

#### 1. 验证逻辑引擎
```typescript
const validateSample = (sample: any): ValidationResult => {
  // 基于规则的高耗能客户识别逻辑
  const isHit = 
    sample.customer_type === 'industrial' && 
    sample.monthly_consumption > 10000

  const confidence = isHit ? 0.95 : 0.85
  const reason = isHit 
    ? '符合高耗能客户条件：工商业客户且月用电量超过10000千瓦时'
    : '不符合高耗能客户条件'

  return {
    id: sample.id,
    sampleData: sample,
    isHit,
    confidence,
    reason,
    timestamp: new Date().toISOString(),
  }
}
```

#### 2. 统计计算引擎
```typescript
const calculateStats = () => {
  const results = validationResults
  const totalSamples = results.length
  const hitCount = results.filter(r => r.isHit).length
  const missCount = totalSamples - hitCount
  const accuracy = totalSamples > 0 ? (hitCount / totalSamples) * 100 : 0
  const precision = hitCount > 0 ? (hitCount / (hitCount + 2)) * 100 : 0
  const recall = hitCount > 0 ? (hitCount / (hitCount + 1)) * 100 : 0
  const f1Score = precision + recall > 0 ? (2 * precision * recall) / (precision + recall) : 0

  setValidationStats({
    totalSamples,
    hitCount,
    missCount,
    accuracy,
    precision,
    recall,
    f1Score,
  })
}
```

## 使用流程

### 1. 访问验证功能
- 在稽查主题规则迭代页面，点击任意规则的"查看"按钮
- 在规则详情弹窗中，点击"命中范围样本性验证"按钮
- 系统将打开验证模态框

### 2. 配置验证参数
- 选择验证模式（自动化/手动）
- 查看样本数量信息
- 点击"开始验证"按钮

### 3. 查看验证结果
- **概览统计**：查看整体验证指标
- **详细结果**：查看每个样本的具体验证结果
- **分析建议**：获取规则优化建议

### 4. 导出和后续操作
- 导出验证结果报告
- 根据建议进行规则迭代优化
- 或扩大验证范围进行更全面的测试

## 验证指标说明

### 1. 准确率 (Accuracy)
- 定义：正确预测的样本数占总样本数的比例
- 计算公式：`(命中数 + 正确未命中数) / 总样本数`
- 评估标准：≥80% 为良好，<80% 需要优化

### 2. 精确率 (Precision)
- 定义：预测为命中的样本中实际命中的比例
- 计算公式：`命中数 / (命中数 + 假阳性数)`
- 评估标准：越高越好，表示规则误报率低

### 3. 召回率 (Recall)
- 定义：实际命中的样本中被正确预测的比例
- 计算公式：`命中数 / (命中数 + 假阴性数)`
- 评估标准：越高越好，表示规则漏报率低

### 4. F1分数 (F1-Score)
- 定义：精确率和召回率的调和平均数
- 计算公式：`2 * (精确率 * 召回率) / (精确率 + 召回率)`
- 评估标准：综合评估指标，平衡精确率和召回率

## 优化建议

### 1. 规则逻辑优化
- 调整阈值参数（如用电量阈值）
- 增加维度条件（如地区维度）
- 优化过滤条件组合

### 2. 样本数据优化
- 扩大验证样本范围
- 增加边界情况测试
- 包含更多业务场景

### 3. 验证流程优化
- 定期进行验证测试
- 建立验证基准
- 跟踪验证指标变化趋势

## 集成说明

### 1. 与规则生成器集成
- 验证结果可直接用于规则迭代
- 提供"迭代优化规则"快捷入口
- 支持基于验证结果的规则调整

### 2. 与规则管理集成
- 验证历史记录保存
- 验证结果关联规则版本
- 支持批量验证功能

### 3. 与数据分析集成
- 验证结果可用于数据质量评估
- 支持样本数据的统计分析
- 提供数据洞察和趋势分析

## 未来扩展

### 1. 高级验证功能
- 支持自定义验证逻辑
- 提供机器学习模型验证
- 支持A/B测试验证

### 2. 自动化验证
- 定时自动验证
- 规则变更后自动触发验证
- 验证结果自动通知

### 3. 验证报告增强
- 可视化图表展示
- 趋势分析报告
- 对比分析功能

## 总结

命中范围样本性验证功能为稽查主题规则提供了全面的验证环境，通过科学的评估指标和直观的结果展示，帮助用户准确评估规则效果，为规则优化提供数据支撑。该功能与现有的规则生成器和规则管理系统无缝集成，形成了完整的规则生命周期管理闭环。 