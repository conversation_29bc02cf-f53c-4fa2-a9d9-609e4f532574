# 稽查知识管理功能

## 功能概述

稽查知识管理页面提供了一个集中化的界面，用于查看、管理和优化从政策文件中提取的稽查要素。

## 主要功能

### 1. 搜索和过滤
- **全局搜索**: 支持按要素名称和业务描述进行搜索
- **多维度过滤**: 
  - 要素类型 (实体、属性、事件、关系)
  - 审核状态 (待审核、已审核、需要修订)
  - 数据源 (政策文件来源)

### 2. 要素列表视图
- **表格展示**: 清晰展示所有稽查要素
- **关键信息**: ID、名称、类型、业务描述、数据源、提取日期、审核状态、标签
- **工具提示**: 长文本描述支持悬停查看完整内容
- **标签显示**: 支持多标签展示，超出部分用数字表示

### 3. 要素操作
每个要素支持以下操作：
- **查看/编辑**: 查看详细信息并编辑要素属性
- **批准**: 标记要素为已验证状态
- **标记修订**: 标记要素需要进一步人工审核
- **删除**: 移除不相关或错误的条目

### 4. 批量操作
- **批量选择**: 支持多选要素
- **批量批准**: 一次性批准多个要素
- **批量标记修订**: 批量标记需要修订的要素
- **批量删除**: 批量删除选中的要素

### 5. 统计信息
页面顶部显示关键统计信息：
- 总要素数
- 待审核数量
- 已审核数量
- 需要修订数量

### 6. 分页功能
- 支持大量数据的分页显示
- 每页显示10条记录
- 智能页码显示，支持省略号

### 7. 导出功能
- 支持导出当前筛选结果
- 便于数据分析和备份

## 技术实现

### 组件结构
```
app/audit-knowledge-management/
├── page.tsx              # 页面入口
├── main.tsx              # 主要组件
└── loading.tsx           # 加载状态组件
```

### 主要特性
- **响应式设计**: 支持桌面和移动端
- **TypeScript**: 完整的类型定义
- **状态管理**: 使用React Hooks管理组件状态
- **性能优化**: 使用useMemo优化过滤和分页计算
- **用户体验**: 加载状态、工具提示、确认对话框

### 数据模型
```typescript
interface AuditElement {
  id: string
  name: string
  type: 'Entity' | 'Attribute' | 'Event' | 'Relationship'
  businessDescription: string
  source: string
  extractionDate: string
  reviewStatus: 'Pending Review' | 'Reviewed' | 'To Be Revised'
  tags: string[]
  originalText?: string
}
```

## 使用说明

### 访问页面
1. 在侧边栏导航中找到"稽查知识管理"
2. 点击进入页面

### 搜索要素
1. 在搜索框中输入关键词
2. 支持要素名称和业务描述的模糊搜索

### 过滤数据
1. 使用要素类型下拉菜单选择特定类型
2. 使用审核状态下拉菜单筛选状态
3. 使用数据源下拉菜单选择来源

### 编辑要素
1. 点击要素行的"编辑"按钮
2. 在弹出的对话框中修改信息
3. 点击"保存更改"确认修改

### 批量操作
1. 使用复选框选择多个要素
2. 点击批量操作按钮执行相应操作

## 设计原则

### 一致性
- 与现有平台设计保持一致
- 使用统一的UI组件库
- 保持交互模式的一致性

### 清晰性
- 清晰的标签和导航
- 直观的数据展示
- 明确的操作反馈

### 可用性
- 设计简洁易用
- 支持快速查找和管理
- 提供清晰的状态反馈

## 未来扩展

### 计划功能
- [ ] 要素关系图谱可视化
- [ ] 版本控制和变更历史
- [ ] 协作审核功能
- [ ] 自动标签推荐
- [ ] 要素质量评分
- [ ] 批量导入功能

### 性能优化
- [ ] 虚拟滚动支持大量数据
- [ ] 服务端分页
- [ ] 缓存优化
- [ ] 搜索索引优化 