import React from "react"

interface LogicSelectorProps {
  value: "AND" | "OR" | "NOT"
  onChange: (val: "AND" | "OR" | "NOT") => void
  className?: string
}

const logicOptions = [
  { value: "AND", label: "AND（与）" },
  { value: "OR", label: "OR（或）" },
  { value: "NOT", label: "NOT（非）" },
]

const LogicSelector: React.FC<LogicSelectorProps> = ({ value, onChange, className }) => (
  <select
    value={value}
    onChange={e => onChange(e.target.value as "AND" | "OR" | "NOT")}
    className={className || "w-20 h-7 text-xs"}
  >
    {logicOptions.map(opt => (
      <option key={opt.value} value={opt.value}>{opt.label}</option>
    ))}
  </select>
)

export default LogicSelector 