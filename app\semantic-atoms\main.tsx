"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { 
  Search, 
  Filter, 
  Plus, 
  Eye, 
  Edit, 
  Copy,
  MoreHorizontal,
  Brain,
  Users
} from "lucide-react"

const mockAtoms = [
  {
    id: 1,
    name: "用户名称含'充电'",
    type: "基础原子",
    category: "电力营销-营销稽查",
    description: "筛选营销业务用户名称包含'充电'字段的用户。",
    usage: 12,
    status: "active",
    tags: ["用户名称", "充电", "筛选"],
    examples: [
      "用户名称: 'XXX充电服务公司'",
      "用户名称: '个人充电桩-李四'"
    ],
    definition: "判断'营销业务系统'中的'用户名称'字段是否包含'充电'关键词。",
    relatedAtoms: ["用户信息"],
    physicalMappings: [
      { table: "cst_info", field: "cst_name", fieldDesc: "用户名称" }
    ]
  },
  {
    id: 2,
    name: "用电地址含'充电'",
    type: "基础原子",
    category: "电力营销-营销稽查",
    description: "筛选用电地址包含'充电'字段的用户。",
    usage: 15,
    status: "active",
    tags: ["用电地址", "充电", "筛选"],
    examples: [
      "用电地址: 'XX小区地下车库充电站'",
      "用电地址: 'XX大厦专用充电车位'"
    ],
    definition: "判断'营销业务系统'中的'用电地址'字段是否包含'充电'关键词。",
    relatedAtoms: ["用户信息"],
    physicalMappings: [
      { table: "cst_info", field: "elec_addr", fieldDesc: "用电地址" }
    ]
  },
  {
    id: 3,
    name: "居民合表电价",
    type: "基础原子",
    category: "电力营销-电费管理",
    description: "执行居民合表电价的用户。",
    usage: 88,
    status: "active",
    tags: ["电价", "居民合表"],
    examples: ["电价类别: '居民合表'"],
    definition: "判断用户的电价标准是否为'居民合表电价'。",
    relatedAtoms: ["电价信息", "用户信息"],
    physicalMappings: [
      { table: "cst_info", field: "price_code", fieldDesc: "电价编码" }
    ]
  },
  {
    id: 4,
    name: "单月用电量超过5000kWh",
    type: "派生原子",
    category: "电力营销-营销稽查",
    description: "抄表周期为'单月'的用户，月度发行电量（抄见电量）超过5000千瓦时。",
    usage: 8,
    status: "active",
    tags: ["用电量", "单月", "异常"],
    examples: ["月度用电量: 5100 kWh"],
    definition: "对于抄表周期为'单月'的用户，其'月度发行电量'需大于5000。",
    relatedAtoms: ["抄表周期", "月度用电量"],
    physicalMappings: [
      { table: "monthly_usage", field: "kwh", fieldDesc: "月度用电量" },
      { table: "cst_info", field: "read_cycle", fieldDesc: "抄表周期" }
    ]
  },
  {
    id: 5,
    name: "双月用电量超过10000kWh",
    type: "派生原子",
    category: "电力营销-营销稽查",
    description: "抄表周期为'双月'的用户，月度发行电量（抄见电量）超过10000千瓦时。",
    usage: 5,
    status: "draft",
    tags: ["用电量", "双月", "异常"],
    examples: ["双月总用电量: 12000 kWh"],
    definition: "对于抄表周期为'双月'的用户，其'月度发行电量'需大于10000。",
    relatedAtoms: ["抄表周期", "月度用电量"],
    physicalMappings: [
      { table: "monthly_usage", field: "kwh", fieldDesc: "月度用电量" },
      { table: "cst_info", field: "read_cycle", fieldDesc: "抄表周期" }
    ]
  },
  {
    id: 6,
    name: "充电桩电量异常稽查",
    type: "复合原子",
    category: "电力营销-营销稽查",
    description: "综合判断可能存在用电异常的电动汽车充电桩用户。",
    usage: 2,
    status: "active",
    tags: ["充电桩", "电量异常", "稽查"],
    examples: ["某充电站，执行居民电价，单月用电量突增至6000kWh"],
    definition: "筛选出名称或地址含'充电'，执行居民合表电价，且用电量（按单/双月周期）超阈值的用户。",
    relatedAtoms: [
      "用户名称含'充电'", 
      "用电地址含'充电'",
      "居民合表电价",
      "单月用电量超过5000kWh",
      "双月用电量超过10000kWh"
    ],
    physicalMappings: []
  }
]

const atomTypes = ["全部", "基础原子", "派生原子", "复合原子"]
const businessCategories = ["全部", "电力营销-业扩用检", "电力营销-营销稽查", "电力营销-计量管理", "电力营销-客户服务", "电力营销-电费管理"]

export default function SemanticAtoms() {
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedType, setSelectedType] = useState("全部")
  const [selectedCategory, setSelectedCategory] = useState("全部")
  const [selectedAtom, setSelectedAtom] = useState<any>(null)

  const filteredAtoms = mockAtoms.filter(atom => {
    const matchesSearch = atom.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         atom.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         atom.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
    const matchesType = selectedType === "全部" || atom.type === selectedType
    const matchesCategory = selectedCategory === "全部" || atom.category === selectedCategory
    
    return matchesSearch && matchesType && matchesCategory
  })

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">语义逻辑原子管理</h1>
          <p className="text-muted-foreground">
            管理语义逻辑原子，支持智能搜索、分类筛选和详细查看
          </p>
        </div>
        <Button>
          <Plus className="h-4 w-4 mr-2" />
          新建原子
        </Button>
      </div>

      <Card>
        <CardContent className="p-6">
          <div className="grid gap-4 md:grid-cols-4">
            <div className="relative">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="搜索原子名称、描述或标签..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={selectedType} onValueChange={setSelectedType}>
              <SelectTrigger>
                <SelectValue placeholder="选择原子类型" />
              </SelectTrigger>
              <SelectContent>
                {atomTypes.map((type) => (
                  <SelectItem key={type} value={type}>
                    {type}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select value={selectedCategory} onValueChange={setSelectedCategory}>
              <SelectTrigger>
                <SelectValue placeholder="选择业务分类" />
              </SelectTrigger>
              <SelectContent>
                {businessCategories.map((category) => (
                  <SelectItem key={category} value={category}>
                    {category}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Button variant="outline" className="w-full">
              <Filter className="h-4 w-4 mr-2" />
              高级筛选
            </Button>
          </div>
        </CardContent>
      </Card>

      <div className="grid gap-4">
        {filteredAtoms.map((atom) => (
          <Card key={atom.id} className="cursor-pointer hover:shadow-md transition-shadow">
            <CardContent className="p-6">
              <div className="flex items-start justify-between">
                <div className="flex-1 space-y-3">
                  <div className="flex items-center space-x-3">
                    <Brain className="h-5 w-5 text-blue-500" />
                    <div>
                      <h3 className="text-lg font-semibold">{atom.name}</h3>
                      <p className="text-sm text-muted-foreground">{atom.description}</p>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-4">
                    <Badge variant="outline">{atom.type}</Badge>
                    <Badge variant="secondary">{atom.category}</Badge>
                    <Badge variant={atom.status === 'active' ? 'default' : 'secondary'}>
                      {atom.status === 'active' ? '已激活' : '草稿'}
                    </Badge>
                    <div className="flex items-center space-x-1 text-sm text-muted-foreground">
                      <Users className="h-4 w-4" />
                      <span>使用 {atom.usage} 次</span>
                    </div>
                  </div>
                  
                  <div className="flex flex-wrap gap-2">
                    {atom.tags.map((tag, index) => (
                      <Badge key={index} variant="outline" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                </div>
                
                <div className="flex items-center space-x-2">
                  <Dialog>
                    <DialogTrigger asChild>
                      <Button 
                        variant="ghost" 
                        size="sm"
                        onClick={() => setSelectedAtom(atom)}
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
                      <DialogHeader>
                        <DialogTitle className="flex items-center space-x-2">
                          <Brain className="h-5 w-5 text-blue-500" />
                          <span>{atom.name}</span>
                        </DialogTitle>
                        <DialogDescription>{atom.description}</DialogDescription>
                      </DialogHeader>
                      
                      <Tabs defaultValue="definition" className="w-full">
                        <TabsList>
                          <TabsTrigger value="definition">定义说明</TabsTrigger>
                          <TabsTrigger value="examples">使用示例</TabsTrigger>
                          <TabsTrigger value="related">关联原子</TabsTrigger>
                        </TabsList>
                        
                        <TabsContent value="definition" className="space-y-4">
                          <Card>
                            <CardHeader>
                              <CardTitle className="text-lg">定义说明</CardTitle>
                            </CardHeader>
                            <CardContent>
                              <p className="text-sm leading-relaxed">{atom.definition}</p>
                            </CardContent>
                          </Card>
                          {atom.physicalMappings && atom.physicalMappings.length > 0 && (
                            <Card>
                              <CardHeader>
                                <CardTitle className="text-sm">关联系物理表字段</CardTitle>
                              </CardHeader>
                              <CardContent>
                                <table className="w-full text-sm border mb-2">
                                  <thead>
                                    <tr className="bg-gray-50">
                                      <th className="p-2 border">物理表名</th>
                                      <th className="p-2 border">字段名</th>
                                      <th className="p-2 border">字段说明</th>
                                    </tr>
                                  </thead>
                                  <tbody>
                                    {atom.physicalMappings.map((mapping, idx) => (
                                      <tr key={idx}>
                                        <td className="p-2 border">{mapping.table}</td>
                                        <td className="p-2 border">{mapping.field}</td>
                                        <td className="p-2 border">{mapping.fieldDesc}</td>
                                      </tr>
                                    ))}
                                  </tbody>
                                </table>
                              </CardContent>
                            </Card>
                          )}
                          <div className="grid gap-4 md:grid-cols-2">
                            <Card>
                              <CardHeader>
                                <CardTitle className="text-sm">基本信息</CardTitle>
                              </CardHeader>
                              <CardContent className="space-y-2">
                                <div className="flex justify-between">
                                  <span className="text-sm text-muted-foreground">类型：</span>
                                  <Badge variant="outline">{atom.type}</Badge>
                                </div>
                                <div className="flex justify-between">
                                  <span className="text-sm text-muted-foreground">业务分类：</span>
                                  <Badge variant="secondary">{atom.category}</Badge>
                                </div>
                                <div className="flex justify-between">
                                  <span className="text-sm text-muted-foreground">状态：</span>
                                  <Badge variant={atom.status === 'active' ? 'default' : 'secondary'}>
                                    {atom.status === 'active' ? '已激活' : '草稿'}
                                  </Badge>
                                </div>
                                <div className="flex justify-between">
                                  <span className="text-sm text-muted-foreground">使用次数：</span>
                                  <span className="text-sm font-medium">{atom.usage}</span>
                                </div>
                              </CardContent>
                            </Card>
                            
                            <Card>
                              <CardHeader>
                                <CardTitle className="text-sm">标签</CardTitle>
                              </CardHeader>
                              <CardContent>
                                <div className="flex flex-wrap gap-2">
                                  {atom.tags.map((tag, index) => (
                                    <Badge key={index} variant="outline">
                                      {tag}
                                    </Badge>
                                  ))}
                                </div>
                              </CardContent>
                            </Card>
                          </div>
                        </TabsContent>
                        
                        <TabsContent value="examples" className="space-y-4">
                          <Card>
                            <CardHeader>
                              <CardTitle className="text-lg">使用示例</CardTitle>
                            </CardHeader>
                            <CardContent>
                              <div className="space-y-3">
                                {atom.examples.map((example, index) => (
                                  <div key={index} className="p-3 bg-muted rounded-lg">
                                    <p className="text-sm">{example}</p>
                                  </div>
                                ))}
                              </div>
                            </CardContent>
                          </Card>
                        </TabsContent>
                        
                        <TabsContent value="related" className="space-y-4">
                          <Card>
                            <CardHeader>
                              <CardTitle className="text-lg">关联原子</CardTitle>
                            </CardHeader>
                            <CardContent>
                              <div className="grid gap-2">
                                {atom.relatedAtoms.map((related, index) => (
                                  <div key={index} className="flex items-center justify-between p-2 border rounded">
                                    <span className="text-sm font-medium">{related}</span>
                                    <Button variant="ghost" size="sm">
                                      <Eye className="h-4 w-4" />
                                    </Button>
                                  </div>
                                ))}
                              </div>
                            </CardContent>
                          </Card>
                        </TabsContent>
                      </Tabs>
                    </DialogContent>
                  </Dialog>
                  
                  <Button variant="ghost" size="sm">
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button variant="ghost" size="sm">
                    <Copy className="h-4 w-4" />
                  </Button>
                  <Button variant="ghost" size="sm">
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <div className="flex items-center justify-between">
        <p className="text-sm text-muted-foreground">
          显示 {filteredAtoms.length} 个原子，共 {mockAtoms.length} 个
        </p>
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm" disabled>
            上一页
          </Button>
          <Button variant="outline" size="sm">
            下一页
          </Button>
        </div>
      </div>
    </div>
  )
}
