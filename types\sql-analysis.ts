// SQL解析相关类型定义

export interface SQLParseResult {
  id: string
  sql_content: string
  parse_status: 'SUCCESS' | 'FAILED' | 'PARTIAL'
  extracted_tables: TableReference[]
  extracted_relations: Relation[]
  extracted_columns: ColumnReference[]
  confidence_score: number
  created_at: Date
  parsing_time_ms: number
}

export interface TableReference {
  name: string
  alias?: string
  schema?: string
  database?: string
  type: 'BASE_TABLE' | 'VIEW' | 'CTE'
  operations: SQLOperation[]
}

export interface ColumnReference {
  name: string
  table: string
  alias?: string
  dataType?: string
  isCalculated: boolean
  expression?: string
}

export interface Relation {
  id: string
  from_table: string
  from_column: string
  to_table: string
  to_column: string
  relation_type: 'INNER_JOIN' | 'LEFT_JOIN' | 'RIGHT_JOIN' | 'FULL_JOIN' | 'WHERE_CONDITION' | 'SUBQUERY'
  confidence: number
  sql_source: string
  join_condition?: string
  created_at: Date
}

export interface SQLOperation {
  type: 'SELECT' | 'INSERT' | 'UPDATE' | 'DELETE' | 'CREATE' | 'DROP' | 'ALTER'
  table: string
  columns: string[]
  conditions?: string[]
}

export interface JoinCondition {
  joinType: 'INNER' | 'LEFT' | 'RIGHT' | 'FULL' | 'CROSS'
  leftTable: string
  leftColumn: string
  rightTable: string
  rightColumn: string
  operator: '=' | '!=' | '<' | '>' | '<=' | '>=' | 'LIKE'
  originalSQL: string
}

export interface WhereCondition {
  leftOperand: string
  operator: string
  rightOperand: string
  logicalOperator?: 'AND' | 'OR'
  isTableJoin: boolean
  originalSQL: string
}

export interface InferredRelation extends Relation {
  inference_method: 'FIELD_NAME_SIMILARITY' | 'VALUE_PATTERN' | 'USAGE_FREQUENCY' | 'BUSINESS_RULE'
  evidence: string[]
  manual_verified: boolean
}

export interface SQLAnalysisStats {
  total_sql_parsed: number
  success_rate: number
  total_relations_found: number
  total_tables_analyzed: number
  avg_confidence_score: number
  parsing_errors: SQLParsingError[]
}

export interface SQLParsingError {
  sql_content: string
  error_type: 'SYNTAX_ERROR' | 'UNSUPPORTED_FEATURE' | 'TIMEOUT' | 'UNKNOWN'
  error_message: string
  line_number?: number
  column_number?: number
  timestamp: Date
}

export interface RelationGraph {
  nodes: GraphNode[]
  edges: GraphEdge[]
  metadata: {
    total_nodes: number
    total_edges: number
    confidence_distribution: Record<string, number>
    relation_types: Record<string, number>
  }
}

export interface GraphNode {
  id: string
  label: string
  type: 'table' | 'column'
  metadata: {
    schema?: string
    dataType?: string
    operations: string[]
    frequency: number
  }
  position?: { x: number; y: number }
}

export interface GraphEdge {
  id: string
  source: string
  target: string
  type: string
  confidence: number
  metadata: {
    sql_sources: string[]
    join_conditions: string[]
    frequency: number
  }
  style?: {
    color: string
    width: number
    dashArray?: string
  }
}

export interface SQLDialect {
  name: string
  version: string
  features: string[]
  keywords: string[]
  functions: string[]
}

export interface ParseOptions {
  dialect: 'mysql' | 'postgresql' | 'oracle' | 'sqlserver' | 'sqlite'
  strict_mode: boolean
  extract_relations: boolean
  infer_implicit_relations: boolean
  confidence_threshold: number
  max_parsing_time_ms: number
}

export interface RelationMiningConfig {
  enable_join_analysis: boolean
  enable_where_analysis: boolean
  enable_field_name_inference: boolean
  enable_value_pattern_inference: boolean
  min_confidence_threshold: number
  max_inference_depth: number
}

// 用于前端展示的简化类型
export interface SQLAnalysisResult {
  parseResult: SQLParseResult
  relationGraph: RelationGraph
  statistics: SQLAnalysisStats
  recommendations: string[]
}

export interface TableRelationSummary {
  tableName: string
  relatedTables: {
    name: string
    relationCount: number
    avgConfidence: number
    relationTypes: string[]
  }[]
  totalRelations: number
  confidenceScore: number
}
