"use client";

import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";

interface WorkOrder {
  id: string;
  title: string;
  type: string;
  status: string;
  submittedAt: string;
  score: number;
  complianceStatus: "compliant" | "non_compliant" | "pending";
}

interface InspectionResult {
  category: string;
  items: {
    standard: string;
    result: "pass" | "fail";
    score: number;
    details: string;
    suggestion?: string;
  }[];
}

const mockInspectionResults: InspectionResult[] = [
  {
    category: "响应完整性",
    items: [
      {
        standard: "问题描述完整性",
        result: "pass",
        score: 100,
        details: "问题描述包含了完整的背景信息、现象描述和影响范围",
      },
      {
        standard: "解决方案完整性",
        result: "pass",
        score: 95,
        details: "解决方案包含了具体的处理步骤和验证方法",
      },
    ],
  },
  {
    category: "附件规范性",
    items: [
      {
        standard: "图片清晰度",
        result: "fail",
        score: 60,
        details: "部分截图模糊，不易辨识",
        suggestion: "建议重新上传清晰的系统截图",
      },
      {
        standard: "附件相关性",
        result: "pass",
        score: 90,
        details: "附件内容与工单描述相符",
      },
    ],
  },
];

interface Props {
  workOrder: WorkOrder;
}

export default function InspectionDetail({ workOrder }: Props) {
  return (
    <div className="space-y-6">
      <div className="grid grid-cols-2 gap-4">
        <Card className="p-4">
          <h3 className="text-lg font-semibold mb-4">基本信息</h3>
          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-gray-500">工单编号</span>
              <span>{workOrder.id}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-500">标题</span>
              <span>{workOrder.title}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-500">类型</span>
              <span>{workOrder.type}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-500">状态</span>
              <span>{workOrder.status}</span>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <h3 className="text-lg font-semibold mb-4">质检结果</h3>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-gray-500">总体得分</span>
              <div className="flex items-center gap-2">
                <Progress value={workOrder.score} className="w-32" />
                <span className="font-semibold">{workOrder.score}分</span>
              </div>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-gray-500">合规状态</span>
              <Badge
                variant={
                  workOrder.complianceStatus === "compliant"
                    ? "outline"
                    : workOrder.complianceStatus === "non_compliant"
                    ? "destructive"
                    : "secondary"
                }
              >
                {workOrder.complianceStatus === "compliant" && "合规"}
                {workOrder.complianceStatus === "non_compliant" && "不合规"}
                {workOrder.complianceStatus === "pending" && "待检查"}
              </Badge>
            </div>
          </div>
        </Card>
      </div>

      <Card className="p-4">
        <h3 className="text-lg font-semibold mb-4">详细检查项</h3>
        <Tabs defaultValue={mockInspectionResults[0].category} className="w-full">
          <TabsList className="w-full justify-start">
            {mockInspectionResults.map((result) => (
              <TabsTrigger key={result.category} value={result.category}>
                {result.category}
              </TabsTrigger>
            ))}
          </TabsList>

          {mockInspectionResults.map((result) => (
            <TabsContent key={result.category} value={result.category}>
              <div className="space-y-4">
                {result.items.map((item, index) => (
                  <Card key={index} className="p-4">
                    <div className="flex items-center justify-between mb-2">
                      <span className="font-medium">{item.standard}</span>
                      <div className="flex items-center gap-4">
                        <Badge variant={item.result === "pass" ? "outline" : "destructive"}>
                          {item.result === "pass" ? "通过" : "不通过"}
                        </Badge>
                        <span className="font-semibold">{item.score}分</span>
                      </div>
                    </div>
                    <p className="text-gray-600 text-sm">{item.details}</p>
                    {item.suggestion && (
                      <p className="text-yellow-600 text-sm mt-2">建议：{item.suggestion}</p>
                    )}
                  </Card>
                ))}
              </div>
            </TabsContent>
          ))}
        </Tabs>
      </Card>
    </div>
  );
} 