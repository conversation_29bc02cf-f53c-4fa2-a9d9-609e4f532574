"use client"

import { useState, useEffect } from "react"
import { ArrowLeft, RefreshCw, AlertTriangle, TrendingUp, Lightbulb, FileText, BarChart3 } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Textarea } from "@/components/ui/textarea"

// Mock data for the problem hit report
const mockProblemReport = {
  problemId: "PROB_20240115_001",
  hitRule: "高耗能客户识别规则",
  hitCustomer: "上海某制造有限公司",
  hitDate: "2024-01-15",
  auditStatus: "待处理",
  customerType: "工商业",
  consumptionValue: "12,500",
  threshold: "10,000",
  billingPeriod: "2024年1月",
  ruleId: "RULE_001",
  explanation: `该客户"上海某制造有限公司"在2024-01-15的2024年1月计费周期月用电量12,500千瓦时，超过了所关联的"高耗能客户识别规则"中定义的10,000千瓦时阈值，且客户类型为工商业，符合规则"高耗能客户识别规则"的全部判断条件。

具体分析如下：
1. 用电量检查：实际用电量12,500kWh > 规则阈值10,000kWh ✓
2. 客户类型检查：实际客户类型"工商业" = 规则要求"工商业" ✓
3. 计费周期检查：数据完整性验证通过 ✓

此问题可能表示：
- 客户用电需求正常增长
- 生产规模扩大导致用电量增加
- 可能存在用电异常或数据录入错误
- 需要进一步核实用电合理性`,
  suggestions: `针对该用电异常情况，基于智能分析提供以下整改建议：

**immediate_actions（立即行动）：**
1. 现场核实电表读数，确认数据准确性
2. 联系客户了解近期生产经营情况变化
3. 核对客户类型与当前电价套餐是否匹配
4. 检查是否存在计量设备故障或异常

**follow_up_measures（后续措施）：**
1. 建议客户优化用电结构，合理安排生产时间
2. 评估是否需要调整电价套餐或计费方式
3. 提供节能用电咨询服务
4. 建立用电监控机制，及时发现异常

**compliance_risks（合规风险提示）：**
- 可能引发用户对电费计算的质疑或投诉
- 需确保计费准确性，避免电费争议
- 应及时与客户沟通，说明用电量增加原因

**estimated_timeline（预计处理时间）：**
- 数据核实：1-2个工作日
- 现场检查：3-5个工作日
- 问题处理：7-10个工作日`,
}

export default function ProblemReportPage() {
  const [reportData, setReportData] = useState(mockProblemReport)

  // In real implementation, this would fetch data based on problem ID from URL
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search)
    const problemId = urlParams.get("problemId") || "PROB_20240115_001"

    // Mock data loading - in real app, this would be an API call
    setReportData({
      ...mockProblemReport,
      problemId,
    })
  }, [])

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "待处理":
        return <Badge className="bg-yellow-100 text-yellow-800 border-yellow-300">待处理</Badge>
      case "处理中":
        return <Badge className="bg-blue-100 text-blue-800 border-blue-300">处理中</Badge>
      case "已完成":
        return <Badge className="bg-green-100 text-green-800 border-green-300">已完成</Badge>
      case "已关闭":
        return <Badge className="bg-gray-100 text-gray-800 border-gray-300">已关闭</Badge>
      default:
        return <Badge variant="secondary">{status}</Badge>
    }
  }

  const handleBackToList = () => {
    // Navigate back to problem hit list
    window.history.back()
  }

  return (
    <div className="flex-1 space-y-6 p-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button variant="outline" size="sm" onClick={handleBackToList}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          返回问题命中列表
        </Button>
        <div className="space-y-1">
          <h1 className="text-3xl font-bold tracking-tight text-gray-900">问题命中报告 - {reportData.problemId}</h1>
          <p className="text-gray-600">详细分析问题命中原因和整改建议</p>
        </div>
      </div>

      {/* Top Summary Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            问题概要信息
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
            <div>
              <label className="text-sm font-medium text-gray-700">问题ID</label>
              <p className="text-sm text-gray-900 mt-1 font-mono">{reportData.problemId}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-700">命中规则</label>
              <p className="text-sm text-gray-900 mt-1">{reportData.hitRule}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-700">命中客户</label>
              <p className="text-sm text-gray-900 mt-1">{reportData.hitCustomer}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-700">命中日期</label>
              <p className="text-sm text-gray-900 mt-1">{reportData.hitDate}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-700">稽查状态</label>
              <div className="mt-1 flex items-center gap-2">
                {getStatusBadge(reportData.auditStatus)}
                <Button size="sm" variant="outline" disabled>
                  <RefreshCw className="h-3 w-3 mr-1" />
                  更新状态 (未来功能)
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Section 1: Problem Hit Reason Explanation */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5" />
            问题命中原因解释
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <Textarea value={reportData.explanation} readOnly className="min-h-48 bg-gray-50 text-sm leading-relaxed" />
          <Button size="sm" variant="outline" disabled>
            <RefreshCw className="h-3 w-3 mr-1" />
            生成新解释 (未来功能)
          </Button>
        </CardContent>
      </Card>

      {/* Section 2: Audit Logic Analysis */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            稽查逻辑分析
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* False Positive Detection */}
          <div className="border-2 border-dashed border-gray-300 bg-gray-50/50 p-4 rounded-lg">
            <div className="flex items-center justify-between mb-2">
              <h4 className="font-medium text-gray-700">误报检测与原因分析</h4>
              <Button size="sm" variant="outline" disabled>
                标记为误报 (未来功能)
              </Button>
            </div>
            <p className="text-sm text-gray-600">LLM将分析此问题是否为误报，并提供规则或数据改进建议 (未来功能)。</p>
            <div className="mt-3 space-y-1">
              <div className="h-2 bg-gray-200 rounded w-full"></div>
              <div className="h-2 bg-gray-200 rounded w-3/4"></div>
              <div className="h-2 bg-gray-200 rounded w-1/2"></div>
            </div>
          </div>

          {/* False Negative Identification */}
          <div className="border-2 border-dashed border-gray-300 bg-gray-50/50 p-4 rounded-lg">
            <h4 className="font-medium text-gray-700 mb-2">漏报识别与归因</h4>
            <p className="text-sm text-gray-600">LLM将分析现有规则的盲区或不足，并追溯漏报的原因 (未来功能)。</p>
            <div className="mt-3 grid grid-cols-3 gap-2">
              <div className="h-16 bg-gray-200 rounded flex items-center justify-center text-xs text-gray-500">
                规则覆盖度分析
              </div>
              <div className="h-16 bg-gray-200 rounded flex items-center justify-center text-xs text-gray-500">
                数据质量评估
              </div>
              <div className="h-16 bg-gray-200 rounded flex items-center justify-center text-xs text-gray-500">
                逻辑缺陷识别
              </div>
            </div>
          </div>

          {/* Multi-rule Collaborative Analysis */}
          <div className="border-2 border-dashed border-gray-300 bg-gray-50/50 p-4 rounded-lg">
            <h4 className="font-medium text-gray-700 mb-2">多规则协同分析</h4>
            <p className="text-sm text-gray-600">
              当多个规则同时命中或相互关联时，LLM分析其内在联系和优先级，提供更全面的问题画像 (未来功能)。
            </p>
            <div className="mt-3 flex items-center justify-center h-20 bg-gray-200 rounded">
              <span className="text-xs text-gray-500">规则关联图谱占位符</span>
            </div>
          </div>

          {/* Rule Execution Path Analysis */}
          <div className="border-2 border-dashed border-gray-300 bg-gray-50/50 p-4 rounded-lg">
            <h4 className="font-medium text-gray-700 mb-2">规则执行路径与性能分析</h4>
            <p className="text-sm text-gray-600">
              追溯规则在稽查系统中的具体执行流程，识别性能瓶颈并提出优化建议 (图示占位符) (未来功能)。
            </p>
            <div className="mt-3 flex items-center justify-center h-24 bg-gray-200 rounded">
              <span className="text-xs text-gray-500">执行路径流程图占位符</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Section 3: Problem Hit Similarity Analysis */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            问题命中相似度分析
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Similar Problem Clustering */}
          <div className="border-2 border-dashed border-gray-300 bg-gray-50/50 p-4 rounded-lg">
            <h4 className="font-medium text-gray-700 mb-2">同类问题聚类</h4>
            <p className="text-sm text-gray-600 mb-3">
              对大量命中问题进行语义或特征聚类，识别出具有相似性质、原因或影响的问题清单 (图表占位符) (未来功能)。
            </p>
            <div className="grid grid-cols-2 gap-4">
              <div className="h-32 bg-gray-200 rounded flex items-center justify-center">
                <span className="text-xs text-gray-500">聚类散点图占位符</span>
              </div>
              <div className="h-32 bg-gray-200 rounded flex items-center justify-center">
                <span className="text-xs text-gray-500">相似度热力图占位符</span>
              </div>
            </div>
          </div>

          {/* Historical Problem Association */}
          <div className="border-2 border-dashed border-gray-300 bg-gray-50/50 p-4 rounded-lg">
            <h4 className="font-medium text-gray-700 mb-2">历史问题关联</h4>
            <p className="text-sm text-gray-600 mb-3">
              将当前命中问题与历史已处理问题进行比对，识别是否是重复出现的问题或模式化风险 (列表占位符) (未来功能)。
            </p>
            <div className="space-y-2">
              {[1, 2, 3, 4].map((i) => (
                <div key={i} className="flex items-center gap-3 p-2 bg-gray-200 rounded">
                  <div className="w-2 h-2 bg-gray-400 rounded-full"></div>
                  <div className="flex-1 h-3 bg-gray-300 rounded"></div>
                  <div className="w-16 h-3 bg-gray-300 rounded"></div>
                </div>
              ))}
            </div>
          </div>

          {/* Risk Trend Prediction */}
          <div className="border-2 border-dashed border-gray-300 bg-gray-50/50 p-4 rounded-lg">
            <h4 className="font-medium text-gray-700 mb-2">风险趋势预测</h4>
            <p className="text-sm text-gray-600 mb-3">
              基于相似度分析结果，预测未来可能出现的风险类型和趋势 (折线图占位符) (未来功能)。
            </p>
            <div className="h-40 bg-gray-200 rounded flex items-center justify-center">
              <span className="text-xs text-gray-500">风险趋势预测图占位符</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Section 4: Intelligent Rectification Suggestions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Lightbulb className="h-5 w-5" />
            智能整改建议
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <Textarea
            value={reportData.suggestions}
            readOnly
            className="min-h-64 bg-gray-50 text-sm leading-relaxed font-mono"
          />
          <div className="flex gap-2">
            <Button size="sm" variant="outline" disabled>
              查看相关案例 (未来功能)
            </Button>
            <Button size="sm" variant="outline" disabled>
              <RefreshCw className="h-3 w-3 mr-1" />
              生成新建议 (未来功能)
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Bottom Navigation */}
      <div className="flex justify-center pt-6 border-t">
        <Button onClick={handleBackToList} className="flex items-center gap-2">
          <ArrowLeft className="h-4 w-4" />
          返回问题命中列表
        </Button>
      </div>
    </div>
  )
}
