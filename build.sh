#!/bin/bash

# 设置错误时退出
set -e

# 定义颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 打印带颜色的信息
info() {
    echo -e "${GREEN}[INFO] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[WARN] $1${NC}"
}

error() {
    echo -e "${RED}[ERROR] $1${NC}"
    exit 1
}

# 检查必要的命令是否存在
check_commands() {
    info "检查必要的命令..."
    commands=("node" "pnpm" "git")
    for cmd in "${commands[@]}"; do
        if ! command -v $cmd &> /dev/null; then
            error "$cmd 未安装"
        fi
    done
}

# 检查Node.js版本
check_node_version() {
    info "检查Node.js版本..."
    required_version="18.0.0"
    current_version=$(node -v | cut -d'v' -f2)
    
    if [ "$(printf '%s\n' "$required_version" "$current_version" | sort -V | head -n1)" != "$required_version" ]; then
        error "Node.js版本必须 >= $required_version，当前版本: $current_version"
    fi
}

# 安装依赖
install_dependencies() {
    info "安装项目依赖..."
    pnpm install --frozen-lockfile || error "依赖安装失败"
}

# 构建项目
build_project() {
    info "开始构建项目..."
    pnpm run build || error "项目构建失败"
}

# 创建构建产物目录
create_dist_dir() {
    info "创建构建产物目录..."
    mkdir -p dist
}

# 复制构建产物
copy_build_artifacts() {
    info "复制构建产物..."
    cp -r .next/* dist/ || error "复制构建产物失败"
}

# 创建版本信息文件
create_version_info() {
    info "创建版本信息文件..."
    {
        echo "构建时间: $(date '+%Y-%m-%d %H:%M:%S')"
        echo "Git提交: $(git rev-parse HEAD)"
        echo "Git分支: $(git rev-parse --abbrev-ref HEAD)"
        echo "Node版本: $(node -v)"
        echo "PNPM版本: $(pnpm -v)"
    } > dist/version.txt
}

# 清理临时文件
cleanup() {
    info "清理临时文件..."
    rm -rf .next
}

# 主函数
main() {
    info "开始构建流程..."
    
    # 执行各个步骤
    check_commands
    check_node_version
    install_dependencies
    build_project
    create_dist_dir
    copy_build_artifacts
    create_version_info
    cleanup
    
    info "构建完成！"
}

# 执行主函数
main 