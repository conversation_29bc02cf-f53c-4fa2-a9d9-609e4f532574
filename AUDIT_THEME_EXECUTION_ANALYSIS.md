# 稽查主题运行分析功能

## 功能概述

稽查主题运行分析功能是一个综合性的监控和分析工具，用于跟踪和分析稽查主题的执行情况，提供性能优化建议，帮助提升查询效率。

## 主要功能模块

### 1. 主题脚本运行日志

**功能描述：**
- 记录和展示稽查主题的执行历史
- 显示执行状态、耗时、处理记录数等关键指标
- 支持按主题名称搜索和状态过滤

**主要特性：**
- 实时状态监控（成功/失败/运行中）
- 性能评分可视化
- 详细的SQL查询记录
- 错误信息追踪

**使用场景：**
- 监控稽查主题执行情况
- 排查执行失败原因
- 分析执行效率趋势

### 2. 运行性能趋势图

**功能描述：**
- 展示最近10次执行的耗时变化趋势
- 可视化性能评分变化
- 提供性能统计指标

**主要特性：**
- 交互式折线图展示
- 执行时间趋势分析
- 性能评分趋势对比
- 平均执行时间统计

**图表元素：**
- X轴：执行时间点
- Y轴：执行耗时（秒）
- 数据点：显示性能评分
- 趋势线：展示性能变化趋势

### 3. 血缘图谱依赖关系分析

**功能描述：**
- 基于血缘图谱分析数据依赖关系
- 识别性能瓶颈节点
- 提供依赖链路的可视化展示

**主要特性：**
- 多类型节点支持（表、视图、函数、存储过程）
- 节点状态监控（正常/警告/错误）
- 依赖关系可视化
- 性能评分展示

**节点类型：**
- `table`: 数据表
- `view`: 视图
- `function`: 函数
- `procedure`: 存储过程

### 4. 智能查询优化建议

**功能描述：**
- 基于血缘图谱分析自动生成SQL优化建议
- 提供针对性的性能提升方案
- 预估优化效果

**优化类型：**
- **索引优化**: 为关键字段添加索引
- **查询优化**: 优化SQL语句结构
- **连接优化**: 改进表连接方式
- **过滤优化**: 添加有效的过滤条件

**建议优先级：**
- **高优先级**: 性能提升显著，建议立即实施
- **中优先级**: 有一定提升效果，可考虑实施
- **低优先级**: 提升效果有限，可选实施

## 技术实现

### 前端技术栈
- **React 18**: 用户界面框架
- **TypeScript**: 类型安全
- **Tailwind CSS**: 样式框架
- **Lucide React**: 图标库
- **SVG**: 自定义图表绘制

### 组件结构
```
audit-theme-execution-analysis/
├── page.tsx                    # 页面入口
├── main.tsx                    # 主组件
├── loading.tsx                 # 加载页面
└── components/
    ├── PerformanceChart.tsx    # 性能趋势图组件
    └── DependencyGraph.tsx     # 血缘图谱组件
```

### 数据结构

#### 运行日志数据
```typescript
interface ExecutionLog {
  id: string
  themeName: string
  executionTime: string
  duration: number
  status: 'success' | 'failed' | 'running'
  recordsProcessed: number
  errorMessage?: string
  sqlQuery: string
  performanceScore: number
}
```

#### 性能趋势数据
```typescript
interface PerformanceTrend {
  executionId: string
  executionTime: string
  duration: number
  performanceScore: number
}
```

#### 血缘图谱节点
```typescript
interface DependencyNode {
  id: string
  name: string
  type: 'table' | 'view' | 'function' | 'procedure'
  status: 'normal' | 'warning' | 'error'
  performance: number
  dependencies: string[]
}
```

#### 优化建议数据
```typescript
interface OptimizationSuggestion {
  id: string
  type: 'index' | 'query' | 'join' | 'filter'
  priority: 'high' | 'medium' | 'low'
  description: string
  impact: string
  sqlSuggestion: string
  estimatedImprovement: number
}
```

## 使用指南

### 访问功能
1. 在侧边栏菜单中找到"稽查主题运行分析"
2. 点击进入功能页面

### 查看运行日志
1. 切换到"运行日志"页签
2. 使用搜索框按主题名称过滤
3. 使用状态过滤器查看特定状态的记录
4. 点击"查看详情"获取更多信息

### 分析性能趋势
1. 切换到"性能趋势"页签
2. 查看最近10次执行的趋势图
3. 分析性能评分变化
4. 查看详细性能统计

### 分析血缘关系
1. 切换到"血缘图谱"页签
2. 查看数据依赖关系
3. 识别性能问题节点
4. 分析依赖链路

### 获取优化建议
1. 切换到"优化建议"页签
2. 查看自动生成的优化建议
3. 按优先级筛选建议
4. 查看预估优化效果

## 性能指标说明

### 性能评分计算
- 基于执行时间、资源消耗、查询复杂度等因素
- 评分范围：0-100分
- 90分以上：优秀
- 70-89分：良好
- 50-69分：一般
- 50分以下：需要优化

### 执行时间分类
- 快速：< 5秒
- 中等：5-15秒
- 较慢：15-30秒
- 很慢：> 30秒

## 最佳实践

### 监控建议
1. 定期查看运行日志，及时发现异常
2. 关注性能趋势变化，预防性能下降
3. 定期分析血缘关系，优化数据流

### 优化建议
1. 优先实施高优先级优化建议
2. 结合业务场景评估优化效果
3. 在测试环境验证优化方案

### 维护建议
1. 定期清理历史日志数据
2. 更新血缘关系信息
3. 调整性能评分算法参数

## 扩展功能

### 计划中的功能
- 实时监控仪表板
- 自动化优化执行
- 性能预警机制
- 历史数据对比分析
- 自定义性能指标

### 集成能力
- 与现有稽查主题系统集成
- 支持多种数据库类型
- 提供API接口供其他系统调用
- 支持数据导出和报告生成

## 故障排除

### 常见问题
1. **图表不显示**: 检查数据格式是否正确
2. **性能评分异常**: 验证数据源连接状态
3. **血缘关系缺失**: 确认元数据同步状态

### 技术支持
如遇到技术问题，请联系开发团队或查看系统日志获取详细错误信息。 