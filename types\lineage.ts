// 数据血缘溯源分析类型定义

// 字段引用
export interface FieldReference {
  schema: string
  table: string
  column: string
  alias?: string
}

// 表引用
export interface TableReference {
  schema: string
  name: string
  alias?: string
}

// 血缘节点
export interface LineageNode {
  id: string
  field: FieldReference
  table: TableReference
  operation: 'SELECT' | 'INSERT' | 'UPDATE' | 'DELETE' | 'TRANSFORM' | 'CREATE' | 'DROP'
  sqlContext: string
  timestamp: Date
  confidence: number
  metadata?: {
    description?: string
    dataType?: string
    businessMeaning?: string
    tags?: string[]
  }
}

// 血缘边
export interface LineageEdge {
  id: string
  sourceNodeId: string
  targetNodeId: string
  relationshipType: 'DIRECT_COPY' | 'TRANSFORMATION' | 'AGGREGATION' | 'JOIN' | 'FILTER' | 'UNION'
  transformationLogic?: string
  confidence: number
  sqlContext: string
  metadata?: {
    description?: string
    transformationFunction?: string
  }
}

// 血缘路径
export interface LineagePath {
  id: string
  path: LineageNode[]
  pathType: 'WRITE_PATH' | 'USAGE_PATH' | 'BIDIRECTIONAL'
  totalHops: number
  confidence: number
  startField: FieldReference
  endField: FieldReference
  pathDescription?: string
}

// 字段血缘
export interface FieldLineage {
  field: FieldReference
  upstreamFields: LineageNode[]
  downstreamFields: LineageNode[]
  transformations: Transformation[]
  confidence: number
  lastUpdated: Date
  metadata?: {
    businessImpact?: string
    dataQuality?: number
    usage?: string[]
  }
}

// 表血缘
export interface TableLineage {
  table: TableReference
  upstreamTables: TableReference[]
  downstreamTables: TableReference[]
  fieldLineages: FieldLineage[]
  confidence: number
  lastUpdated: Date
  metadata?: {
    businessPurpose?: string
    dataVolume?: number
    updateFrequency?: string
  }
}

// 转换操作
export interface Transformation {
  id: string
  type: 'DIRECT' | 'CALCULATION' | 'AGGREGATION' | 'CONCATENATION' | 'SPLIT' | 'LOOKUP' | 'CUSTOM'
  sourceFields: FieldReference[]
  targetField: FieldReference
  logic: string
  confidence: number
  sqlContext: string
}

// 血缘图
export interface LineageGraph {
  nodes: LineageGraphNode[]
  edges: LineageGraphEdge[]
  metadata: {
    totalNodes: number
    totalEdges: number
    maxDepth: number
    analysisTimestamp: Date
    confidenceDistribution: {
      high: number    // > 0.8
      medium: number  // 0.5 - 0.8
      low: number     // < 0.5
    }
  }
}

// 血缘图节点（用于可视化）
export interface LineageGraphNode {
  id: string
  label: string
  type: 'TABLE' | 'FIELD' | 'TRANSFORMATION'
  level: number
  position?: { x: number; y: number }
  metadata: {
    schema: string
    table: string
    column?: string
    dataType?: string
    businessMeaning?: string
    confidence: number
    operations: string[]
  }
  style?: {
    color?: string
    size?: number
    shape?: 'circle' | 'square' | 'diamond'
  }
}

// 血缘图边（用于可视化）
export interface LineageGraphEdge {
  id: string
  source: string
  target: string
  type: 'DIRECT' | 'TRANSFORMATION' | 'AGGREGATION' | 'JOIN'
  confidence: number
  label?: string
  metadata: {
    sqlContext: string
    transformationLogic?: string
    relationshipType: string
  }
  style?: {
    color?: string
    width?: number
    dashArray?: string
  }
}

// 血缘分析配置
export interface LineageAnalysisConfig {
  maxDepth: number
  minConfidence: number
  includeTransformations: boolean
  includeSystemTables: boolean
  analysisDirection: 'UPSTREAM' | 'DOWNSTREAM' | 'BOTH'
  timeRange?: {
    start: Date
    end: Date
  }
  filters?: {
    schemas?: string[]
    tables?: string[]
    operations?: string[]
  }
}

// 血缘分析结果
export interface LineageAnalysisResult {
  targetField?: FieldReference
  targetTable?: TableReference
  analysisType: 'FIELD_LINEAGE' | 'TABLE_LINEAGE' | 'IMPACT_ANALYSIS'
  lineageGraph: LineageGraph
  paths: LineagePath[]
  statistics: {
    totalPaths: number
    averagePathLength: number
    highConfidencePaths: number
    analysisTimeMs: number
  }
  recommendations?: string[]
  warnings?: string[]
}

// 血缘分析服务接口
export interface LineageAnalysisService {
  analyzeFieldLineage(field: FieldReference, config?: LineageAnalysisConfig): Promise<LineageAnalysisResult>
  analyzeTableLineage(table: TableReference, config?: LineageAnalysisConfig): Promise<LineageAnalysisResult>
  traceWritePath(field: FieldReference, config?: LineageAnalysisConfig): Promise<LineagePath[]>
  traceUsagePath(field: FieldReference, config?: LineageAnalysisConfig): Promise<LineagePath[]>
  analyzeImpact(field: FieldReference, config?: LineageAnalysisConfig): Promise<LineageAnalysisResult>
  buildLineageGraph(nodes: LineageNode[], edges: LineageEdge[]): LineageGraph
}

// 血缘搜索条件
export interface LineageSearchCriteria {
  fieldName?: string
  tableName?: string
  schemaName?: string
  operation?: string
  dateRange?: {
    start: Date
    end: Date
  }
  confidenceRange?: {
    min: number
    max: number
  }
  tags?: string[]
}

// 血缘分析统计
export interface LineageStatistics {
  totalFields: number
  totalTables: number
  totalTransformations: number
  averageConfidence: number
  pathLengthDistribution: {
    [length: number]: number
  }
  operationDistribution: {
    [operation: string]: number
  }
  confidenceDistribution: {
    high: number
    medium: number
    low: number
  }
}
