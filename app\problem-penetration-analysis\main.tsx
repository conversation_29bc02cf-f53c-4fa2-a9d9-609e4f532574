"use client"

import { use<PERSON>tate, use<PERSON>em<PERSON> } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Progress } from "@/components/ui/progress"
import { Separator } from "@/components/ui/separator"
import { 
  Search, 
  Filter, 
  Brain, 
  Target, 
  TrendingUp, 
  Lightbulb, 
  AlertTriangle, 
  CheckCircle, 
  Clock, 
  Eye,
  Link,
  Users,
  BarChart3,
  FileText,
  <PERSON>tings,
  <PERSON>rkles
} from "lucide-react"
import { Pagination, PaginationContent, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from "@/components/ui/pagination"

// 扩展问题类型定义
interface ProblemHit {
  id: string
  title: string
  description: string
  status: 'active' | 'resolved' | 'pending' | 'false_positive'
  severity: 'low' | 'medium' | 'high' | 'critical'
  createdAt: string
  updatedAt: string
  auditTask: string
  problemType: string
  confidence: number
  similarityScore?: number
  relatedProblems?: string[]
  intelligentAnalysis?: IntelligentAnalysis
  rectificationSuggestions?: RectificationSuggestion[]
  traceabilityAnalysis?: TraceabilityAnalysis
}

interface IntelligentAnalysis {
  accuracy: 'accurate' | 'false_positive' | 'questionable'
  confidence: number
  reasoning: string
  ruleLogicAssessment: string
  recommendations: string[]
}

interface RectificationSuggestion {
  id: string
  title: string
  description: string
  priority: 'low' | 'medium' | 'high'
  estimatedEffort: string
  relatedPolicies: string[]
  implementationSteps: string[]
}

interface TraceabilityAnalysis {
  triggeredRule: string
  dataPoints: string[]
  nonCompliantBehavior: string
  policyBasis: string
  detailedExplanation: string
}

// 模拟数据
const mockProblemHits: ProblemHit[] = [
  {
    id: "PH-001",
    title: "电动汽车充电桩电量异常",
    description: "检测到用户名称或用电地址包含'充电'字段且执行居民合表电价的用户，月度发行电量超出阈值",
    status: "active",
    severity: "high",
    createdAt: "2024-01-15 10:30:00",
    updatedAt: "2024-01-15 14:20:00",
    auditTask: "营销业务稽查监控",
    problemType: "电量异常",
    confidence: 95,
    similarityScore: 0.85,
    relatedProblems: ["PH-002", "PH-003"],
    intelligentAnalysis: {
      accuracy: "accurate",
      confidence: 92,
      reasoning: "通过语义分析确认该用户确实存在充电桩业务特征，且电量使用模式符合充电桩运营特征，规则逻辑正确。",
      ruleLogicAssessment: "规则逻辑符合预期：用户名称/地址包含'充电' + 居民合表电价 + 电量超阈值，检测条件明确合理。",
      recommendations: [
        "立即开展现场稽查核实",
        "检查用户实际用电性质",
        "核实是否存在商业充电桩违规使用居民电价",
        "建立充电桩用户专项监控机制"
      ]
    },
    rectificationSuggestions: [
      {
        id: "RS-001",
        title: "充电桩用户电价合规性检查",
        description: "对充电桩用户进行电价执行合规性专项检查，确保电价政策正确执行",
        priority: "high",
        estimatedEffort: "3-5天",
        relatedPolicies: ["电价执行管理办法", "充电桩用户管理规范", "营销稽查工作规程"],
        implementationSteps: [
          "现场核实用户实际用电性质",
          "检查充电桩设备安装情况",
          "核实用户营业执照和经营范围",
          "评估是否应执行商业电价",
          "建立充电桩用户档案"
        ]
      },
      {
        id: "RS-002",
        title: "充电桩用户电量监控机制",
        description: "建立充电桩用户的专项电量监控机制，定期筛查异常用电",
        priority: "medium",
        estimatedEffort: "2-3天",
        relatedPolicies: ["电量监控管理办法", "异常用电处理规程"],
        implementationSteps: [
          "设置充电桩用户电量预警阈值",
          "建立月度电量异常筛查机制",
          "配置自动告警规则",
          "制定异常处理流程"
        ]
      }
    ],
    traceabilityAnalysis: {
      triggeredRule: "电动汽车充电桩电量异常监控规则",
      dataPoints: ["用户名称", "用电地址", "电价类型", "抄表周期", "月度发行电量"],
      nonCompliantBehavior: "用户'张三充电站'（地址：XX市XX区充电广场）执行居民合表电价，单月抄见电量6500千瓦时，超出5000千瓦时阈值",
      policyBasis: "根据《电价执行管理办法》第X条规定，充电桩等商业用电设施应执行相应商业电价，不得违规使用居民电价",
      detailedExplanation: "系统在执行充电桩电量异常监控时，发现用户'张三充电站'同时满足以下条件：1）用户名称包含'充电'字段；2）执行居民合表电价；3）单月抄见电量6500千瓦时，超出设定的5000千瓦时阈值。该用户可能存在商业充电桩违规使用居民电价的情况，需要立即开展现场稽查核实。"
    }
  },
  {
    id: "PH-002",
    title: "充电桩用户双月电量超限",
    description: "检测到充电桩用户双月抄见电量超过10000千瓦时阈值，存在电价执行风险",
    status: "pending",
    severity: "medium",
    createdAt: "2024-01-15 10:32:00",
    updatedAt: "2024-01-15 11:45:00",
    auditTask: "营销业务稽查监控",
    problemType: "电量异常",
    confidence: 88,
    similarityScore: 0.78,
    relatedProblems: ["PH-001"],
    intelligentAnalysis: {
      accuracy: "questionable",
      confidence: 75,
      reasoning: "双月电量12000千瓦时确实超出阈值，但需要结合用户实际经营规模判断是否合理，可能存在误报。",
      ruleLogicAssessment: "双月阈值10000千瓦时设置合理，但建议结合用户历史用电数据进行分析。",
      recommendations: [
        "查看用户历史用电数据趋势",
        "核实用户充电桩数量和功率",
        "评估电量增长是否合理",
        "考虑调整监控阈值"
      ]
    },
    rectificationSuggestions: [
      {
        id: "RS-003",
        title: "充电桩用户用电合理性评估",
        description: "对充电桩用户的用电量进行合理性评估，确定是否存在异常",
        priority: "medium",
        estimatedEffort: "2天",
        relatedPolicies: ["用电合理性评估标准", "充电桩行业用电参考"],
        implementationSteps: [
          "收集用户历史用电数据",
          "核实充电桩设备规格和数量",
          "计算理论用电量",
          "对比实际用电量差异",
          "评估用电合理性"
        ]
      }
    ],
    traceabilityAnalysis: {
      triggeredRule: "电动汽车充电桩电量异常监控规则",
      dataPoints: ["用户名称", "用电地址", "电价类型", "抄表周期", "双月发行电量"],
      nonCompliantBehavior: "用户'李四新能源充电站'双月抄见电量12000千瓦时，超出10000千瓦时阈值",
      policyBasis: "根据《营销稽查工作规程》第X条规定，对充电桩用户进行专项监控，防止电价执行偏差",
      detailedExplanation: "系统检测到用户'李四新能源充电站'双月抄见电量12000千瓦时，超出设定的10000千瓦时阈值。该用户执行居民合表电价，但用电量较大，需要进一步核实是否存在商业充电桩违规使用居民电价的情况。"
    }
  },
  {
    id: "PH-003",
    title: "充电桩用户电价类型异常",
    description: "检测到充电桩用户电价类型与用电性质不匹配，可能存在电价执行错误",
    status: "active",
    severity: "critical",
    createdAt: "2024-01-15 09:15:00",
    updatedAt: "2024-01-15 12:00:00",
    auditTask: "营销业务稽查监控",
    problemType: "电价异常",
    confidence: 92,
    similarityScore: 0.65,
    relatedProblems: ["PH-001"],
    intelligentAnalysis: {
      accuracy: "accurate",
      confidence: 95,
      reasoning: "用户明确为充电桩运营，但执行居民电价，存在明显的电价执行错误，需要立即纠正。",
      ruleLogicAssessment: "电价类型判断逻辑清晰，充电桩应执行商业电价，规则执行正确。",
      recommendations: [
        "立即更正电价类型",
        "补收电价差额",
        "建立电价类型自动校验机制",
        "加强新用户电价类型审核"
      ]
    },
    rectificationSuggestions: [
      {
        id: "RS-004",
        title: "充电桩用户电价类型更正",
        description: "更正充电桩用户的电价类型，并补收相应的电价差额",
        priority: "high",
        estimatedEffort: "1-2天",
        relatedPolicies: ["电价执行管理办法", "电价差额补收规程"],
        implementationSteps: [
          "核实用户实际用电性质",
          "更正电价类型为商业电价",
          "计算电价差额",
          "通知用户补交费用",
          "更新用户档案信息"
        ]
      }
    ],
    traceabilityAnalysis: {
      triggeredRule: "充电桩用户电价类型监控规则",
      dataPoints: ["用户名称", "用电地址", "电价类型", "用户性质", "经营范围"],
      nonCompliantBehavior: "用户'王五充电桩服务部'经营范围明确为充电桩运营，但执行居民合表电价",
      policyBasis: "根据《电价执行管理办法》第X条规定，充电桩等商业用电设施应执行商业电价，不得使用居民电价",
      detailedExplanation: "系统检测到用户'王五充电桩服务部'的经营范围明确包含充电桩运营，但电价类型为居民合表电价，存在明显的电价执行错误。充电桩属于商业用电设施，应执行相应的商业电价。"
    }
  },
  {
    id: "PH-004",
    title: "充电桩用户地址信息异常",
    description: "检测到充电桩用户用电地址信息不完整或格式异常，影响稽查工作开展",
    status: "pending",
    severity: "low",
    createdAt: "2024-01-15 08:00:00",
    updatedAt: "2024-01-15 08:30:00",
    auditTask: "营销业务稽查监控",
    problemType: "数据质量",
    confidence: 85,
    similarityScore: 0.45,
    relatedProblems: [],
    intelligentAnalysis: {
      accuracy: "accurate",
      confidence: 88,
      reasoning: "用户地址信息确实存在格式不规范的问题，需要完善地址信息以便稽查工作开展。",
      ruleLogicAssessment: "地址格式校验规则合理，能够有效识别不规范地址信息。",
      recommendations: [
        "完善用户地址信息",
        "建立地址信息校验机制",
        "加强新用户信息录入审核"
      ]
    },
    rectificationSuggestions: [
      {
        id: "RS-005",
        title: "用户地址信息完善",
        description: "完善充电桩用户的地址信息，确保稽查工作能够正常开展",
        priority: "low",
        estimatedEffort: "1天",
        relatedPolicies: ["用户信息管理规范", "地址信息录入标准"],
        implementationSteps: [
          "联系用户核实详细地址",
          "更新用户档案地址信息",
          "验证地址信息准确性",
          "建立地址信息定期核查机制"
        ]
      }
    ],
    traceabilityAnalysis: {
      triggeredRule: "用户地址信息质量检查规则",
      dataPoints: ["用户名称", "用电地址", "地址格式", "地址完整性"],
      nonCompliantBehavior: "用户'赵六充电站'地址信息为'充电站'，缺少详细地址信息",
      policyBasis: "根据《用户信息管理规范》第X条规定，用户地址信息应包含完整的省市区街道门牌号信息",
      detailedExplanation: "系统检测到用户'赵六充电站'的地址信息仅为'充电站'，缺少详细的省市区街道门牌号信息，不符合地址信息录入标准，影响稽查工作的正常开展。"
    }
  }
]

export default function ProblemPenetrationAnalysis() {
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [problemTypeFilter, setProblemTypeFilter] = useState("all")
  const [selectedProblem, setSelectedProblem] = useState<ProblemHit | null>(null)
  // 分页相关
  const [currentPage, setCurrentPage] = useState(1)
  const pageSize = 10

  const filteredProblems = useMemo(() => {
    return mockProblemHits.filter(problem => {
      const matchesSearch = problem.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           problem.description.toLowerCase().includes(searchTerm.toLowerCase())
      const matchesStatus = statusFilter === "all" || problem.status === statusFilter
      const matchesType = problemTypeFilter === "all" || problem.problemType === problemTypeFilter
      return matchesSearch && matchesStatus && matchesType
    })
  }, [searchTerm, statusFilter, problemTypeFilter])

  // 分页数据
  const totalPages = Math.ceil(filteredProblems.length / pageSize)
  const pagedProblems = useMemo(() => {
    const start = (currentPage - 1) * pageSize
    return filteredProblems.slice(start, start + pageSize)
  }, [filteredProblems, currentPage])

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-red-100 text-red-800'
      case 'resolved': return 'bg-green-100 text-green-800'
      case 'pending': return 'bg-yellow-100 text-yellow-800'
      case 'false_positive': return 'bg-gray-100 text-gray-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'bg-red-500 text-white'
      case 'high': return 'bg-orange-500 text-white'
      case 'medium': return 'bg-yellow-500 text-white'
      case 'low': return 'bg-blue-500 text-white'
      default: return 'bg-gray-500 text-white'
    }
  }

  const getAccuracyColor = (accuracy: string) => {
    switch (accuracy) {
      case 'accurate': return 'bg-green-100 text-green-800'
      case 'false_positive': return 'bg-red-100 text-red-800'
      case 'questionable': return 'bg-yellow-100 text-yellow-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* 页面标题 */}
      {/* <div className="flex items-center justify-between mb-2">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">问题命中穿透分析</h1>
          <p className="text-muted-foreground mt-2 text-base">利用高级分析能力对审计系统识别的问题进行深度分析和智能处理</p>
        </div>
        <Badge variant="outline" className="flex items-center gap-1 text-base px-3 py-1 rounded-lg">
          <Sparkles className="w-4 h-4" /> 智能分析
        </Badge>
      </div> */}

      {/* 搜索和过滤区域 */}
      <Card className="shadow-sm rounded-xl">
        <CardHeader className="pb-2">
          <CardTitle className="flex items-center gap-2 text-lg">
            <Search className="w-5 h-5" /> 问题搜索与过滤
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">搜索问题</label>
              <Input className="rounded-lg" placeholder="搜索问题标题或描述..." value={searchTerm} onChange={(e) => setSearchTerm(e.target.value)} />
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">状态过滤</label>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="rounded-lg">
                  <SelectValue placeholder="选择状态" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部状态</SelectItem>
                  <SelectItem value="active">活跃</SelectItem>
                  <SelectItem value="pending">待处理</SelectItem>
                  <SelectItem value="resolved">已解决</SelectItem>
                  <SelectItem value="false_positive">误报</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">问题类型</label>
              <Select value={problemTypeFilter} onValueChange={setProblemTypeFilter}>
                <SelectTrigger className="rounded-lg">
                  <SelectValue placeholder="选择类型" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部类型</SelectItem>
                  <SelectItem value="电量异常">电量异常</SelectItem>
                  <SelectItem value="电价异常">电价异常</SelectItem>
                  <SelectItem value="数据质量">数据质量</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2 flex flex-col justify-end">
              <Button className="w-full rounded-lg" variant="outline">
                <Filter className="w-4 h-4 mr-2" /> 高级过滤
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 统计概览 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="rounded-xl shadow-sm hover:shadow-md transition-shadow">
          <CardContent className="p-6 flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-muted-foreground">总问题数</p>
              <p className="text-3xl font-extrabold text-blue-700">{mockProblemHits.length}</p>
            </div>
            <AlertTriangle className="w-10 h-10 text-orange-500" />
          </CardContent>
        </Card>
        <Card className="rounded-xl shadow-sm hover:shadow-md transition-shadow">
          <CardContent className="p-6 flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-muted-foreground">活跃问题</p>
              <p className="text-3xl font-extrabold text-red-600">{mockProblemHits.filter(p => p.status === 'active').length}</p>
            </div>
            <Target className="w-10 h-10 text-red-500" />
          </CardContent>
        </Card>
        <Card className="rounded-xl shadow-sm hover:shadow-md transition-shadow">
          <CardContent className="p-6 flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-muted-foreground">准确判断</p>
              <p className="text-3xl font-extrabold text-green-600">{mockProblemHits.filter(p => p.intelligentAnalysis?.accuracy === 'accurate').length}</p>
            </div>
            <CheckCircle className="w-10 h-10 text-green-500" />
          </CardContent>
        </Card>
        <Card className="rounded-xl shadow-sm hover:shadow-md transition-shadow">
          <CardContent className="p-6 flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-muted-foreground">相似问题组</p>
              <p className="text-3xl font-extrabold text-blue-600">2</p>
            </div>
            <Users className="w-10 h-10 text-blue-500" />
          </CardContent>
        </Card>
      </div>

      {/* 问题列表 */}
      <Card className="rounded-xl shadow-sm">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-lg">
            <BarChart3 className="w-5 h-5" /> 问题命中列表
          </CardTitle>
          <CardDescription>显示所有审计系统识别的问题，支持智能分析和批量处理</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table className="min-w-full border rounded-xl">
              <TableHeader>
                <TableRow className="bg-gray-50">
                  <TableHead>问题ID</TableHead>
                  <TableHead>标题</TableHead>
                  <TableHead>状态</TableHead>
                  <TableHead>严重程度</TableHead>
                  <TableHead>审计任务</TableHead>
                  <TableHead>发现时间</TableHead>
                  <TableHead>智能分析</TableHead>
                  <TableHead>操作</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {pagedProblems.map((problem) => (
                  <TableRow key={problem.id} className="hover:bg-blue-50 transition-colors">
                    <TableCell className="font-mono">{problem.id}</TableCell>
                    <TableCell>
                      <div>
                        <div className="font-medium">{problem.title}</div>
                        <div className="text-sm text-muted-foreground">{problem.description}</div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge className={getStatusColor(problem.status) + " rounded-full px-2 py-1 text-xs"}>
                        {problem.status === 'active' && '活跃'}
                        {problem.status === 'resolved' && '已解决'}
                        {problem.status === 'pending' && '待处理'}
                        {problem.status === 'false_positive' && '误报'}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <Badge className={getSeverityColor(problem.severity) + " rounded-full px-2 py-1 text-xs"}>
                        {problem.severity === 'critical' && '严重'}
                        {problem.severity === 'high' && '高'}
                        {problem.severity === 'medium' && '中'}
                        {problem.severity === 'low' && '低'}
                      </Badge>
                    </TableCell>
                    <TableCell>{problem.auditTask}</TableCell>
                    <TableCell>{problem.createdAt}</TableCell>
                    <TableCell>
                      {problem.intelligentAnalysis && (
                        <Badge className={getAccuracyColor(problem.intelligentAnalysis.accuracy) + " rounded-full px-2 py-1 text-xs"}>
                          {problem.intelligentAnalysis.accuracy === 'accurate' && '准确判断'}
                          {problem.intelligentAnalysis.accuracy === 'false_positive' && '疑似误报'}
                          {problem.intelligentAnalysis.accuracy === 'questionable' && '逻辑存疑'}
                        </Badge>
                      )}
                    </TableCell>
                    <TableCell>
                      <Dialog>
                        <DialogTrigger asChild>
                          <Button variant="outline" size="sm" className="rounded-lg px-3 py-1" onClick={() => setSelectedProblem(problem)}>
                            <Eye className="w-4 h-4 mr-1" /> 详情
                          </Button>
                        </DialogTrigger>
                        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto rounded-xl">
                          <DialogHeader>
                            <DialogTitle>问题详情分析</DialogTitle>
                            <DialogDescription>
                              问题ID: {problem.id} - {problem.title}
                            </DialogDescription>
                          </DialogHeader>
                          
                          {selectedProblem && (
                            <Tabs defaultValue="analysis" className="w-full">
                              <TabsList className="grid w-full grid-cols-5">
                                <TabsTrigger value="analysis">智能分析</TabsTrigger>
                                <TabsTrigger value="similarity">相似性分析</TabsTrigger>
                                <TabsTrigger value="rectification">整改建议</TabsTrigger>
                                <TabsTrigger value="traceability">追溯分析</TabsTrigger>
                                <TabsTrigger value="actions">操作</TabsTrigger>
                              </TabsList>

                              <TabsContent value="analysis" className="space-y-4">
                                <Card>
                                  <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                      <Brain className="w-5 h-5" />
                                      智能分析结果
                                    </CardTitle>
                                  </CardHeader>
                                  <CardContent className="space-y-4">
                                    <div className="flex items-center justify-between">
                                      <span className="font-medium">判断准确性:</span>
                                      <Badge className={getAccuracyColor(selectedProblem.intelligentAnalysis?.accuracy || '')}>
                                        {selectedProblem.intelligentAnalysis?.accuracy === 'accurate' && '准确判断'}
                                        {selectedProblem.intelligentAnalysis?.accuracy === 'false_positive' && '疑似误报'}
                                        {selectedProblem.intelligentAnalysis?.accuracy === 'questionable' && '逻辑存疑'}
                                      </Badge>
                                    </div>
                                    <div>
                                      <span className="font-medium">置信度:</span>
                                      <Progress value={selectedProblem.intelligentAnalysis?.confidence || 0} className="mt-2" />
                                    </div>
                                    <div>
                                      <span className="font-medium">分析推理:</span>
                                      <p className="text-sm text-muted-foreground mt-1">
                                        {selectedProblem.intelligentAnalysis?.reasoning}
                                      </p>
                                    </div>
                                    <div>
                                      <span className="font-medium">规则逻辑评估:</span>
                                      <p className="text-sm text-muted-foreground mt-1">
                                        {selectedProblem.intelligentAnalysis?.ruleLogicAssessment}
                                      </p>
                                    </div>
                                    <div>
                                      <span className="font-medium">建议:</span>
                                      <ul className="text-sm text-muted-foreground mt-1 space-y-1">
                                        {selectedProblem.intelligentAnalysis?.recommendations.map((rec, index) => (
                                          <li key={index}>• {rec}</li>
                                        ))}
                                      </ul>
                                    </div>
                                  </CardContent>
                                </Card>
                              </TabsContent>

                              <TabsContent value="similarity" className="space-y-4">
                                <Card>
                                  <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                      <Link className="w-5 h-5" />
                                      相似性问题分析
                                    </CardTitle>
                                  </CardHeader>
                                  <CardContent className="space-y-4">
                                    <div className="flex items-center justify-between">
                                      <span className="font-medium">相似性评分:</span>
                                      <span className="text-lg font-bold">
                                        {Math.round((selectedProblem.similarityScore || 0) * 100)}%
                                      </span>
                                    </div>
                                    {selectedProblem.relatedProblems && selectedProblem.relatedProblems.length > 0 && (
                                      <div>
                                        <span className="font-medium">相关问题:</span>
                                        <div className="mt-2 space-y-2">
                                          {selectedProblem.relatedProblems.map((relatedId) => {
                                            const relatedProblem = mockProblemHits.find(p => p.id === relatedId)
                                            return relatedProblem ? (
                                              <div key={relatedId} className="flex items-center justify-between p-2 border rounded">
                                                <div>
                                                  <div className="font-medium">{relatedProblem.title}</div>
                                                  <div className="text-sm text-muted-foreground">{relatedProblem.description}</div>
                                                </div>
                                                <Badge className={getStatusColor(relatedProblem.status)}>
                                                  {relatedProblem.status === 'active' && '活跃'}
                                                  {relatedProblem.status === 'resolved' && '已解决'}
                                                  {relatedProblem.status === 'pending' && '待处理'}
                                                  {relatedProblem.status === 'false_positive' && '误报'}
                                                </Badge>
                                              </div>
                                            ) : null
                                          })}
                                        </div>
                                      </div>
                                    )}
                                  </CardContent>
                                </Card>
                              </TabsContent>

                              <TabsContent value="rectification" className="space-y-4">
                                <Card>
                                  <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                      <Lightbulb className="w-5 h-5" />
                                      智能整改建议
                                    </CardTitle>
                                  </CardHeader>
                                  <CardContent className="space-y-4">
                                    {selectedProblem.rectificationSuggestions?.map((suggestion) => (
                                      <div key={suggestion.id} className="border rounded-lg p-4 space-y-3">
                                        <div className="flex items-center justify-between">
                                          <h4 className="font-medium">{suggestion.title}</h4>
                                          <Badge className={getSeverityColor(suggestion.priority)}>
                                            {suggestion.priority === 'high' && '高优先级'}
                                            {suggestion.priority === 'medium' && '中优先级'}
                                            {suggestion.priority === 'low' && '低优先级'}
                                          </Badge>
                                        </div>
                                        <p className="text-sm text-muted-foreground">{suggestion.description}</p>
                                        <div className="flex items-center gap-4 text-sm">
                                          <span>预估工作量: {suggestion.estimatedEffort}</span>
                                        </div>
                                        <div>
                                          <span className="font-medium text-sm">相关政策:</span>
                                          <div className="mt-1 space-y-1">
                                            {suggestion.relatedPolicies.map((policy, index) => (
                                              <Badge key={index} variant="outline" className="mr-1">
                                                {policy}
                                              </Badge>
                                            ))}
                                          </div>
                                        </div>
                                        <div>
                                          <span className="font-medium text-sm">实施步骤:</span>
                                          <ol className="mt-1 space-y-1 text-sm text-muted-foreground">
                                            {suggestion.implementationSteps.map((step, index) => (
                                              <li key={index}>{index + 1}. {step}</li>
                                            ))}
                                          </ol>
                                        </div>
                                        <div className="flex gap-2">
                                          <Button size="sm">
                                            <FileText className="w-4 h-4 mr-1" />
                                            生成整改任务
                                          </Button>
                                          <Button size="sm" variant="outline">
                                            <Link className="w-4 h-4 mr-1" />
                                            关联工作单
                                          </Button>
                                        </div>
                                      </div>
                                    ))}
                                  </CardContent>
                                </Card>
                              </TabsContent>

                              <TabsContent value="traceability" className="space-y-4">
                                <Card>
                                  <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                      <Settings className="w-5 h-5" />
                                      命中原因追溯分析
                                    </CardTitle>
                                  </CardHeader>
                                  <CardContent className="space-y-4">
                                    <div>
                                      <span className="font-medium">触发规则:</span>
                                      <p className="text-sm text-muted-foreground mt-1">
                                        {selectedProblem.traceabilityAnalysis?.triggeredRule}
                                      </p>
                                    </div>
                                    <div>
                                      <span className="font-medium">相关数据点:</span>
                                      <div className="mt-1 space-y-1">
                                        {selectedProblem.traceabilityAnalysis?.dataPoints.map((point, index) => (
                                          <Badge key={index} variant="outline" className="mr-1">
                                            {point}
                                          </Badge>
                                        ))}
                                      </div>
                                    </div>
                                    <div>
                                      <span className="font-medium">不合规行为:</span>
                                      <p className="text-sm text-muted-foreground mt-1">
                                        {selectedProblem.traceabilityAnalysis?.nonCompliantBehavior}
                                      </p>
                                    </div>
                                    <div>
                                      <span className="font-medium">政策依据:</span>
                                      <p className="text-sm text-muted-foreground mt-1">
                                        {selectedProblem.traceabilityAnalysis?.policyBasis}
                                      </p>
                                    </div>
                                    <div>
                                      <span className="font-medium">详细解释:</span>
                                      <p className="text-sm text-muted-foreground mt-1">
                                        {selectedProblem.traceabilityAnalysis?.detailedExplanation}
                                      </p>
                                    </div>
                                  </CardContent>
                                </Card>
                              </TabsContent>

                              <TabsContent value="actions" className="space-y-4">
                                <Card>
                                  <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                      <TrendingUp className="w-5 h-5" />
                                      操作管理
                                    </CardTitle>
                                  </CardHeader>
                                  <CardContent className="space-y-4">
                                    <div className="grid grid-cols-2 gap-4">
                                      <Button className="w-full">
                                        <CheckCircle className="w-4 h-4 mr-2" />
                                        标记为已解决
                                      </Button>
                                      <Button variant="outline" className="w-full">
                                        <AlertTriangle className="w-4 h-4 mr-2" />
                                        标记为误报
                                      </Button>
                                      <Button variant="outline" className="w-full">
                                        <Clock className="w-4 h-4 mr-2" />
                                        标记为待处理
                                      </Button>
                                      <Button variant="outline" className="w-full">
                                        <Users className="w-4 h-4 mr-2" />
                                        批量处理相似问题
                                      </Button>
                                    </div>
                                    <Separator />
                                    <div>
                                      <span className="font-medium">问题状态变更历史:</span>
                                      <div className="mt-2 space-y-2 text-sm">
                                        <div className="flex items-center justify-between p-2 bg-gray-50 rounded">
                                          <span>2024-01-15 14:20:00</span>
                                          <span>状态更新为: 活跃</span>
                                        </div>
                                        <div className="flex items-center justify-between p-2 bg-gray-50 rounded">
                                          <span>2024-01-15 10:30:00</span>
                                          <span>问题被发现</span>
                                        </div>
                                      </div>
                                    </div>
                                  </CardContent>
                                </Card>
                              </TabsContent>
                            </Tabs>
                          )}
                        </DialogContent>
                      </Dialog>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
          {/* 分页控件 */}
          <div className="flex justify-end mt-4">
            <Pagination>
              <PaginationContent>
                <PaginationItem>
                  <PaginationPrevious onClick={() => setCurrentPage(p => Math.max(1, p - 1))} aria-disabled={currentPage === 1} />
                </PaginationItem>
                {Array.from({ length: totalPages }, (_, i) => (
                  <PaginationItem key={i}>
                    <PaginationLink isActive={currentPage === i + 1} onClick={() => setCurrentPage(i + 1)}>
                      {i + 1}
                    </PaginationLink>
                  </PaginationItem>
                ))}
                <PaginationItem>
                  <PaginationNext onClick={() => setCurrentPage(p => Math.min(totalPages, p + 1))} aria-disabled={currentPage === totalPages} />
                </PaginationItem>
              </PaginationContent>
            </Pagination>
          </div>
        </CardContent>
      </Card>

      {/* 问题聚类分析 */}
      <Card className="rounded-xl shadow-sm">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-lg">
            <Users className="w-5 h-5" />
            问题聚类分析
          </CardTitle>
          <CardDescription>
            基于相似性计算的问题聚类，支持批量处理和趋势分析
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card className="border-blue-200">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">电量异常问题组</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">问题数量:</span>
                    <Badge>2</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">平均相似度:</span>
                    <span className="text-sm font-medium">85%</span>
                  </div>
                  <div className="text-xs text-muted-foreground">
                    主要特征：充电桩用户电量超阈值
                  </div>
                  <Button size="sm" className="w-full">
                    批量处理
                  </Button>
                </div>
              </CardContent>
            </Card>
            <Card className="border-orange-200">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">电价异常问题组</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">问题数量:</span>
                    <Badge>1</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">平均相似度:</span>
                    <span className="text-sm font-medium">75%</span>
                  </div>
                  <div className="text-xs text-muted-foreground">
                    主要特征：充电桩执行居民电价
                  </div>
                  <Button size="sm" className="w-full">
                    批量处理
                  </Button>
                </div>
              </CardContent>
            </Card>
            <Card className="border-green-200">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">数据质量问题组</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">问题数量:</span>
                    <Badge>1</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">平均相似度:</span>
                    <span className="text-sm font-medium">65%</span>
                  </div>
                  <div className="text-xs text-muted-foreground">
                    主要特征：地址信息不完整
                  </div>
                  <Button size="sm" className="w-full">
                    批量处理
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </CardContent>
      </Card>
    </div>
  )
} 