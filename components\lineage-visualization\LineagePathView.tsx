"use client"

import { useState } from "react"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { ScrollArea } from "@/components/ui/scroll-area"
import { 
  ArrowRight, 
  ArrowLeft, 
  Database, 
  GitBranch, 
  Clock, 
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  Info,
  ChevronDown,
  ChevronRight
} from "lucide-react"
import type { LineagePath, LineageNode, FieldReference } from "@/types/lineage"

interface LineagePathViewProps {
  paths: LineagePath[]
  targetField?: FieldReference
  onPathSelect?: (path: LineagePath) => void
  onNodeClick?: (node: LineageNode) => void
}

export default function LineagePathView({
  paths,
  targetField,
  onPathSelect,
  onNodeClick
}: LineagePathViewProps) {
  const [selectedPath, setSelectedPath] = useState<string | null>(null)
  const [expandedPaths, setExpandedPaths] = useState<Set<string>>(new Set())

  // 分类路径
  const writePaths = paths.filter(p => p.pathType === 'WRITE_PATH')
  const usagePaths = paths.filter(p => p.pathType === 'USAGE_PATH')
  const bidirectionalPaths = paths.filter(p => p.pathType === 'BIDIRECTIONAL')

  const togglePathExpansion = (pathId: string) => {
    const newExpanded = new Set(expandedPaths)
    if (newExpanded.has(pathId)) {
      newExpanded.delete(pathId)
    } else {
      newExpanded.add(pathId)
    }
    setExpandedPaths(newExpanded)
  }

  const handlePathClick = (path: LineagePath) => {
    setSelectedPath(selectedPath === path.id ? null : path.id)
    onPathSelect?.(path)
  }

  const getConfidenceColor = (confidence: number) => {
    if (confidence > 0.8) return "text-green-600 bg-green-50"
    if (confidence > 0.6) return "text-yellow-600 bg-yellow-50"
    return "text-red-600 bg-red-50"
  }

  const getConfidenceIcon = (confidence: number) => {
    if (confidence > 0.8) return <CheckCircle className="h-4 w-4" />
    if (confidence > 0.6) return <AlertTriangle className="h-4 w-4" />
    return <AlertTriangle className="h-4 w-4" />
  }

  const PathCard = ({ path }: { path: LineagePath }) => {
    const isExpanded = expandedPaths.has(path.id)
    const isSelected = selectedPath === path.id

    return (
      <Card 
        className={`cursor-pointer transition-all ${
          isSelected ? 'ring-2 ring-blue-500 bg-blue-50' : 'hover:bg-gray-50'
        }`}
        onClick={() => handlePathClick(path)}
      >
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation()
                  togglePathExpansion(path.id)
                }}
              >
                {isExpanded ? (
                  <ChevronDown className="h-4 w-4" />
                ) : (
                  <ChevronRight className="h-4 w-4" />
                )}
              </Button>
              <div className="flex items-center gap-2">
                {path.pathType === 'WRITE_PATH' && <ArrowLeft className="h-4 w-4 text-blue-500" />}
                {path.pathType === 'USAGE_PATH' && <ArrowRight className="h-4 w-4 text-green-500" />}
                {path.pathType === 'BIDIRECTIONAL' && <GitBranch className="h-4 w-4 text-purple-500" />}
                <span className="font-medium">
                  {path.pathType === 'WRITE_PATH' && '写入路径'}
                  {path.pathType === 'USAGE_PATH' && '使用路径'}
                  {path.pathType === 'BIDIRECTIONAL' && '双向路径'}
                </span>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Badge variant="outline" className="flex items-center gap-1">
                <TrendingUp className="h-3 w-3" />
                {path.totalHops} 步
              </Badge>
              <Badge className={`flex items-center gap-1 ${getConfidenceColor(path.confidence)}`}>
                {getConfidenceIcon(path.confidence)}
                {(path.confidence * 100).toFixed(0)}%
              </Badge>
            </div>
          </div>
          
          {path.pathDescription && (
            <p className="text-sm text-gray-600 mt-2">{path.pathDescription}</p>
          )}
        </CardHeader>

        {isExpanded && (
          <CardContent className="pt-0">
            <div className="space-y-3">
              {/* 路径概览 */}
              <div className="flex items-center gap-2 text-sm text-gray-600">
                <Database className="h-4 w-4" />
                <span>
                  {path.startField.schema}.{path.startField.table}.{path.startField.column}
                </span>
                <ArrowRight className="h-4 w-4" />
                <span>
                  {path.endField.schema}.{path.endField.table}.{path.endField.column}
                </span>
              </div>

              {/* 路径节点 */}
              <div className="space-y-2">
                <h4 className="font-medium text-sm">路径详情</h4>
                <div className="space-y-2">
                  {path.path.map((node, index) => (
                    <div key={node.id} className="flex items-center gap-3">
                      <div className="flex items-center gap-2 min-w-0 flex-1">
                        <div className="flex-shrink-0 w-6 h-6 rounded-full bg-blue-100 text-blue-600 text-xs flex items-center justify-center font-medium">
                          {index + 1}
                        </div>
                        <div 
                          className="min-w-0 flex-1 cursor-pointer hover:bg-gray-100 p-2 rounded"
                          onClick={(e) => {
                            e.stopPropagation()
                            onNodeClick?.(node)
                          }}
                        >
                          <div className="flex items-center gap-2">
                            <Database className="h-4 w-4 text-gray-500" />
                            <span className="font-medium text-sm">
                              {node.table.name}.{node.field.column}
                            </span>
                            <Badge variant="outline" size="sm">
                              {node.operation}
                            </Badge>
                          </div>
                          <div className="text-xs text-gray-500 mt-1">
                            {node.metadata?.description || '无描述'}
                          </div>
                          {node.metadata?.businessMeaning && (
                            <div className="text-xs text-blue-600 mt-1">
                              业务含义: {node.metadata.businessMeaning}
                            </div>
                          )}
                        </div>
                        <div className="flex items-center gap-1">
                          <Badge className={`text-xs ${getConfidenceColor(node.confidence)}`}>
                            {(node.confidence * 100).toFixed(0)}%
                          </Badge>
                          <div className="text-xs text-gray-500 flex items-center gap-1">
                            <Clock className="h-3 w-3" />
                            {node.timestamp.toLocaleDateString()}
                          </div>
                        </div>
                      </div>
                      {index < path.path.length - 1 && (
                        <ArrowRight className="h-4 w-4 text-gray-400 flex-shrink-0" />
                      )}
                    </div>
                  ))}
                </div>
              </div>

              {/* SQL上下文 */}
              <div className="space-y-2">
                <h4 className="font-medium text-sm">相关SQL</h4>
                <div className="space-y-1">
                  {path.path.slice(0, 3).map((node, index) => (
                    <div key={`sql-${index}`} className="bg-gray-100 p-2 rounded text-xs font-mono">
                      <div className="text-gray-600 mb-1">步骤 {index + 1}:</div>
                      <div className="text-gray-800">{node.sqlContext}</div>
                    </div>
                  ))}
                  {path.path.length > 3 && (
                    <div className="text-xs text-gray-500 text-center py-1">
                      ... 还有 {path.path.length - 3} 个步骤
                    </div>
                  )}
                </div>
              </div>
            </div>
          </CardContent>
        )}
      </Card>
    )
  }

  const PathSummary = ({ paths, title, icon }: { 
    paths: LineagePath[], 
    title: string, 
    icon: React.ReactNode 
  }) => (
    <div className="space-y-3">
      <div className="flex items-center gap-2 mb-3">
        {icon}
        <h3 className="font-medium">{title}</h3>
        <Badge variant="outline">{paths.length}</Badge>
      </div>
      
      {paths.length === 0 ? (
        <Card>
          <CardContent className="py-8 text-center text-gray-500">
            <Info className="h-8 w-8 mx-auto mb-2 text-gray-400" />
            <p>暂无{title.toLowerCase()}数据</p>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-3">
          {paths.map(path => (
            <PathCard key={path.id} path={path} />
          ))}
        </div>
      )}
    </div>
  )

  return (
    <div className="space-y-6">
      {/* 概览统计 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <GitBranch className="h-5 w-5" />
            血缘路径分析
            {targetField && (
              <Badge variant="outline">
                {targetField.schema}.{targetField.table}.{targetField.column}
              </Badge>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{writePaths.length}</div>
              <div className="text-sm text-gray-600">写入路径</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{usagePaths.length}</div>
              <div className="text-sm text-gray-600">使用路径</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">{bidirectionalPaths.length}</div>
              <div className="text-sm text-gray-600">双向路径</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-600">
                {paths.length > 0 ? (paths.reduce((sum, p) => sum + p.confidence, 0) / paths.length * 100).toFixed(0) : 0}%
              </div>
              <div className="text-sm text-gray-600">平均置信度</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 路径详情 */}
      <Tabs defaultValue="write" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="write" className="flex items-center gap-2">
            <ArrowLeft className="h-4 w-4" />
            写入路径 ({writePaths.length})
          </TabsTrigger>
          <TabsTrigger value="usage" className="flex items-center gap-2">
            <ArrowRight className="h-4 w-4" />
            使用路径 ({usagePaths.length})
          </TabsTrigger>
          <TabsTrigger value="bidirectional" className="flex items-center gap-2">
            <GitBranch className="h-4 w-4" />
            双向路径 ({bidirectionalPaths.length})
          </TabsTrigger>
        </TabsList>

        <TabsContent value="write" className="mt-4">
          <ScrollArea className="h-[600px]">
            <PathSummary 
              paths={writePaths} 
              title="写入路径" 
              icon={<ArrowLeft className="h-5 w-5 text-blue-500" />} 
            />
          </ScrollArea>
        </TabsContent>

        <TabsContent value="usage" className="mt-4">
          <ScrollArea className="h-[600px]">
            <PathSummary 
              paths={usagePaths} 
              title="使用路径" 
              icon={<ArrowRight className="h-5 w-5 text-green-500" />} 
            />
          </ScrollArea>
        </TabsContent>

        <TabsContent value="bidirectional" className="mt-4">
          <ScrollArea className="h-[600px]">
            <PathSummary 
              paths={bidirectionalPaths} 
              title="双向路径" 
              icon={<GitBranch className="h-5 w-5 text-purple-500" />} 
            />
          </ScrollArea>
        </TabsContent>
      </Tabs>
    </div>
  )
}
