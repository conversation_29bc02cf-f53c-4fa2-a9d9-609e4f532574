"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Textarea } from "@/components/ui/textarea"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { Separator } from "@/components/ui/separator"
import { 
  MessageSquare, 
  Target, 
  Users, 
  Shield, 
  AlertTriangle, 
  Database,
  ArrowRight,
  CheckCircle,
  Circle
} from "lucide-react"

// 业务语义选项类型
interface BusinessIntent {
  id: string
  category: string
  title: string
  description: string
  icon: React.ReactNode
  options: string[]
  selected: boolean
  selectedOptions: string[]
}

interface Step0IntentGuideProps {
  businessIntents: BusinessIntent[]
  setBusinessIntents: (intents: BusinessIntent[]) => void
  naturalLanguageInput: string
  setNaturalLanguageInput: (input: string) => void
  nextStep: () => void
}

export default function Step0IntentGuide({
  businessIntents,
  setBusinessIntents,
  naturalLanguageInput,
  setNaturalLanguageInput,
  nextStep
}: Step0IntentGuideProps) {
  const [chatMessages, setChatMessages] = useState<Array<{
    id: string
    type: 'user' | 'assistant'
    content: string
    timestamp: Date
  }>>([
    {
      id: '1',
      type: 'assistant',
      content: '您好！我是您的稽查规则设计助手。请告诉我您想要查什么？我可以帮您明确稽查目标、对象、风险点等要素。',
      timestamp: new Date()
    }
  ])

  const [inputMessage, setInputMessage] = useState('')

  // 更新业务意图选择
  const updateIntentSelection = (intentId: string, selected: boolean) => {
    setBusinessIntents(businessIntents.map(intent => 
      intent.id === intentId 
        ? { ...intent, selected }
        : intent
    ))
  }

  // 更新选项选择
  const updateOptionSelection = (intentId: string, option: string, selected: boolean) => {
    setBusinessIntents(businessIntents.map(intent => {
      if (intent.id === intentId) {
        const selectedOptions = selected
          ? [...intent.selectedOptions, option]
          : intent.selectedOptions.filter(opt => opt !== option)
        return { ...intent, selectedOptions }
      }
      return intent
    }))
  }

  // 发送消息
  const sendMessage = () => {
    if (!inputMessage.trim()) return

    const userMessage = {
      id: Date.now().toString(),
      type: 'user' as const,
      content: inputMessage,
      timestamp: new Date()
    }

    setChatMessages(prev => [...prev, userMessage])
    setInputMessage('')

    // 模拟助手回复
    setTimeout(() => {
      const assistantMessage = {
        id: (Date.now() + 1).toString(),
        type: 'assistant' as const,
        content: generateAssistantResponse(inputMessage),
        timestamp: new Date()
      }
      setChatMessages(prev => [...prev, assistantMessage])
    }, 1000)
  }

  // 生成助手回复
  const generateAssistantResponse = (userInput: string) => {
    const lowerInput = userInput.toLowerCase()
    
    if (lowerInput.includes('充电') || lowerInput.includes('充电桩')) {
      return '我理解您想要稽查充电桩相关的异常。建议您选择"基本信息"中的"用电户"和"风险点"中的"充电桩电量异常"。'
    } else if (lowerInput.includes('工商业') || lowerInput.includes('高耗能')) {
      return '您想要稽查工商业高耗能异常。建议选择"稽查对象"中的"工商业用户"和"风险点"中的"工商业高耗能异常"。'
    } else if (lowerInput.includes('功率因数')) {
      return '功率因数稽查很重要。建议选择"专业分类"中的"计量采集"和"风险点"中的"功率因数异常"。'
    } else {
      return '我理解您的需求。请从左侧的业务语义选项中选择相关的要素，或者继续告诉我更多细节。'
    }
  }

  // 生成自然语言描述
  const generateNaturalLanguageDescription = () => {
    const selectedIntents = businessIntents.filter(intent => intent.selected)
    if (selectedIntents.length === 0) return naturalLanguageInput

    let description = '我要设计的稽查主题信息如下：\n'
    
    selectedIntents.forEach((intent, index) => {
      if (intent.selectedOptions.length > 0) {
        // if (index > 0) description += '，'
        description += `${index + 1}.${intent.title}：${intent.selectedOptions.join('、')}\n`
      }
    })

    return description
  }

  // 确认并进入下一步
  const handleConfirm = () => {
    const description = generateNaturalLanguageDescription()
    setNaturalLanguageInput(description)
    nextStep()
  }

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 lg:grid-cols-[280px_1fr] gap-6">
        {/* 左侧：业务语义选项 */}
        <div className="h-full">
          <Card className="h-full shadow-md border border-gray-200 bg-gradient-to-b from-white to-gray-50">
            <CardHeader className="pb-2">
              <CardTitle className="flex items-center gap-2 text-lg font-bold text-gray-800">
                <Target className="h-5 w-5 text-blue-500" />
                业务语义选项
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-2 px-2 pb-4 overflow-y-auto max-h-[70vh]">
              {businessIntents.map((intent, idx) => (
                <div key={intent.id} className="py-2 border-b last:border-b-0 border-dashed border-gray-200">
                  <div className="flex items-center gap-2 mb-1">
                    <Checkbox
                      checked={intent.selected}
                      onCheckedChange={(checked) => 
                        updateIntentSelection(intent.id, checked as boolean)
                      }
                      className="scale-110"
                    />
                    <div className="flex items-center gap-2">
                      <span className="text-blue-500">{intent.icon}</span>
                      <span className="font-semibold text-gray-900 text-base">{intent.title}</span>
                    </div>
                  </div>
                  {intent.selected && (
                    <div className="ml-7 space-y-1">
                      <p className="text-xs text-gray-500 mb-1">{intent.description}</p>
                      <div className="flex flex-wrap gap-2">
                        {intent.options.map((option) => (
                          <Badge
                            key={option}
                            variant={intent.selectedOptions.includes(option) ? "default" : "outline"}
                            className={`cursor-pointer transition-all duration-150 px-3 py-1 rounded-full text-sm border-2 ${intent.selectedOptions.includes(option) ? 'bg-blue-500 text-white border-blue-500 shadow' : 'hover:bg-blue-50 hover:border-blue-300 border-gray-200 text-gray-700'}`}
                            onClick={() => 
                              updateOptionSelection(
                                intent.id, 
                                option, 
                                !intent.selectedOptions.includes(option)
                              )
                            }
                          >
                            {option}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </CardContent>
          </Card>
        </div>

        {/* 右侧：对话界面 */}
        <div className="space-y-4">
          <Card className="shadow-sm">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MessageSquare className="h-5 w-5 text-blue-500" />
                对话引导
              </CardTitle>
            </CardHeader>
            <CardContent>
              {/* 对话消息区域 */}
              <div className="h-64 overflow-y-auto space-y-4 mb-4 p-4 border rounded-lg bg-gray-50">
                {chatMessages.map((message) => (
                  <div
                    key={message.id}
                    className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
                  >
                    <div
                      className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg shadow-sm transition-all duration-150 ${
                        message.type === 'user'
                          ? 'bg-blue-500 text-white'
                          : 'bg-white text-gray-800 border border-gray-200'
                      }`}
                    >
                      <p className="text-sm">{message.content}</p>
                      <p className="text-xs opacity-70 mt-1">
                        {message.timestamp.toLocaleTimeString()}
                      </p>
                    </div>
                  </div>
                ))}
              </div>

              {/* 输入区域 */}
              <div className="flex gap-2">
                <Input
                  value={inputMessage}
                  onChange={(e) => setInputMessage(e.target.value)}
                  placeholder="请输入您的稽查需求..."
                  onKeyPress={(e) => e.key === 'Enter' && sendMessage()}
                />
                <Button onClick={sendMessage} size="sm">
                  发送
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* 生成的描述预览 */}
          <Card className="shadow-sm">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CheckCircle className="h-5 w-5 text-green-500" />
                稽查主题设计意图总结
              </CardTitle>
            </CardHeader>
            <CardContent>
              <Textarea
                value={generateNaturalLanguageDescription()}
                onChange={(e) => setNaturalLanguageInput(e.target.value)}
                placeholder="基于您的选择，系统生成的稽查目标描述..."
                className="min-h-[120px] bg-gray-50"
              />
            </CardContent>
          </Card>
        </div>
      </div>

      {/* 操作按钮 */}
      <div className="flex justify-end gap-4">
        <Button variant="outline" onClick={() => setChatMessages([chatMessages[0]])}>
          重置对话
        </Button>
        <Button onClick={handleConfirm} className="flex items-center gap-2">
          确认并进入结构化编排
          <ArrowRight className="h-4 w-4" />
        </Button>
      </div>
    </div>
  )
} 