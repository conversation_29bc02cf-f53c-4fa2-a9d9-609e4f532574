"use client"

import { useState } from "react"
import { MainLayout } from "@/components/layout/main-layout"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Progress } from "@/components/ui/progress"
import { 
  Zap, 
  Database, 
  GitBranch, 
  TrendingUp, 
  Clock,
  CheckCircle,
  FileText,
  Network
} from "lucide-react"
import { mockSQLStatements, mockParseResults } from "@/lib/mock-sql-data"
import SQLAnalysisResults from "@/components/sql-analysis/SQLAnalysisResults"
import type { SQLParseResult } from "@/types/sql-analysis"

export default function SQLDemoPage() {
  const [sqlContent, setSqlContent] = useState("")
  const [isAnalyzing, setIsAnalyzing] = useState(false)
  const [progress, setProgress] = useState(0)
  const [result, setResult] = useState<SQLParseResult | null>(null)
  const [step, setStep] = useState(0) // 0: 输入, 1: 分析中, 2: 结果

  // 加载示例SQL
  const loadExample = (index: number) => {
    setSqlContent(mockSQLStatements[index].sql)
  }

  // 模拟分析过程
  const analyzeSQL = async () => {
    if (!sqlContent.trim()) return

    setIsAnalyzing(true)
    setStep(1)
    setProgress(0)

    // 模拟进度
    const interval = setInterval(() => {
      setProgress(prev => {
        if (prev >= 100) {
          clearInterval(interval)
          return 100
        }
        return prev + 10
      })
    }, 200)

    // 模拟延迟
    await new Promise(resolve => setTimeout(resolve, 2000))

    // 使用Mock结果
    const mockResult = { ...mockParseResults[0] }
    mockResult.sql_content = sqlContent
    mockResult.created_at = new Date()
    
    setResult(mockResult)
    setStep(2)
    setIsAnalyzing(false)
    clearInterval(interval)
  }

  const reset = () => {
    setStep(0)
    setResult(null)
    setProgress(0)
    setSqlContent("")
  }

  return (
    <MainLayout>
      <div className="flex-1 space-y-6 p-6">
        {/* 页面标题 */}
        <div className="space-y-2">
          <h1 className="text-3xl font-bold tracking-tight text-gray-900 flex items-center gap-2">
            <Zap className="h-7 w-7 text-blue-500" />
            SQL解析演示
          </h1>
          <p className="text-gray-600">
            体验智能SQL解析功能，提取表间关联关系
          </p>
        </div>

        {/* 步骤指示器 */}
        <div className="flex items-center space-x-4 mb-6">
          {[
            { step: 0, label: "SQL输入", icon: FileText },
            { step: 1, label: "智能解析", icon: Zap },
            { step: 2, label: "结果展示", icon: CheckCircle }
          ].map(({ step: stepNum, label, icon: Icon }) => (
            <div key={stepNum} className="flex items-center space-x-2">
              <div className={`
                w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium
                ${step >= stepNum 
                  ? 'bg-blue-500 text-white' 
                  : 'bg-gray-200 text-gray-500'
                }
              `}>
                <Icon className="w-4 h-4" />
              </div>
              <span className={`text-sm ${step >= stepNum ? 'text-blue-600 font-medium' : 'text-gray-500'}`}>
                {label}
              </span>
              {stepNum < 2 && (
                <div className={`w-8 h-0.5 ${step > stepNum ? 'bg-blue-500' : 'bg-gray-200'}`} />
              )}
            </div>
          ))}
        </div>

        {/* SQL输入阶段 */}
        {step === 0 && (
          <Card>
            <CardHeader>
              <CardTitle>SQL脚本输入</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* 示例选择 */}
              <div className="space-y-2">
                <label className="text-sm font-medium">选择示例SQL</label>
                <div className="flex flex-wrap gap-2">
                  {mockSQLStatements.slice(0, 3).map((stmt, index) => (
                    <Button
                      key={stmt.id}
                      variant="outline"
                      size="sm"
                      onClick={() => loadExample(index)}
                      className="flex items-center gap-2"
                    >
                      <Badge variant="secondary" className="text-xs">
                        {stmt.category}
                      </Badge>
                      {stmt.name}
                    </Button>
                  ))}
                </div>
              </div>

              {/* SQL输入框 */}
              <div className="space-y-2">
                <label className="text-sm font-medium">SQL内容</label>
                <Textarea
                  rows={12}
                  value={sqlContent}
                  onChange={(e) => setSqlContent(e.target.value)}
                  placeholder="请输入或粘贴SQL脚本..."
                  className="font-mono text-sm"
                />
              </div>

              <div className="flex gap-2">
                <Button 
                  onClick={analyzeSQL}
                  disabled={!sqlContent.trim() || isAnalyzing}
                  className="flex items-center gap-2"
                >
                  <Zap className="w-4 h-4" />
                  开始解析
                </Button>
                <Button variant="outline" onClick={() => setSqlContent("")}>
                  清空
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* 分析进度阶段 */}
        {step === 1 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Zap className="w-5 h-5 animate-pulse" />
                正在解析SQL脚本...
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <Progress value={progress} className="w-full" />
              <div className="text-sm text-gray-600">
                {progress < 30 && "正在解析SQL语法结构..."}
                {progress >= 30 && progress < 60 && "正在提取表和字段信息..."}
                {progress >= 60 && progress < 90 && "正在分析关联关系..."}
                {progress >= 90 && "正在生成分析报告..."}
              </div>
            </CardContent>
          </Card>
        )}

        {/* 结果展示阶段 */}
        {step === 2 && result && (
          <div className="space-y-6">
            <Alert className="border-green-200 bg-green-50">
              <CheckCircle className="w-4 h-4 text-green-600" />
              <AlertDescription className="text-green-800">
                SQL解析成功完成！发现 {result.extracted_tables.length} 个数据表，
                {result.extracted_relations.length} 个关联关系
              </AlertDescription>
            </Alert>

            {/* 快速统计 */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <Card>
                <CardContent className="p-4 text-center">
                  <Database className="w-8 h-8 text-blue-500 mx-auto mb-2" />
                  <div className="text-2xl font-bold text-blue-600">
                    {result.extracted_tables.length}
                  </div>
                  <div className="text-sm text-gray-600">数据表</div>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="p-4 text-center">
                  <GitBranch className="w-8 h-8 text-green-500 mx-auto mb-2" />
                  <div className="text-2xl font-bold text-green-600">
                    {result.extracted_relations.length}
                  </div>
                  <div className="text-sm text-gray-600">关联关系</div>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="p-4 text-center">
                  <TrendingUp className="w-8 h-8 text-purple-500 mx-auto mb-2" />
                  <div className="text-2xl font-bold text-purple-600">
                    {Math.round(result.confidence_score * 100)}%
                  </div>
                  <div className="text-sm text-gray-600">置信度</div>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="p-4 text-center">
                  <Clock className="w-8 h-8 text-orange-500 mx-auto mb-2" />
                  <div className="text-2xl font-bold text-orange-600">
                    {result.parsing_time_ms}ms
                  </div>
                  <div className="text-sm text-gray-600">解析耗时</div>
                </CardContent>
              </Card>
            </div>

            {/* 详细结果 */}
            <SQLAnalysisResults
              parseResult={result}
              onViewGraph={() => {
                alert('关系图谱功能开发中...')
              }}
              onExport={() => {
                const dataStr = JSON.stringify(result, null, 2)
                const dataBlob = new Blob([dataStr], { type: 'application/json' })
                const url = URL.createObjectURL(dataBlob)
                const link = document.createElement('a')
                link.href = url
                link.download = 'sql-analysis-result.json'
                link.click()
              }}
            />

            <div className="flex gap-2">
              <Button variant="outline" onClick={reset}>
                重新分析
              </Button>
              <Button onClick={() => {
                const dataStr = JSON.stringify(result, null, 2)
                const dataBlob = new Blob([dataStr], { type: 'application/json' })
                const url = URL.createObjectURL(dataBlob)
                const link = document.createElement('a')
                link.href = url
                link.download = 'sql-analysis-result.json'
                link.click()
              }}>
                导出结果
              </Button>
            </div>
          </div>
        )}
      </div>
    </MainLayout>
  )
}
