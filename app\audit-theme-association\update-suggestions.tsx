"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Checkbox } from "@/components/ui/checkbox"
import { Separator } from "@/components/ui/separator"
import { 
  Plus, 
  RefreshCw, 
  AlertTriangle, 
  Unlink, 
  CheckCircle,
  FileText,
  Target,
  Settings,
  AlertTriangle as RiskIcon
} from "lucide-react"

// 更新建议接口
interface UpdateSuggestion {
  id: string
  type: 'new_element' | 'updated_element' | 'outdated_element' | 'removed_element'
  element: {
    id: string
    name: string
    type: 'risk_point' | 'policy_basis' | 'control_measure' | 'compliance_requirement'
    description: string
    source: string
    status: 'active' | 'inactive' | 'pending'
    lastUpdated: string
  }
  reason: string
  confidence: number
  action: 'add' | 'update' | 'remove' | 'review'
  policySource?: string
  changeDetails?: string
}

interface UpdateSuggestionsProps {
  suggestions: UpdateSuggestion[]
  onApply: (selectedSuggestions: UpdateSuggestion[]) => void
  onCancel: () => void
}

export function UpdateSuggestions({ suggestions, onApply, onCancel }: UpdateSuggestionsProps) {
  const [selectedSuggestions, setSelectedSuggestions] = useState<string[]>([])

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'risk_point': return <RiskIcon className="w-4 h-4" />
      case 'policy_basis': return <FileText className="w-4 h-4" />
      case 'control_measure': return <Target className="w-4 h-4" />
      case 'compliance_requirement': return <Settings className="w-4 h-4" />
      default: return <FileText className="w-4 h-4" />
    }
  }

  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'risk_point': return '风险点'
      case 'policy_basis': return '政策依据'
      case 'control_measure': return '控制措施'
      case 'compliance_requirement': return '合规要求'
      default: return type
    }
  }

  const getActionIcon = (type: string) => {
    switch (type) {
      case 'new_element': return <Plus className="w-4 h-4 text-green-600" />
      case 'updated_element': return <RefreshCw className="w-4 h-4 text-blue-600" />
      case 'outdated_element': return <AlertTriangle className="w-4 h-4 text-yellow-600" />
      case 'removed_element': return <Unlink className="w-4 h-4 text-red-600" />
      default: return <FileText className="w-4 h-4" />
    }
  }

  const getActionLabel = (type: string) => {
    switch (type) {
      case 'new_element': return '新增要素'
      case 'updated_element': return '更新要素'
      case 'outdated_element': return '过时要素'
      case 'removed_element': return '移除要素'
      default: return type
    }
  }

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return 'bg-green-100 text-green-800'
    if (confidence >= 0.6) return 'bg-yellow-100 text-yellow-800'
    return 'bg-red-100 text-red-800'
  }

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedSuggestions(suggestions.map(s => s.id))
    } else {
      setSelectedSuggestions([])
    }
  }

  const handleSelectSuggestion = (suggestionId: string, checked: boolean) => {
    if (checked) {
      setSelectedSuggestions([...selectedSuggestions, suggestionId])
    } else {
      setSelectedSuggestions(selectedSuggestions.filter(id => id !== suggestionId))
    }
  }

  const handleApply = () => {
    const selected = suggestions.filter(s => selectedSuggestions.includes(s.id))
    onApply(selected)
  }

  const groupedSuggestions = suggestions.reduce((groups, suggestion) => {
    const group = suggestion.type
    if (!groups[group]) {
      groups[group] = []
    }
    groups[group].push(suggestion)
    return groups
  }, {} as Record<string, UpdateSuggestion[]>)

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold">更新建议概览</h3>
          <p className="text-sm text-gray-600">
            发现 {suggestions.length} 个建议更新，请选择要应用的更新
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Checkbox
            id="select-all"
            checked={selectedSuggestions.length === suggestions.length}
            onCheckedChange={handleSelectAll}
          />
          <label htmlFor="select-all" className="text-sm font-medium">
            全选
          </label>
        </div>
      </div>

      <div className="space-y-4">
        {Object.entries(groupedSuggestions).map(([type, typeSuggestions]) => (
          <Card key={type}>
            <CardHeader className="pb-3">
              <div className="flex items-center gap-2">
                {getActionIcon(type)}
                <CardTitle className="text-base">{getActionLabel(type)}</CardTitle>
                <Badge variant="outline">{typeSuggestions.length} 项</Badge>
              </div>
            </CardHeader>
            <CardContent className="space-y-3">
              {typeSuggestions.map((suggestion) => (
                <div key={suggestion.id} className="border rounded-lg p-4">
                  <div className="flex items-start gap-3">
                    <Checkbox
                      id={suggestion.id}
                      checked={selectedSuggestions.includes(suggestion.id)}
                      onCheckedChange={(checked) => handleSelectSuggestion(suggestion.id, checked as boolean)}
                    />
                    <div className="flex-1 space-y-3">
                      <div className="flex items-start justify-between">
                        <div className="flex items-center gap-2">
                          {getTypeIcon(suggestion.element.type)}
                          <h4 className="font-medium">{suggestion.element.name}</h4>
                          <Badge variant="outline" className="text-xs">
                            {getTypeLabel(suggestion.element.type)}
                          </Badge>
                        </div>
                        <Badge className={getConfidenceColor(suggestion.confidence)}>
                          置信度: {Math.round(suggestion.confidence * 100)}%
                        </Badge>
                      </div>
                      
                      <p className="text-sm text-gray-600">{suggestion.element.description}</p>
                      
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <span className="font-medium">来源:</span> {suggestion.element.source}
                        </div>
                        <div>
                          <span className="font-medium">最后更新:</span> {suggestion.element.lastUpdated}
                        </div>
                      </div>

                      {suggestion.policySource && (
                        <div className="text-sm">
                          <span className="font-medium">政策来源:</span> {suggestion.policySource}
                        </div>
                      )}

                      <div className="bg-blue-50 p-3 rounded-md">
                        <p className="text-sm text-blue-800">
                          <span className="font-medium">更新原因:</span> {suggestion.reason}
                        </p>
                      </div>

                      {suggestion.changeDetails && (
                        <div className="bg-gray-50 p-3 rounded-md">
                          <p className="text-sm text-gray-700">
                            <span className="font-medium">变更详情:</span> {suggestion.changeDetails}
                          </p>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>
        ))}
      </div>

      <Separator />

      <div className="flex items-center justify-between">
        <div className="text-sm text-gray-600">
          已选择 {selectedSuggestions.length} / {suggestions.length} 项更新
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={onCancel}>
            取消
          </Button>
          <Button 
            onClick={handleApply}
            disabled={selectedSuggestions.length === 0}
          >
            <CheckCircle className="w-4 h-4 mr-2" />
            应用选中更新 ({selectedSuggestions.length})
          </Button>
        </div>
      </div>
    </div>
  )
} 