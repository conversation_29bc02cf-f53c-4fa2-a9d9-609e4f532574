import IssueHits from "@/app/issue-hits/issue-hits"
import type { Issue } from "@/types/issue"

const mockIssues: Issue[] = [
  {
    id: 1,
    title: "数据异常报警",
    description: "发现用户表存在数据缺失，请及时处理。",
    status: "active",
    severity: "high",
    createdAt: "2024-01-15 10:30:00",
    updatedAt: "2024-01-15 14:20:00",
    clues: [
      {
        id: "clue-1-1",
        title: "用户ID缺失检测",
        description: "检测到用户表中存在ID字段为空的记录",
        confidence: 95,
        createdAt: "2024-01-15 10:30:00",
        evidence: ["用户表ID字段存在NULL值", "影响记录数: 156条"],
        ruleHit: {
          id: "rule-hit-1",
          ruleName: "用户数据完整性检查",
          ruleId: "RULE_001",
          themeName: "数据质量监控",
          hitTime: "2024-01-15 10:30:00",
          dataSource: "user_management_db",
          tableName: "users",
          columnName: "user_id",
          condition: "user_id IS NULL OR user_id = ''",
          result: "发现156条异常记录",
          logicSteps: [
            {
              id: "step-1-1",
              stepName: "空值检查",
              description: "检查user_id字段是否为空",
              logicType: "validation",
              parameters: { field: "user_id", checkType: "null_or_empty" },
              result: true,
              isHit: true,
              order: 1
            },
            {
              id: "step-1-2",
              stepName: "记录统计",
              description: "统计异常记录数量",
              logicType: "aggregation",
              parameters: { operation: "count", condition: "user_id IS NULL" },
              result: 156,
              isHit: true,
              order: 2
            }
          ]
        }
      },
      {
        id: "clue-1-2",
        title: "邮箱格式异常",
        description: "检测到用户邮箱格式不符合规范",
        confidence: 88,
        createdAt: "2024-01-15 10:32:00",
        evidence: ["邮箱格式验证失败", "影响记录数: 23条"],
        ruleHit: {
          id: "rule-hit-2",
          ruleName: "邮箱格式验证",
          ruleId: "RULE_002",
          themeName: "数据格式检查",
          hitTime: "2024-01-15 10:32:00",
          dataSource: "user_management_db",
          tableName: "users",
          columnName: "email",
          condition: "email NOT REGEXP '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$'",
          result: "发现23条格式异常记录",
          logicSteps: [
            {
              id: "step-2-1",
              stepName: "正则表达式验证",
              description: "使用正则表达式验证邮箱格式",
              logicType: "validation",
              parameters: { 
                field: "email", 
                regex: "^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$" 
              },
              result: false,
              isHit: true,
              order: 1
            }
          ]
        }
      }
    ]
  },
  {
    id: 2,
    title: "规则命中告警",
    description: "营销活动返现规则被触发，需复核。",
    status: "pending",
    severity: "medium",
    createdAt: "2024-01-15 09:15:00",
    updatedAt: "2024-01-15 11:45:00",
    clues: [
      {
        id: "clue-2-1",
        title: "异常返现金额",
        description: "检测到单笔返现金额超过阈值",
        confidence: 92,
        createdAt: "2024-01-15 09:15:00",
        evidence: ["返现金额: ¥5000", "阈值: ¥1000", "超出400%"],
        ruleHit: {
          id: "rule-hit-3",
          ruleName: "返现金额监控",
          ruleId: "RULE_003",
          themeName: "营销风控",
          hitTime: "2024-01-15 09:15:00",
          dataSource: "marketing_db",
          tableName: "cashback_records",
          columnName: "amount",
          condition: "amount > 1000",
          result: "发现异常返现记录",
          logicSteps: [
            {
              id: "step-3-1",
              stepName: "金额阈值检查",
              description: "检查返现金额是否超过阈值",
              logicType: "condition",
              parameters: { field: "amount", threshold: 1000, operator: ">" },
              result: true,
              isHit: true,
              order: 1
            },
            {
              id: "step-3-2",
              stepName: "异常程度计算",
              description: "计算超出阈值的百分比",
              logicType: "calculation",
              parameters: { operation: "percentage", base: 1000, current: 5000 },
              result: 400,
              isHit: true,
              order: 2
            }
          ]
        }
      }
    ]
  },
  {
    id: 3,
    title: "数据同步失败",
    description: "MaxCompute 任务同步失败，已自动重试。",
    status: "resolved",
    severity: "low",
    createdAt: "2024-01-15 08:00:00",
    updatedAt: "2024-01-15 08:30:00",
    clues: [
      {
        id: "clue-3-1",
        title: "网络连接超时",
        description: "MaxCompute连接超时导致同步失败",
        confidence: 85,
        createdAt: "2024-01-15 08:00:00",
        evidence: ["连接超时: 30秒", "重试次数: 3次", "最终状态: 成功"],
        ruleHit: {
          id: "rule-hit-4",
          ruleName: "数据同步监控",
          ruleId: "RULE_004",
          themeName: "系统监控",
          hitTime: "2024-01-15 08:00:00",
          dataSource: "system_monitor",
          tableName: "sync_tasks",
          columnName: "status",
          condition: "status = 'failed' AND retry_count >= 3",
          result: "同步任务失败",
          logicSteps: [
            {
              id: "step-4-1",
              stepName: "状态检查",
              description: "检查同步任务状态",
              logicType: "validation",
              parameters: { field: "status", expected: "failed" },
              result: true,
              isHit: true,
              order: 1
            },
            {
              id: "step-4-2",
              stepName: "重试次数检查",
              description: "检查重试次数是否达到上限",
              logicType: "condition",
              parameters: { field: "retry_count", threshold: 3, operator: ">=" },
              result: true,
              isHit: true,
              order: 2
            }
          ]
        }
      }
    ]
  }
]

export default function Main() {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">问题命中管理</h1>
        <div className="text-sm text-gray-500">
          共 {mockIssues.length} 个问题
        </div>
      </div>
      
      {mockIssues.map(issue => (
        <IssueHits key={issue.id} issue={issue} />
      ))}
    </div>
  )
} 