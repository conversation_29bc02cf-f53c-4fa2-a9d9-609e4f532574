"use client"

import { useState } from "react"
import { <PERSON>, CardHeader, CardTitle, CardContent } from "@/components/ui/card"
import { Textarea } from "@/components/ui/textarea"
import { Button } from "@/components/ui/button"
import { Shield, Alert<PERSON>riangle, Wand2, BookO<PERSON> } from "lucide-react"

export default function KnowledgeAnalysisPage() {
  // 可根据需要支持规则选择/输入
  const [ruleDesc] = useState("识别月度用电量超过10000千瓦时的工商业客户")

  return (
    <div className="flex-1 p-6 space-y-6">
      <div className="space-y-2">
        <h1 className="text-3xl font-bold tracking-tight text-gray-900">稽查知识分析</h1>
        <p className="text-gray-600">对稽查规则进行结构、阈值、风险等多维度智能分析，辅助规则优化与决策</p>
      </div>
      <Card>
        <CardHeader>
          <CardTitle>分析对象</CardTitle>
        </CardHeader>
        <CardContent>
          <Textarea
            value={ruleDesc}
            readOnly
            className="min-h-20 bg-gray-50"
          />
        </CardContent>
      </Card>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* 结构分析 */}
        <Card className="col-span-1">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm flex items-center gap-2">
              <Shield className="h-4 w-4 text-blue-400" /> 规则结构完整性分析
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="text-sm text-gray-700 space-y-1">
              <li>✔️ 条件逻辑完整，未检测到明显缺失</li>
              <li>✔️ 结果列定义规范</li>
              <li>✔️ 动态参数配置合理</li>
            </ul>
          </CardContent>
        </Card>
        {/* 阈值与地域合理性分析 */}
        <Card className="col-span-1">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm flex items-center gap-2">
              <AlertTriangle className="h-4 w-4 text-yellow-400" /> 阈值与地域合理性分析
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="text-sm text-gray-700 space-y-1">
              <li>⚠️ 阈值10000千瓦时高于本地区平均水平（建议参考行业标准）</li>
              <li>✔️ 地域适用范围明确</li>
            </ul>
          </CardContent>
        </Card>
        {/* 风险拦截准确性分析 */}
        <Card className="col-span-1">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm flex items-center gap-2">
              <Shield className="h-4 w-4 text-green-400" /> 风险拦截准确性分析
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="text-sm text-gray-700 space-y-1">
              <li>✔️ 规则可有效识别高耗能客户</li>
              <li>✔️ 误报率低，命中率高（基于历史数据模拟）</li>
            </ul>
          </CardContent>
        </Card>
        {/* 智能建议与回流 */}
        <Card className="col-span-1">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm flex items-center gap-2">
              <Wand2 className="h-4 w-4 text-purple-400" /> 智能优化建议
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="text-sm text-gray-700 space-y-1">
              <li>• 建议将阈值动态调整为行业均值±20%</li>
              <li>• 可增加客户类型细分，提升规则适用性</li>
              <li>• 可关联最新政策条款，增强合规性</li>
            </ul>
            <Button className="mt-4 w-full" variant="outline">
              一键回流到规则生成
            </Button>
          </CardContent>
        </Card>
      </div>
      {/* 分析结果可视化区域 */}
      <div>
        <div className="font-medium text-gray-700 mb-2">分析结果可视化</div>
        <div className="flex flex-wrap gap-4">
          <Card className="flex-1 min-w-[220px]">
            <CardHeader className="pb-2">
              <CardTitle className="text-xs text-gray-500">命中率趋势</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-24 flex items-center justify-center text-gray-400 text-xs">[趋势图占位]</div>
            </CardContent>
          </Card>
          <Card className="flex-1 min-w-[220px]">
            <CardHeader className="pb-2">
              <CardTitle className="text-xs text-gray-500">地域分布</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-24 flex items-center justify-center text-gray-400 text-xs">[地域分布图占位]</div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
} 