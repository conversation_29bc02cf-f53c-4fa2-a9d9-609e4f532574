"use client"

import { useState, use<PERSON>emo } from "react"
import { Card, CardHeader, CardTitle, CardContent, CardDescription } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bs<PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { Progress } from "@/components/ui/progress"
import { 
  AlertTriangle, 
  CheckCircle, 
  Eye, 
  Edit, 
  Plus,
  FileText,
  TrendingUp,
  AlertCircle,
  ArrowRight,
  ExternalLink,
  MessageSquare,
  BookOpen,
  Target,
  Zap,
  Download,
  Share2,
  Filter,
  Search,
  BarChart3,
  PieChart,
  Activity,
  Users,
  Building,
  Calendar,
  DollarSign,
  Shield,
  Lightbulb,
  ArrowUpRight,
  ArrowDownRight
} from "lucide-react"

// 类型定义
interface AuditScenario {
  id: string
  name: string
  description: string
  category: string
  riskLevel: 'High' | 'Medium' | 'Low'
  priority: 'High' | 'Medium' | 'Low'
  lastUpdated: string
  status: 'Active' | 'Draft' | 'Archived'
  tags: string[]
}

interface RiskAnalysis {
  id: string
  riskType: string
  description: string
  severity: 'Critical' | 'High' | 'Medium' | 'Low'
  probability: number
  impact: number
  riskScore: number
  affectedEntities: string[]
  mitigationMeasures: string[]
  status: 'Open' | 'In Progress' | 'Resolved'
}

interface AnomalyPattern {
  id: string
  patternType: string
  description: string
  frequency: number
  confidence: number
  affectedParties: string[]
  timeRange: string
  riskLevel: 'High' | 'Medium' | 'Low'
  suggestedActions: string[]
}

interface ComplianceIssue {
  id: string
  issueType: string
  description: string
  regulation: string
  severity: 'Critical' | 'High' | 'Medium' | 'Low'
  affectedEntities: string[]
  violationDetails: string
  recommendedActions: string[]
  deadline: string
}

interface EntityRelationship {
  source: string
  target: string
  relationshipType: string
  strength: number
  riskLevel: 'High' | 'Medium' | 'Low'
  description: string
}

// 模拟数据
const mockScenarios: AuditScenario[] = [
  {
    id: "AS001",
    name: "业扩受电工程'三指定'分析",
    description: "分析业扩受电工程中是否存在指定设计、指定施工、指定设备采购的违规行为",
    category: "业扩工程",
    riskLevel: "High",
    priority: "High",
    lastUpdated: "2024-01-20",
    status: "Active",
    tags: ["业扩工程", "三指定", "违规分析"]
  },
  {
    id: "AS002",
    name: "高耗能客户用电稽查",
    description: "针对高耗能客户的用电行为进行专项稽查分析",
    category: "客户稽查",
    riskLevel: "Medium",
    priority: "Medium",
    lastUpdated: "2024-01-18",
    status: "Active",
    tags: ["高耗能", "客户稽查", "节能降耗"]
  },
  {
    id: "AS003",
    name: "分布式能源接入稽查",
    description: "分析分布式能源接入过程中的合规性和风险点",
    category: "新能源",
    riskLevel: "Medium",
    priority: "High",
    lastUpdated: "2024-01-15",
    status: "Active",
    tags: ["分布式能源", "新能源", "接入稽查"]
  },
  {
    id: "AS004",
    name: "虚拟电厂参与度分析",
    description: "分析客户参与虚拟电厂项目的合规性和贡献度",
    category: "虚拟电厂",
    riskLevel: "Low",
    priority: "Medium",
    lastUpdated: "2024-01-12",
    status: "Draft",
    tags: ["虚拟电厂", "参与度", "合规分析"]
  }
]

const mockRiskAnalysis: RiskAnalysis[] = [
  {
    id: "RA001",
    riskType: "指定设计风险",
    description: "发现多个业扩工程存在指定设计单位的情况",
    severity: "High",
    probability: 0.85,
    impact: 0.9,
    riskScore: 0.77,
    affectedEntities: ["设计单位A", "设计单位B", "客户C"],
    mitigationMeasures: ["加强设计单位选择流程", "建立设计单位评估机制"],
    status: "Open"
  },
  {
    id: "RA002",
    riskType: "指定施工风险",
    description: "部分工程存在指定施工单位的违规行为",
    severity: "Medium",
    probability: 0.65,
    impact: 0.7,
    riskScore: 0.46,
    affectedEntities: ["施工单位X", "施工单位Y"],
    mitigationMeasures: ["规范施工招标流程", "加强施工质量监管"],
    status: "In Progress"
  }
]

const mockAnomalyPatterns: AnomalyPattern[] = [
  {
    id: "AP001",
    patternType: "设计单位集中度异常",
    description: "发现某设计单位在特定区域的设计项目占比过高",
    frequency: 15,
    confidence: 0.92,
    affectedParties: ["设计单位A", "区域B"],
    timeRange: "2023-01 至 2024-01",
    riskLevel: "High",
    suggestedActions: ["调查设计单位选择流程", "评估是否存在利益输送"]
  },
  {
    id: "AP002",
    patternType: "设备采购价格异常",
    description: "同类设备采购价格存在显著差异",
    frequency: 8,
    confidence: 0.78,
    affectedParties: ["供应商X", "供应商Y"],
    timeRange: "2023-06 至 2024-01",
    riskLevel: "Medium",
    suggestedActions: ["分析价格差异原因", "评估采购流程合规性"]
  }
]

const mockComplianceIssues: ComplianceIssue[] = [
  {
    id: "CI001",
    issueType: "违反招投标规定",
    description: "业扩工程未按规定进行公开招标",
    regulation: "《招标投标法》",
    severity: "Critical",
    affectedEntities: ["项目A", "项目B"],
    violationDetails: "直接指定设计单位，未履行招标程序",
    recommendedActions: ["立即停止违规行为", "重新履行招标程序"],
    deadline: "2024-02-15"
  },
  {
    id: "CI002",
    issueType: "利益冲突",
    description: "发现相关人员与设计单位存在利益关联",
    regulation: "《电力监管条例》",
    severity: "High",
    affectedEntities: ["人员X", "设计单位A"],
    violationDetails: "相关人员持有设计单位股份",
    recommendedActions: ["调查利益关联", "调整相关人员"],
    deadline: "2024-02-20"
  }
]

const mockEntityRelationships: EntityRelationship[] = [
  {
    source: "设计单位A",
    target: "客户C",
    relationshipType: "设计服务",
    strength: 0.95,
    riskLevel: "High",
    description: "频繁的设计服务关系，可能存在指定行为"
  },
  {
    source: "施工单位X",
    target: "设计单位A",
    relationshipType: "合作关系",
    strength: 0.78,
    riskLevel: "Medium",
    description: "存在稳定的合作关系，需要关注是否存在利益输送"
  }
]

export function AuditScenarioAnalysis() {
  const [selectedScenario, setSelectedScenario] = useState<string>("AS001")
  const [activeTab, setActiveTab] = useState("overview")
  const [isExportDialogOpen, setIsExportDialogOpen] = useState(false)
  const [exportFormat, setExportFormat] = useState("pdf")
  const [isCreateTaskDialogOpen, setIsCreateTaskDialogOpen] = useState(false)
  const [taskDescription, setTaskDescription] = useState("")

  const currentScenario = useMemo(() => 
    mockScenarios.find(s => s.id === selectedScenario), 
    [selectedScenario]
  )

  const getRiskLevelBadgeVariant = (level: string) => {
    switch (level) {
      case 'Critical':
      case 'High': return 'destructive'
      case 'Medium': return 'secondary'
      case 'Low': return 'default'
      default: return 'default'
    }
  }

  const getPriorityBadgeVariant = (priority: string) => {
    switch (priority) {
      case 'High': return 'destructive'
      case 'Medium': return 'secondary'
      case 'Low': return 'default'
      default: return 'default'
    }
  }

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'Active': return 'default'
      case 'Draft': return 'secondary'
      case 'Archived': return 'outline'
      default: return 'default'
    }
  }

  const handleExport = () => {
    setIsExportDialogOpen(true)
  }

  const handleCreateTask = () => {
    setIsCreateTaskDialogOpen(true)
  }

  const calculateRiskScore = (probability: number, impact: number) => {
    return probability * impact
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          {/* <h1 className="text-3xl font-bold text-gray-900">重点稽查场景要素分析</h1>
          <p className="text-gray-600 mt-2">针对特定、高价值稽查场景的专项洞察与分析</p> */}
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={handleExport}>
            <Download className="w-4 h-4 mr-2" />
            导出报告
          </Button>
          <Button onClick={handleCreateTask}>
            <Plus className="w-4 h-4 mr-2" />
            创建稽查任务
          </Button>
        </div>
      </div>

      {/* 场景选择器 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Target className="w-5 h-5" />
            稽查场景选择
          </CardTitle>
          <CardDescription>
            选择要分析的重点稽查场景，系统将自动整合相关要素和数据
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4">
            <div className="flex-1">
              <Label htmlFor="scenario-select">选择场景</Label>
              <Select value={selectedScenario} onValueChange={setSelectedScenario}>
                <SelectTrigger>
                  <SelectValue placeholder="选择稽查场景" />
                </SelectTrigger>
                <SelectContent>
                  {mockScenarios.map((scenario) => (
                    <SelectItem key={scenario.id} value={scenario.id}>
                      <div className="flex items-center gap-2">
                        <span>{scenario.name}</span>
                        <Badge variant={getRiskLevelBadgeVariant(scenario.riskLevel)}>
                          {scenario.riskLevel}
                        </Badge>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            {currentScenario && (
              <div className="flex items-center gap-2">
                <Badge variant={getPriorityBadgeVariant(currentScenario.priority)}>
                  优先级: {currentScenario.priority}
                </Badge>
                <Badge variant={getStatusBadgeVariant(currentScenario.status)}>
                  {currentScenario.status}
                </Badge>
              </div>
            )}
          </div>
          {currentScenario && (
            <div className="mt-4 p-4 bg-gray-50 rounded-lg">
              <h4 className="font-medium mb-2">{currentScenario.name}</h4>
              <p className="text-gray-600 text-sm mb-3">{currentScenario.description}</p>
              <div className="flex flex-wrap gap-2">
                {currentScenario.tags.map((tag) => (
                  <Badge key={tag} variant="outline" className="text-xs">
                    {tag}
                  </Badge>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* 分析内容 */}
      {currentScenario && (
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="overview">概览</TabsTrigger>
            <TabsTrigger value="risk-analysis">风险分析</TabsTrigger>
            <TabsTrigger value="anomaly-patterns">异常模式</TabsTrigger>
            <TabsTrigger value="compliance-issues">合规问题</TabsTrigger>
            <TabsTrigger value="entity-relationships">实体关系</TabsTrigger>
          </TabsList>

          {/* 概览标签页 */}
          <TabsContent value="overview" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">高风险项目</p>
                      <p className="text-2xl font-bold text-red-600">8</p>
                    </div>
                    <AlertTriangle className="w-8 h-8 text-red-500" />
                  </div>
                  <div className="mt-2 flex items-center text-sm">
                    <ArrowUpRight className="w-4 h-4 text-red-500 mr-1" />
                    <span className="text-red-500">+12%</span>
                    <span className="text-gray-500 ml-1">较上月</span>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">异常模式</p>
                      <p className="text-2xl font-bold text-orange-600">15</p>
                    </div>
                    <BarChart3 className="w-8 h-8 text-orange-500" />
                  </div>
                  <div className="mt-2 flex items-center text-sm">
                    <ArrowUpRight className="w-4 h-4 text-orange-500 mr-1" />
                    <span className="text-orange-500">+8%</span>
                    <span className="text-gray-500 ml-1">较上月</span>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">合规问题</p>
                      <p className="text-2xl font-bold text-blue-600">23</p>
                    </div>
                    <Shield className="w-8 h-8 text-blue-500" />
                  </div>
                  <div className="mt-2 flex items-center text-sm">
                    <ArrowDownRight className="w-4 h-4 text-green-500 mr-1" />
                    <span className="text-green-500">-5%</span>
                    <span className="text-gray-500 ml-1">较上月</span>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">关联实体</p>
                      <p className="text-2xl font-bold text-purple-600">42</p>
                    </div>
                    <Users className="w-8 h-8 text-purple-500" />
                  </div>
                  <div className="mt-2 flex items-center text-sm">
                    <ArrowUpRight className="w-4 h-4 text-purple-500 mr-1" />
                    <span className="text-purple-500">+3%</span>
                    <span className="text-gray-500 ml-1">较上月</span>
                  </div>
                </CardContent>
              </Card>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Activity className="w-5 h-5" />
                    风险趋势分析
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {mockRiskAnalysis.map((risk) => (
                      <div key={risk.id} className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-1">
                            <span className="font-medium">{risk.riskType}</span>
                            <Badge variant={getRiskLevelBadgeVariant(risk.severity)}>
                              {risk.severity}
                            </Badge>
                          </div>
                          <p className="text-sm text-gray-600">{risk.description}</p>
                        </div>
                        <div className="text-right">
                          <div className="text-lg font-bold text-red-600">
                            {(risk.riskScore * 100).toFixed(0)}%
                          </div>
                          <div className="text-xs text-gray-500">风险评分</div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Lightbulb className="w-5 h-5" />
                    关键洞察
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                      <div className="flex items-start gap-2">
                        <AlertCircle className="w-5 h-5 text-blue-600 mt-0.5" />
                        <div>
                          <h4 className="font-medium text-blue-900">设计单位集中度异常</h4>
                          <p className="text-sm text-blue-700 mt-1">
                            发现某设计单位在特定区域的设计项目占比过高，可能存在指定行为
                          </p>
                        </div>
                      </div>
                    </div>
                    <div className="p-4 bg-orange-50 border border-orange-200 rounded-lg">
                      <div className="flex items-start gap-2">
                        <TrendingUp className="w-5 h-5 text-orange-600 mt-0.5" />
                        <div>
                          <h4 className="font-medium text-orange-900">价格异常波动</h4>
                          <p className="text-sm text-orange-700 mt-1">
                            同类设备采购价格存在显著差异，需要进一步调查
                          </p>
                        </div>
                      </div>
                    </div>
                    <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                      <div className="flex items-start gap-2">
                        <CheckCircle className="w-5 h-5 text-green-600 mt-0.5" />
                        <div>
                          <h4 className="font-medium text-green-900">合规改进</h4>
                          <p className="text-sm text-green-700 mt-1">
                            通过加强监管，合规问题数量较上月下降5%
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* 风险分析标签页 */}
          <TabsContent value="risk-analysis" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <AlertTriangle className="w-5 h-5" />
                  风险分析详情
                </CardTitle>
                <CardDescription>
                  基于场景要素识别的潜在风险和威胁分析
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>风险类型</TableHead>
                      <TableHead>严重程度</TableHead>
                      <TableHead>概率</TableHead>
                      <TableHead>影响</TableHead>
                      <TableHead>风险评分</TableHead>
                      <TableHead>状态</TableHead>
                      <TableHead>操作</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {mockRiskAnalysis.map((risk) => (
                      <TableRow key={risk.id}>
                        <TableCell>
                          <div>
                            <div className="font-medium">{risk.riskType}</div>
                            <div className="text-sm text-gray-500">{risk.description}</div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant={getRiskLevelBadgeVariant(risk.severity)}>
                            {risk.severity}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Progress value={risk.probability * 100} className="w-16" />
                            <span className="text-sm">{(risk.probability * 100).toFixed(0)}%</span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Progress value={risk.impact * 100} className="w-16" />
                            <span className="text-sm">{(risk.impact * 100).toFixed(0)}%</span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="text-lg font-bold text-red-600">
                            {(risk.riskScore * 100).toFixed(0)}%
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant={risk.status === 'Open' ? 'destructive' : 'secondary'}>
                            {risk.status}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Button variant="outline" size="sm">
                            <Eye className="w-4 h-4 mr-1" />
                            查看详情
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>

          {/* 异常模式标签页 */}
          <TabsContent value="anomaly-patterns" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="w-5 h-5" />
                  异常模式识别
                </CardTitle>
                <CardDescription>
                  基于数据挖掘识别的异常行为和模式
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {mockAnomalyPatterns.map((pattern) => (
                    <Card key={pattern.id} className="border-l-4 border-l-orange-500">
                      <CardContent className="p-4">
                        <div className="flex items-start justify-between mb-3">
                          <div>
                            <h4 className="font-medium">{pattern.patternType}</h4>
                            <p className="text-sm text-gray-600 mt-1">{pattern.description}</p>
                          </div>
                          <Badge variant={getRiskLevelBadgeVariant(pattern.riskLevel)}>
                            {pattern.riskLevel}
                          </Badge>
                        </div>
                        <div className="space-y-2 text-sm">
                          <div className="flex justify-between">
                            <span className="text-gray-500">频率:</span>
                            <span className="font-medium">{pattern.frequency} 次</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-500">置信度:</span>
                            <span className="font-medium">{(pattern.confidence * 100).toFixed(0)}%</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-500">时间范围:</span>
                            <span className="font-medium">{pattern.timeRange}</span>
                          </div>
                        </div>
                        <div className="mt-3">
                          <h5 className="text-sm font-medium mb-2">建议行动:</h5>
                          <div className="space-y-1">
                            {pattern.suggestedActions.map((action, index) => (
                              <div key={index} className="text-sm text-gray-600 flex items-center gap-1">
                                <ArrowRight className="w-3 h-3" />
                                {action}
                              </div>
                            ))}
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* 合规问题标签页 */}
          <TabsContent value="compliance-issues" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Shield className="w-5 h-5" />
                  合规问题清单
                </CardTitle>
                <CardDescription>
                  识别出的合规性问题和违规行为
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>问题类型</TableHead>
                      <TableHead>严重程度</TableHead>
                      <TableHead>法规依据</TableHead>
                      <TableHead>截止日期</TableHead>
                      <TableHead>状态</TableHead>
                      <TableHead>操作</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {mockComplianceIssues.map((issue) => (
                      <TableRow key={issue.id}>
                        <TableCell>
                          <div>
                            <div className="font-medium">{issue.issueType}</div>
                            <div className="text-sm text-gray-500">{issue.description}</div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant={getRiskLevelBadgeVariant(issue.severity)}>
                            {issue.severity}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="text-sm">{issue.regulation}</div>
                        </TableCell>
                        <TableCell>
                          <div className="text-sm">{issue.deadline}</div>
                        </TableCell>
                        <TableCell>
                          <Badge variant="destructive">待处理</Badge>
                        </TableCell>
                        <TableCell>
                          <Button variant="outline" size="sm">
                            <Eye className="w-4 h-4 mr-1" />
                            查看详情
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>

          {/* 实体关系标签页 */}
          <TabsContent value="entity-relationships" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="w-5 h-5" />
                  实体关系图谱
                </CardTitle>
                <CardDescription>
                  相关实体之间的关联关系和风险分析
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {mockEntityRelationships.map((relationship, index) => (
                    <Card key={index} className="border-l-4 border-l-purple-500">
                      <CardContent className="p-4">
                        <div className="flex items-center justify-between mb-3">
                          <div className="flex items-center gap-2">
                            <span className="font-medium">{relationship.source}</span>
                            <ArrowRight className="w-4 h-4 text-gray-400" />
                            <span className="font-medium">{relationship.target}</span>
                          </div>
                          <Badge variant={getRiskLevelBadgeVariant(relationship.riskLevel)}>
                            {relationship.riskLevel}
                          </Badge>
                        </div>
                        <div className="space-y-2 text-sm">
                          <div className="flex justify-between">
                            <span className="text-gray-500">关系类型:</span>
                            <span className="font-medium">{relationship.relationshipType}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-500">关联强度:</span>
                            <div className="flex items-center gap-2">
                              <Progress value={relationship.strength * 100} className="w-16" />
                              <span className="font-medium">{(relationship.strength * 100).toFixed(0)}%</span>
                            </div>
                          </div>
                        </div>
                        <div className="mt-3 p-3 bg-gray-50 rounded-lg">
                          <p className="text-sm text-gray-600">{relationship.description}</p>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      )}

      {/* 导出报告对话框 */}
      <Dialog open={isExportDialogOpen} onOpenChange={setIsExportDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>导出分析报告</DialogTitle>
            <DialogDescription>
              选择导出格式和内容范围
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="export-format">导出格式</Label>
              <Select value={exportFormat} onValueChange={setExportFormat}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="pdf">PDF 报告</SelectItem>
                  <SelectItem value="excel">Excel 表格</SelectItem>
                  <SelectItem value="word">Word 文档</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsExportDialogOpen(false)}>
              取消
            </Button>
            <Button onClick={() => setIsExportDialogOpen(false)}>
              确认导出
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 创建稽查任务对话框 */}
      <Dialog open={isCreateTaskDialogOpen} onOpenChange={setIsCreateTaskDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>创建稽查任务</DialogTitle>
            <DialogDescription>
              基于分析结果创建稽查任务
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="task-description">任务描述</Label>
              <Textarea
                id="task-description"
                placeholder="请输入稽查任务描述..."
                value={taskDescription}
                onChange={(e) => setTaskDescription(e.target.value)}
                rows={4}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsCreateTaskDialogOpen(false)}>
              取消
            </Button>
            <Button onClick={() => setIsCreateTaskDialogOpen(false)}>
              创建任务
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
} 