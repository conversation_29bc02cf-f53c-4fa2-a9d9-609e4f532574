import React, { useState, useEffect } from "react"
import { <PERSON>, Card<PERSON>eader, Card<PERSON><PERSON>le, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger, TabsContent } from "@/components/ui/tabs"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { 
  TrendingUp, 
  History, 
  BarChart3, 
  FileText, 
  Users, 
  Target,
  ArrowUp,
  ArrowDown,
  Minus,
  Eye,
  Calendar,
  Activity
} from "lucide-react"

// 主题版本接口
interface ThemeVersion {
  id: string
  version: string
  createdAt: string
  description: string
  targetUsers: string[]
  baseConditions: any[]
  branchConditions: any[]
  hitCount: number
  executionCount: number
  status: 'draft' | 'published' | 'archived'
  changes: {
    type: 'added' | 'removed' | 'modified'
    field: string
    oldValue?: string
    newValue?: string
    description: string
  }[]
}

// 主题演进趋势分析组件
interface ThemeEvolutionAnalysisProps {
  themeId: string
  themeName: string
  versions: ThemeVersion[]
  onClose: () => void
  onShowDetail?: () => void
}

const ThemeEvolutionAnalysis: React.FC<ThemeEvolutionAnalysisProps> = ({
  themeId,
  themeName,
  versions,
  onClose,
  onShowDetail
}) => {
  const [selectedVersion, setSelectedVersion] = useState<string | null>(null)
  const [compareMode, setCompareMode] = useState(false)
  const [compareVersion1, setCompareVersion1] = useState<string>("")
  const [compareVersion2, setCompareVersion2] = useState<string>("")

  // 按时间排序版本
  const sortedVersions = [...versions].sort((a, b) => 
    new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
  )

  // 获取版本变化统计
  const getVersionStats = (version: ThemeVersion) => {
    const added = version.changes.filter(c => c.type === 'added').length
    const removed = version.changes.filter(c => c.type === 'removed').length
    const modified = version.changes.filter(c => c.type === 'modified').length
    
    return { added, removed, modified }
  }

  // 获取命中率变化
  const getHitRateChange = (currentVersion: ThemeVersion, previousVersion?: ThemeVersion) => {
    if (!previousVersion) return { change: 0, trend: 'new' }
    
    const currentRate = currentVersion.executionCount > 0 
      ? (currentVersion.hitCount / currentVersion.executionCount) * 100 
      : 0
    const previousRate = previousVersion.executionCount > 0 
      ? (previousVersion.hitCount / previousVersion.executionCount) * 100 
      : 0
    
    const change = currentRate - previousRate
    return {
      change: Math.abs(change),
      trend: change > 0 ? 'up' : change < 0 ? 'down' : 'stable'
    }
  }

  // 高亮显示变化
  const highlightChanges = (text: string, changes: any[]) => {
    let highlightedText = text
    
    changes.forEach(change => {
      if (change.type === 'added') {
        highlightedText = highlightedText.replace(
          change.newValue,
          `<span class="bg-green-100 text-green-800 px-1 rounded">${change.newValue}</span>`
        )
      } else if (change.type === 'removed') {
        highlightedText = highlightedText.replace(
          change.oldValue,
          `<span class="bg-red-100 text-red-800 px-1 rounded line-through">${change.oldValue}</span>`
        )
      } else if (change.type === 'modified') {
        highlightedText = highlightedText.replace(
          change.oldValue,
          `<span class="bg-yellow-100 text-yellow-800 px-1 rounded">${change.oldValue} → ${change.newValue}</span>`
        )
      }
    })
    
    return highlightedText
  }

  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            主题演进趋势分析 - {themeName}
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-6">
          {/* 概览统计 */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center justify-between">
                演进概览
                {onShowDetail && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={onShowDetail}
                    className="flex items-center gap-2"
                  >
                    <FileText className="h-4 w-4" />
                    查看详细对比
                  </Button>
                )}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-4 gap-4">
                <div className="text-center p-4 bg-blue-50 rounded-lg">
                  <div className="text-2xl font-bold text-blue-600">{versions.length}</div>
                  <div className="text-sm text-gray-600">总版本数</div>
                </div>
                <div className="text-center p-4 bg-green-50 rounded-lg">
                  <div className="text-2xl font-bold text-green-600">
                    {versions.reduce((sum, v) => sum + v.hitCount, 0)}
                  </div>
                  <div className="text-sm text-gray-600">累计命中数</div>
                </div>
                <div className="text-center p-4 bg-purple-50 rounded-lg">
                  <div className="text-2xl font-bold text-purple-600">
                    {versions.reduce((sum, v) => sum + v.executionCount, 0)}
                  </div>
                  <div className="text-sm text-gray-600">累计执行数</div>
                </div>
                <div className="text-center p-4 bg-orange-50 rounded-lg">
                  <div className="text-2xl font-bold text-orange-600">
                    {versions.reduce((sum, v) => sum + v.changes.length, 0)}
                  </div>
                  <div className="text-sm text-gray-600">累计变更数</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Tabs defaultValue="timeline" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="timeline">演进时间线</TabsTrigger>
              <TabsTrigger value="changes">变更明细</TabsTrigger>
              <TabsTrigger value="metrics">指标趋势</TabsTrigger>
            </TabsList>

            <TabsContent value="timeline" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <History className="h-4 w-4" />
                    版本演进时间线
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {sortedVersions.map((version, index) => {
                      const stats = getVersionStats(version)
                      const previousVersion = index < sortedVersions.length - 1 ? sortedVersions[index + 1] : undefined
                      const hitRateChange = getHitRateChange(version, previousVersion)
                      
                      return (
                        <div key={version.id} className="border rounded-lg p-4 hover:bg-gray-50">
                          <div className="flex items-center justify-between mb-3">
                            <div className="flex items-center gap-3">
                              <Badge variant={version.status === 'published' ? 'default' : 'secondary'}>
                                v{version.version}
                              </Badge>
                              <span className="text-sm text-gray-500">
                                {new Date(version.createdAt).toLocaleDateString()}
                              </span>
                              <div className="flex items-center gap-1">
                                {hitRateChange.trend === 'up' && <ArrowUp className="h-3 w-3 text-green-500" />}
                                {hitRateChange.trend === 'down' && <ArrowDown className="h-3 w-3 text-red-500" />}
                                {hitRateChange.trend === 'stable' && <Minus className="h-3 w-3 text-gray-500" />}
                                <span className="text-xs text-gray-500">
                                  {hitRateChange.trend === 'new' ? '新版本' : `${hitRateChange.change.toFixed(1)}%`}
                                </span>
                              </div>
                            </div>
                            <div className="flex items-center gap-2">
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => setSelectedVersion(version.id)}
                              >
                                <Eye className="h-3 w-3 mr-1" />
                                查看详情
                              </Button>
                            </div>
                          </div>
                          
                          <div className="text-sm text-gray-700 mb-3">
                            {version.description}
                          </div>
                          
                          <div className="flex items-center gap-4 text-xs">
                            <div className="flex items-center gap-1">
                              <Users className="h-3 w-3" />
                              <span>命中: {version.hitCount}</span>
                            </div>
                            <div className="flex items-center gap-1">
                              <Activity className="h-3 w-3" />
                              <span>执行: {version.executionCount}</span>
                            </div>
                            <div className="flex items-center gap-1">
                              <FileText className="h-3 w-3" />
                              <span>变更: {version.changes.length}</span>
                            </div>
                            {stats.added > 0 && (
                              <Badge variant="outline" className="text-green-600 border-green-200">
                                +{stats.added}
                              </Badge>
                            )}
                            {stats.removed > 0 && (
                              <Badge variant="outline" className="text-red-600 border-red-200">
                                -{stats.removed}
                              </Badge>
                            )}
                            {stats.modified > 0 && (
                              <Badge variant="outline" className="text-yellow-600 border-yellow-200">
                                ~{stats.modified}
                              </Badge>
                            )}
                          </div>
                        </div>
                      )
                    })}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="changes" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <FileText className="h-4 w-4" />
                    变更明细对比
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center gap-4 mb-4">
                    <Select value={compareVersion1} onValueChange={setCompareVersion1}>
                      <SelectTrigger className="w-48">
                        <SelectValue placeholder="选择版本1" />
                      </SelectTrigger>
                      <SelectContent>
                        {sortedVersions.map(version => (
                          <SelectItem key={version.id} value={version.id}>
                            v{version.version} - {new Date(version.createdAt).toLocaleDateString()}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <span className="text-gray-500">vs</span>
                    <Select value={compareVersion2} onValueChange={setCompareVersion2}>
                      <SelectTrigger className="w-48">
                        <SelectValue placeholder="选择版本2" />
                      </SelectTrigger>
                      <SelectContent>
                        {sortedVersions.map(version => (
                          <SelectItem key={version.id} value={version.id}>
                            v{version.version} - {new Date(version.createdAt).toLocaleDateString()}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  
                  {compareVersion1 && compareVersion2 && (
                    <div className="space-y-4">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>变更类型</TableHead>
                            <TableHead>字段</TableHead>
                            <TableHead>原值</TableHead>
                            <TableHead>新值</TableHead>
                            <TableHead>描述</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {(() => {
                            const v1 = sortedVersions.find(v => v.id === compareVersion1)
                            const v2 = sortedVersions.find(v => v.id === compareVersion2)
                            if (!v1 || !v2) return []
                            
                            // 合并两个版本的所有变更
                            const allChanges = [...v1.changes, ...v2.changes]
                            return allChanges.map((change, index) => (
                              <TableRow key={index}>
                                <TableCell>
                                  <Badge 
                                    variant={change.type === 'added' ? 'default' : 
                                           change.type === 'removed' ? 'destructive' : 'secondary'}
                                  >
                                    {change.type === 'added' ? '新增' : 
                                     change.type === 'removed' ? '删除' : '修改'}
                                  </Badge>
                                </TableCell>
                                <TableCell>{change.field}</TableCell>
                                <TableCell className="text-red-600">
                                  {change.oldValue || '-'}
                                </TableCell>
                                <TableCell className="text-green-600">
                                  {change.newValue || '-'}
                                </TableCell>
                                <TableCell>{change.description}</TableCell>
                              </TableRow>
                            ))
                          })()}
                        </TableBody>
                      </Table>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="metrics" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <BarChart3 className="h-4 w-4" />
                    指标趋势分析
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-6">
                    {/* 命中率趋势 */}
                    <div className="space-y-4">
                      <h4 className="font-medium">命中率趋势</h4>
                      <div className="space-y-2">
                        {sortedVersions.map((version, index) => {
                          const rate = version.executionCount > 0 
                            ? (version.hitCount / version.executionCount) * 100 
                            : 0
                          return (
                            <div key={version.id} className="flex items-center justify-between">
                              <span className="text-sm">v{version.version}</span>
                              <div className="flex items-center gap-2">
                                <div className="w-32 bg-gray-200 rounded-full h-2">
                                  <div 
                                    className="bg-blue-600 h-2 rounded-full" 
                                    style={{ width: `${Math.min(rate, 100)}%` }}
                                  ></div>
                                </div>
                                <span className="text-sm text-gray-600">{rate.toFixed(1)}%</span>
                              </div>
                            </div>
                          )
                        })}
                      </div>
                    </div>
                    
                    {/* 执行次数趋势 */}
                    <div className="space-y-4">
                      <h4 className="font-medium">执行次数趋势</h4>
                      <div className="space-y-2">
                        {sortedVersions.map((version) => (
                          <div key={version.id} className="flex items-center justify-between">
                            <span className="text-sm">v{version.version}</span>
                            <div className="flex items-center gap-2">
                              <div className="w-32 bg-gray-200 rounded-full h-2">
                                <div 
                                  className="bg-green-600 h-2 rounded-full" 
                                  style={{ width: `${Math.min((version.executionCount / 100) * 100, 100)}%` }}
                                ></div>
                              </div>
                              <span className="text-sm text-gray-600">{version.executionCount}</span>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </DialogContent>
    </Dialog>
  )
}

export default ThemeEvolutionAnalysis 