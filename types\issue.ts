export interface Issue {
  id: number
  title: string
  description: string
  status: 'active' | 'resolved' | 'pending'
  createdAt: string
  updatedAt: string
  severity: 'low' | 'medium' | 'high' | 'critical'
  clues: IssueClue[]
}

export interface IssueClue {
  id: string
  title: string
  description: string
  ruleHit: RuleHit
  evidence: string[]
  confidence: number
  createdAt: string
}

export interface RuleHit {
  id: string
  ruleName: string
  ruleId: string
  themeName: string
  logicSteps: LogicStep[]
  hitTime: string
  dataSource: string
  tableName: string
  columnName: string
  condition: string
  result: string
}

export interface LogicStep {
  id: string
  stepName: string
  description: string
  logicType: 'condition' | 'calculation' | 'validation' | 'aggregation'
  parameters: Record<string, any>
  result: any
  isHit: boolean
  order: number
}

export interface TraceFlowData {
  issue: Issue
  selectedClue: IssueClue
  flowSteps: FlowStep[]
}

export interface FlowStep {
  id: string
  stepType: 'rule_trigger' | 'logic_evaluation' | 'data_validation' | 'clue_generation'
  title: string
  description: string
  details: Record<string, any>
  timestamp: string
  order: number
} 