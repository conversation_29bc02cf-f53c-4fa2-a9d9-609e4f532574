"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";

interface InspectionResult {
  id: string;
  workOrderId: string;
  title: string;
  type: string;
  inspectedAt: string;
  score: number;
  status: "compliant" | "non_compliant";
  nonComplianceCount: number;
  categories: {
    name: string;
    score: number;
    items: {
      standard: string;
      result: "pass" | "fail";
      score: number;
      details: string;
      suggestion?: string;
    }[];
  }[];
}

const mockResults: InspectionResult[] = [
  {
    id: "IR-2024-001",
    workOrderId: "WO-2024-001",
    title: "系统异常访问审计",
    type: "系统安全",
    inspectedAt: "2024-03-20",
    score: 95,
    status: "compliant",
    nonComplianceCount: 1,
    categories: [
      {
        name: "响应完整性",
        score: 98,
        items: [
          {
            standard: "问题描述完整性",
            result: "pass",
            score: 100,
            details: "问题描述包含了完整的背景信息、现象描述和影响范围",
          },
          {
            standard: "解决方案完整性",
            result: "pass",
            score: 95,
            details: "解决方案包含了具体的处理步骤和验证方法",
          },
        ],
      },
      {
        name: "附件规范性",
        score: 92,
        items: [
          {
            standard: "图片清晰度",
            result: "fail",
            score: 85,
            details: "部分截图模糊，不易辨识",
            suggestion: "建议重新上传清晰的系统截图",
          },
          {
            standard: "附件相关性",
            result: "pass",
            score: 100,
            details: "附件内容与工单描述相符",
          },
        ],
      },
    ],
  },
];

export default function InspectionResults() {
  const [results, setResults] = useState<InspectionResult[]>(mockResults);
  const [selectedResult, setSelectedResult] = useState<InspectionResult | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const handleViewDetail = (result: InspectionResult) => {
    setSelectedResult(result);
    setIsDialogOpen(true);
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold">质检结果列表</h2>
        <Button variant="outline">导出报告</Button>
      </div>

      <Card>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>工单编号</TableHead>
              <TableHead>标题</TableHead>
              <TableHead>类型</TableHead>
              <TableHead>质检时间</TableHead>
              <TableHead>质检得分</TableHead>
              <TableHead>合规状态</TableHead>
              <TableHead>不合规项数</TableHead>
              <TableHead>操作</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {results.map((result) => (
              <TableRow key={result.id}>
                <TableCell>{result.workOrderId}</TableCell>
                <TableCell>{result.title}</TableCell>
                <TableCell>{result.type}</TableCell>
                <TableCell>{result.inspectedAt}</TableCell>
                <TableCell>
                  <div className="flex items-center gap-2">
                    <Progress value={result.score} className="w-20" />
                    <span>{result.score}分</span>
                  </div>
                </TableCell>
                <TableCell>
                  <Badge
                    variant={result.status === "compliant" ? "outline" : "destructive"}
                  >
                    {result.status === "compliant" ? "合规" : "不合规"}
                  </Badge>
                </TableCell>
                <TableCell>{result.nonComplianceCount}</TableCell>
                <TableCell>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleViewDetail(result)}
                  >
                    查看详情
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </Card>

      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>质检结果详情</DialogTitle>
          </DialogHeader>
          {selectedResult && (
            <div className="space-y-6">
              <Card className="p-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <span className="text-gray-500">工单编号</span>
                    <p className="font-medium">{selectedResult.workOrderId}</p>
                  </div>
                  <div>
                    <span className="text-gray-500">标题</span>
                    <p className="font-medium">{selectedResult.title}</p>
                  </div>
                  <div>
                    <span className="text-gray-500">类型</span>
                    <p className="font-medium">{selectedResult.type}</p>
                  </div>
                  <div>
                    <span className="text-gray-500">质检时间</span>
                    <p className="font-medium">{selectedResult.inspectedAt}</p>
                  </div>
                </div>
              </Card>

              <Card className="p-4">
                <h3 className="text-lg font-semibold mb-4">质检结果</h3>
                <Tabs defaultValue={selectedResult.categories[0].name}>
                  <TabsList>
                    {selectedResult.categories.map((category) => (
                      <TabsTrigger key={category.name} value={category.name}>
                        {category.name}
                      </TabsTrigger>
                    ))}
                  </TabsList>

                  {selectedResult.categories.map((category) => (
                    <TabsContent key={category.name} value={category.name}>
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <span className="text-gray-500">类别得分</span>
                          <div className="flex items-center gap-2">
                            <Progress value={category.score} className="w-32" />
                            <span className="font-semibold">{category.score}分</span>
                          </div>
                        </div>

                        <div className="space-y-4">
                          {category.items.map((item, index) => (
                            <Card key={index} className="p-4">
                              <div className="flex items-center justify-between mb-2">
                                <span className="font-medium">{item.standard}</span>
                                <div className="flex items-center gap-4">
                                  <Badge
                                    variant={item.result === "pass" ? "outline" : "destructive"}
                                  >
                                    {item.result === "pass" ? "通过" : "不通过"}
                                  </Badge>
                                  <span className="font-semibold">{item.score}分</span>
                                </div>
                              </div>
                              <p className="text-gray-600 text-sm">{item.details}</p>
                              {item.suggestion && (
                                <p className="text-yellow-600 text-sm mt-2">
                                  建议：{item.suggestion}
                                </p>
                              )}
                            </Card>
                          ))}
                        </div>
                      </div>
                    </TabsContent>
                  ))}
                </Tabs>
              </Card>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
} 