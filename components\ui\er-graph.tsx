"use client"

import { useEffect, useRef } from "react"
import * as d3 from "d3"

interface Table {
  name: string
  comment: string
  fields: { name: string; type: string; comment: string; relations: { table: string; field: string; type: string }[] }[]
}

interface ERGraphProps {
  tables?: Table[]
  graph?: {
    nodes: { id: string; type: string; label: string; db?: string; table?: string; field?: string }[]
    links: { source: string; target: string; type: string }[]
  }
}

export default function D3ERGraph({ tables, graph }: ERGraphProps) {
  const ref = useRef<SVGSVGElement>(null)

  useEffect(() => {
    if (!ref.current) return
    const width = 600
    const height = 400
    const svg = d3.select(ref.current)
    svg.selectAll("*").remove()

    let nodes: any[] = []
    let links: any[] = []

    if (tables) {
      // 表关系图谱模式
      tables.forEach((table) => {
        nodes.push({ id: table.name, type: "table", label: table.name, comment: table.comment })
        table.fields.forEach((field) => {
          nodes.push({ id: `${table.name}.${field.name}`, type: "field", label: field.name, table: table.name })
          field.relations.forEach((rel) => {
            links.push({ source: `${table.name}.${field.name}`, target: `${rel.table}.${rel.field}`, type: rel.type })
            links.push({ source: table.name, target: rel.table, type: "table-rel" })
          })
        })
      })
    } else if (graph) {
      // 元数据关系图谱模式
      nodes = graph.nodes.map(n => ({ ...n }))
      links = graph.links.map(l => ({ ...l }))
    }

    // 力导向布局
    const simulation = d3.forceSimulation(nodes)
      .force("link", d3.forceLink(links).id((d: any) => d.id).distance(80))
      .force("charge", d3.forceManyBody().strength(-200))
      .force("center", d3.forceCenter(width / 2, height / 2))
      .stop()

    for (let i = 0; i < 200; ++i) simulation.tick()

    // 连线
    svg.append("g")
      .selectAll("line")
      .data(links)
      .enter()
      .append("line")
      .attr("x1", (d: any) => d.source.x)
      .attr("y1", (d: any) => d.source.y)
      .attr("x2", (d: any) => d.target.x)
      .attr("y2", (d: any) => d.target.y)
      .attr("stroke", (d: any) => d.type === "table-rel" || d.type === "db-table" ? "#aaa" : d.type === "field-index" ? "#43a047" : "#2196f3")
      .attr("stroke-width", 2)
      .attr("opacity", 0.7)

    // 节点
    const node = svg.append("g")
      .selectAll("circle")
      .data(nodes)
      .enter()
      .append("circle")
      .attr("cx", (d: any) => d.x)
      .attr("cy", (d: any) => d.y)
      .attr("r", (d: any) => {
        if (d.type === "database") return 28
        if (d.type === "table") return 22
        if (d.type === "index") return 14
        return 12
      })
      .attr("fill", (d: any) => {
        if (d.type === "database") return "#ffb300"
        if (d.type === "table") return "#1976d2"
        if (d.type === "index") return "#43a047"
        return "#fff"
      })
      .attr("stroke", (d: any) => {
        if (d.type === "database") return "#ffa000"
        if (d.type === "table") return "#1565c0"
        if (d.type === "index") return "#388e3c"
        return "#1976d2"
      })
      .attr("stroke-width", 2)
      .style("cursor", "pointer")
      .on("click", function (event, d) {
        svg.selectAll("circle").attr("fill", (n: any) => {
          if (n.type === "database") return "#ffb300"
          if (n.type === "table") return "#1976d2"
          if (n.type === "index") return "#43a047"
          return "#fff"
        })
        svg.selectAll("circle").attr("stroke-width", (n: any) => n.id === d.id || links.some(l => (l.source.id === d.id && l.target.id === n.id) || (l.target.id === d.id && l.source.id === n.id)) ? 4 : 2)
        d3.select(this).attr("fill", "#ff9800")
      })

    // 标签
    svg.append("g")
      .selectAll("text")
      .data(nodes)
      .enter()
      .append("text")
      .attr("x", (d: any) => d.x)
      .attr("y", (d: any) => d.y + (d.type === "database" ? -34 : d.type === "table" ? -28 : d.type === "index" ? 24 : 20))
      .attr("text-anchor", "middle")
      .attr("font-size", (d: any) => d.type === "database" ? 18 : d.type === "table" ? 16 : d.type === "index" ? 13 : 12)
      .attr("fill", (d: any) => {
        if (d.type === "database") return "#ffb300"
        if (d.type === "table") return "#1976d2"
        if (d.type === "index") return "#43a047"
        return "#333"
      })
      .text((d: any) => d.label)
  }, [tables, graph])

  return <svg ref={ref} width={600} height={400} className="border rounded bg-white" />
} 