# 主题演进趋势分析功能

## 功能概述

主题演进趋势分析功能是一个专门用于查看同一主题规则多版本内容变化和命中样本数量变化的系统模块。该功能支持版本对比、变更明细查看和指标趋势分析。

## 主要功能

### 1. 主题演进趋势分析
- **演进时间线**: 按时间顺序展示主题的所有版本
- **变更统计**: 显示每个版本的变更数量（新增、删除、修改）
- **命中率趋势**: 展示命中率的变化趋势
- **执行统计**: 显示执行次数和命中数的变化

### 2. 主题演进明细
- **版本对比**: 支持选择任意两个版本进行详细对比
- **条件对比**: 高亮显示基础条件的变化
- **描述对比**: 对比规则描述的变化
- **指标对比**: 对比命中率、执行次数等指标

### 3. 变更高亮显示
- **新增内容**: 绿色高亮显示新增的字段和条件
- **删除内容**: 红色高亮显示删除的字段和条件
- **修改内容**: 黄色高亮显示修改的字段和条件

## 使用场景

### 1. 迭代已有规则
当用户从"稽查主题规则迭代"页面点击"迭代"按钮时，系统会：
1. 自动加载原始规则数据
2. 填充到规则生成器中
3. 在最后一步显示"历史演进趋势分析"按钮

### 2. 查看演进历史
用户可以：
1. 点击"历史演进趋势分析"按钮
2. 查看主题的所有版本历史
3. 分析变更趋势和效果

### 3. 详细对比分析
用户可以：
1. 选择任意两个版本进行对比
2. 查看详细的变更内容
3. 分析指标变化

## 技术实现

### 1. URL参数处理
```typescript
// 处理URL参数，判断是否为迭代已有规则
useEffect(() => {
  const urlParams = new URLSearchParams(window.location.search)
  const ruleId = urlParams.get("ruleId")
  
  if (ruleId) {
    setIsIteratingExistingRule(true)
    setOriginalRuleId(ruleId)
    loadOriginalRuleData(ruleId)
    loadThemeVersions(ruleId)
  }
}, [])
```

### 2. 版本数据结构
```typescript
interface ThemeVersion {
  id: string
  version: string
  createdAt: string
  description: string
  targetUsers: string[]
  baseConditions: any[]
  branchConditions: any[]
  hitCount: number
  executionCount: number
  status: 'draft' | 'published' | 'archived'
  changes: {
    type: 'added' | 'removed' | 'modified'
    field: string
    oldValue?: string
    newValue?: string
    description: string
  }[]
}
```

### 3. 变更高亮算法
```typescript
const highlightChanges = (text: string, changes: any[]) => {
  let highlightedText = text
  
  changes.forEach(change => {
    if (change.type === 'added') {
      highlightedText = highlightedText.replace(
        change.newValue,
        `<span class="bg-green-100 text-green-800 px-1 rounded">${change.newValue}</span>`
      )
    } else if (change.type === 'removed') {
      highlightedText = highlightedText.replace(
        change.oldValue,
        `<span class="bg-red-100 text-red-800 px-1 rounded line-through">${change.oldValue}</span>`
      )
    } else if (change.type === 'modified') {
      highlightedText = highlightedText.replace(
        change.oldValue,
        `<span class="bg-yellow-100 text-yellow-800 px-1 rounded">${change.oldValue} → ${change.newValue}</span>`
      )
    }
  })
  
  return highlightedText
}
```

## 组件结构

### 1. ThemeEvolutionAnalysis
- **功能**: 主题演进趋势分析主组件
- **特性**: 时间线展示、概览统计、指标趋势
- **位置**: `app/rule-generator/ThemeEvolutionAnalysis.tsx`

### 2. ThemeEvolutionDetail
- **功能**: 主题演进明细对比组件
- **特性**: 版本对比、条件对比、描述对比
- **位置**: `app/rule-generator/ThemeEvolutionDetail.tsx`

### 3. 主文件集成
- **功能**: 在规则生成器中集成演进分析功能
- **特性**: URL参数处理、状态管理、组件渲染
- **位置**: `app/rule-generator/main.tsx`

## 使用流程

### 1. 从迭代页面进入
1. 在"稽查主题规则迭代"页面找到要迭代的规则
2. 点击"迭代"按钮
3. 系统自动跳转到规则生成器并加载原始数据
4. 完成规则设计后，在最后一步点击"历史演进趋势分析"

### 2. 查看演进趋势
1. 在演进趋势分析页面查看版本时间线
2. 查看每个版本的变更统计和指标变化
3. 点击"查看详细对比"进入明细页面

### 3. 详细对比分析
1. 选择要对比的两个版本
2. 查看条件对比、描述对比、指标对比
3. 分析变更对规则效果的影响

## 数据示例

### 版本数据示例
```json
{
  "id": "v1.2",
  "version": "1.2",
  "createdAt": "2024-03-10T09:15:00Z",
  "description": "电动汽车充电桩电量异常，对营销业务用户...",
  "targetUsers": ["营销业务用户"],
  "baseConditions": [...],
  "branchConditions": [...],
  "hitCount": 92,
  "executionCount": 180,
  "status": "draft",
  "changes": [
    {
      "type": "modified",
      "field": "单月阈值",
      "oldValue": "4000千瓦时",
      "newValue": "5000千瓦时",
      "description": "进一步调整单月用电量阈值"
    }
  ]
}
```

## 未来扩展

### 1. 功能增强
- **自动变更检测**: 自动检测规则变更并生成变更记录
- **变更影响评估**: 评估变更对规则效果的影响
- **版本回滚**: 支持回滚到历史版本

### 2. 可视化增强
- **变更图表**: 使用图表展示变更趋势
- **影响分析图**: 可视化展示变更对指标的影响
- **时间轴**: 更直观的时间轴展示

### 3. 协作功能
- **变更评论**: 支持对变更添加评论
- **变更审批**: 支持变更审批流程
- **变更通知**: 变更通知机制

## 总结

主题演进趋势分析功能为稽查主题规则的管理提供了强大的版本控制和变更分析能力，帮助用户：
- 追踪规则的演进历史
- 分析变更对规则效果的影响
- 优化规则设计决策
- 提高规则管理的效率和质量

通过直观的界面和详细的分析功能，该模块大大简化了规则版本管理的工作，为构建更加完善和高效的稽查体系提供了有力支持。 