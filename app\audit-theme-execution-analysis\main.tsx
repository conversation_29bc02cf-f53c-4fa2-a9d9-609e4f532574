'use client'

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Progress } from '@/components/ui/progress'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Separator } from '@/components/ui/separator'
import { 
  Play, 
  Clock, 
  TrendingUp, 
  AlertTriangle, 
  CheckCircle, 
  XCircle,
  RefreshCw,
  Search,
  Filter,
  Download,
  Lightbulb,
  Database,
  Activity
} from 'lucide-react'
import Performance<PERSON>hart from './components/PerformanceChart'
import DependencyGraph from './components/DependencyGraph'

// 运行日志数据类型
interface ExecutionLog {
  id: string
  themeName: string
  executionTime: string
  duration: number // 毫秒
  status: 'success' | 'failed' | 'running'
  recordsProcessed: number
  errorMessage?: string
  sqlQuery: string
  performanceScore: number // 0-100
}

// 性能趋势数据类型
interface PerformanceTrend {
  executionId: string
  executionTime: string
  duration: number
  performanceScore: number
}

// 优化建议数据类型
interface OptimizationSuggestion {
  id: string
  type: 'index' | 'query' | 'join' | 'filter'
  priority: 'high' | 'medium' | 'low'
  description: string
  impact: string
  sqlSuggestion: string
  estimatedImprovement: number // 百分比
}

// 血缘图谱节点数据类型
interface DependencyNode {
  id: string
  name: string
  type: 'table' | 'view' | 'function' | 'procedure'
  status: 'normal' | 'warning' | 'error'
  performance: number
  dependencies: string[]
}

export default function AuditThemeExecutionAnalysis() {
  const [activeTab, setActiveTab] = useState('logs')
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')
  const [executionLogs, setExecutionLogs] = useState<ExecutionLog[]>([])
  const [performanceTrends, setPerformanceTrends] = useState<PerformanceTrend[]>([])
  const [optimizationSuggestions, setOptimizationSuggestions] = useState<OptimizationSuggestion[]>([])
  const [dependencyNodes, setDependencyNodes] = useState<DependencyNode[]>([])
  const [isLoading, setIsLoading] = useState(false)

  // 模拟数据
  useEffect(() => {
    // 模拟运行日志数据
    const mockLogs: ExecutionLog[] = [
      {
        id: '1',
        themeName: '电动汽车充电桩电量异常',
        executionTime: '2024-01-15 14:30:25',
        duration: 12500,
        status: 'success',
        recordsProcessed: 15420,
        sqlQuery: 'SELECT COUNT(*) FROM financial_data WHERE amount IS NULL',
        performanceScore: 85
      },
      {
        id: '2',
        themeName: '最大需量值异常',
        executionTime: '2024-01-15 13:45:12',
        duration: 8900,
        status: 'success',
        recordsProcessed: 8920,
        sqlQuery: 'SELECT customer_id, COUNT(*) FROM customer_info GROUP BY customer_id HAVING COUNT(*) > 1',
        performanceScore: 92
      },
      {
        id: '3',
        themeName: '电能表倍率异常',
        executionTime: '2024-01-15 12:20:45',
        duration: 15600,
        status: 'failed',
        recordsProcessed: 0,
        errorMessage: '数据库连接超时',
        sqlQuery: 'SELECT * FROM transaction_data WHERE amount > 1000000',
        performanceScore: 0
      },
      {
        id: '4',
        themeName: '抄表示数异常',
        executionTime: '2024-01-15 11:15:30',
        duration: 7200,
        status: 'success',
        recordsProcessed: 5670,
        sqlQuery: 'SELECT product_id, SUM(quantity) FROM inventory GROUP BY product_id',
        performanceScore: 78
      },
      {
        id: '5',
        themeName: '充换电设施电价执行异常',
        executionTime: '2024-01-15 10:30:15',
        duration: 9800,
        status: 'running',
        recordsProcessed: 2340,
        sqlQuery: 'SELECT user_id, role_id FROM user_permissions WHERE role_id NOT IN (SELECT id FROM valid_roles)',
        performanceScore: 65
      }
    ]

    // 模拟性能趋势数据
    const mockTrends: PerformanceTrend[] = [
      { executionId: '1', executionTime: '2024-01-15 14:30', duration: 12500, performanceScore: 85 },
      { executionId: '2', executionTime: '2024-01-15 13:45', duration: 8900, performanceScore: 92 },
      { executionId: '3', executionTime: '2024-01-15 12:20', duration: 15600, performanceScore: 0 },
      { executionId: '4', executionTime: '2024-01-15 11:15', duration: 7200, performanceScore: 78 },
      { executionId: '5', executionTime: '2024-01-15 10:30', duration: 9800, performanceScore: 65 },
      { executionId: '6', executionTime: '2024-01-14 16:45', duration: 11200, performanceScore: 88 },
      { executionId: '7', executionTime: '2024-01-14 15:20', duration: 8300, performanceScore: 91 },
      { executionId: '8', executionTime: '2024-01-14 14:10', duration: 14500, performanceScore: 76 },
      { executionId: '9', executionTime: '2024-01-14 13:05', duration: 9200, performanceScore: 89 },
      { executionId: '10', executionTime: '2024-01-14 12:00', duration: 10800, performanceScore: 82 }
    ]

    // 模拟优化建议数据
    const mockSuggestions: OptimizationSuggestion[] = [
      {
        id: '1',
        type: 'index',
        priority: 'high',
        description: '为 financial_data 表的 amount 字段添加索引',
        impact: '查询性能提升显著',
        sqlSuggestion: 'CREATE INDEX idx_financial_amount ON financial_data(amount)',
        estimatedImprovement: 75
      },
      {
        id: '2',
        type: 'query',
        priority: 'medium',
        description: '优化 GROUP BY 查询，使用窗口函数替代',
        impact: '减少重复计算',
        sqlSuggestion: 'SELECT customer_id, COUNT(*) OVER (PARTITION BY customer_id) as count FROM customer_info',
        estimatedImprovement: 45
      },
      {
        id: '3',
        type: 'join',
        priority: 'high',
        description: '使用 INNER JOIN 替代 NOT IN 子查询',
        impact: '提高查询效率',
        sqlSuggestion: 'SELECT u.user_id, u.role_id FROM user_permissions u INNER JOIN valid_roles v ON u.role_id = v.id',
        estimatedImprovement: 60
      },
      {
        id: '4',
        type: 'filter',
        priority: 'low',
        description: '添加分区过滤条件',
        impact: '减少扫描数据量',
        sqlSuggestion: 'SELECT * FROM transaction_data WHERE amount > 1000000 AND transaction_date >= CURRENT_DATE - INTERVAL 30 DAY',
        estimatedImprovement: 25
      }
    ]

    // 模拟血缘图谱数据
    const mockDependencyNodes: DependencyNode[] = [
      {
        id: '1',
        name: 'financial_data',
        type: 'table',
        status: 'normal',
        performance: 85,
        dependencies: []
      },
      {
        id: '2',
        name: 'customer_info',
        type: 'table',
        status: 'warning',
        performance: 72,
        dependencies: ['financial_data']
      },
      {
        id: '3',
        name: 'transaction_data',
        type: 'table',
        status: 'error',
        performance: 45,
        dependencies: ['financial_data', 'customer_info']
      },
      {
        id: '4',
        name: 'inventory',
        type: 'table',
        status: 'normal',
        performance: 92,
        dependencies: []
      },
      {
        id: '5',
        name: 'user_permissions',
        type: 'table',
        status: 'normal',
        performance: 88,
        dependencies: []
      },
      {
        id: '6',
        name: 'valid_roles',
        type: 'table',
        status: 'normal',
        performance: 95,
        dependencies: []
      },
      {
        id: '7',
        name: 'audit_summary_view',
        type: 'view',
        status: 'warning',
        performance: 68,
        dependencies: ['financial_data', 'customer_info', 'transaction_data']
      }
    ]

    setExecutionLogs(mockLogs)
    setPerformanceTrends(mockTrends)
    setOptimizationSuggestions(mockSuggestions)
    setDependencyNodes(mockDependencyNodes)
  }, [])

  // 过滤运行日志
  const filteredLogs = executionLogs.filter(log => {
    const matchesSearch = log.themeName.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = statusFilter === 'all' || log.status === statusFilter
    return matchesSearch && matchesStatus
  })

  // 获取状态徽章
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'success':
        return <Badge className="bg-green-100 text-green-800"><CheckCircle className="w-3 h-3 mr-1" />成功</Badge>
      case 'failed':
        return <Badge className="bg-red-100 text-red-800"><XCircle className="w-3 h-3 mr-1" />失败</Badge>
      case 'running':
        return <Badge className="bg-blue-100 text-blue-800"><Activity className="w-3 h-3 mr-1" />运行中</Badge>
      default:
        return <Badge variant="secondary">{status}</Badge>
    }
  }

  // 获取优先级徽章
  const getPriorityBadge = (priority: string) => {
    switch (priority) {
      case 'high':
        return <Badge className="bg-red-100 text-red-800">高</Badge>
      case 'medium':
        return <Badge className="bg-yellow-100 text-yellow-800">中</Badge>
      case 'low':
        return <Badge className="bg-green-100 text-green-800">低</Badge>
      default:
        return <Badge variant="secondary">{priority}</Badge>
    }
  }

  // 格式化持续时间
  const formatDuration = (ms: number) => {
    if (ms < 1000) return `${ms}ms`
    if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`
    return `${(ms / 60000).toFixed(1)}min`
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          {/* <h1 className="text-3xl font-bold">稽查主题运行分析</h1>
          <p className="text-muted-foreground mt-2">
            监控和分析稽查主题的执行情况，优化查询性能
          </p> */}
        </div>
        <div className="flex gap-2">
          <Button variant="outline" size="sm">
            <RefreshCw className="w-4 h-4 mr-2" />
            刷新数据
          </Button>
          <Button variant="outline" size="sm">
            <Download className="w-4 h-4 mr-2" />
            导出报告
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="logs" className="flex items-center gap-2">
            <Database className="w-4 h-4" />
            运行日志
          </TabsTrigger>
          <TabsTrigger value="performance" className="flex items-center gap-2">
            <TrendingUp className="w-4 h-4" />
            性能趋势
          </TabsTrigger>
          <TabsTrigger value="dependencies" className="flex items-center gap-2">
            <Database className="w-4 h-4" />
            血缘图谱
          </TabsTrigger>
          <TabsTrigger value="optimization" className="flex items-center gap-2">
            <Lightbulb className="w-4 h-4" />
            优化建议
          </TabsTrigger>
        </TabsList>

        {/* 运行日志页签 */}
        <TabsContent value="logs" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>主题脚本运行日志</CardTitle>
              <CardDescription>
                查看稽查主题的执行历史记录和状态
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex gap-4 mb-4">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                    <Input
                      placeholder="搜索主题名称..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="w-48">
                    <SelectValue placeholder="选择状态" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部状态</SelectItem>
                    <SelectItem value="success">成功</SelectItem>
                    <SelectItem value="failed">失败</SelectItem>
                    <SelectItem value="running">运行中</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>主题名称</TableHead>
                    <TableHead>执行时间</TableHead>
                    <TableHead>耗时</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead>处理记录数</TableHead>
                    <TableHead>性能评分</TableHead>
                    <TableHead>操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredLogs.map((log) => (
                    <TableRow key={log.id}>
                      <TableCell className="font-medium">{log.themeName}</TableCell>
                      <TableCell>{log.executionTime}</TableCell>
                      <TableCell>{formatDuration(log.duration)}</TableCell>
                      <TableCell>{getStatusBadge(log.status)}</TableCell>
                      <TableCell>{log.recordsProcessed.toLocaleString()}</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Progress value={log.performanceScore} className="w-20" />
                          <span className="text-sm">{log.performanceScore}%</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Button variant="ghost" size="sm">
                          查看详情
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>

          {/* 运行性能趋势图 */}
          <PerformanceChart data={performanceTrends} />
        </TabsContent>

        {/* 性能趋势页签 */}
        <TabsContent value="performance" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>详细性能分析</CardTitle>
              <CardDescription>
                深入分析执行性能指标和趋势
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <div className="p-4 border rounded-lg">
                  <div className="flex items-center gap-2 mb-2">
                    <Clock className="w-4 h-4 text-blue-500" />
                    <span className="font-medium">总执行次数</span>
                  </div>
                  <p className="text-2xl font-bold">{performanceTrends.length}</p>
                </div>
                <div className="p-4 border rounded-lg">
                  <div className="flex items-center gap-2 mb-2">
                    <TrendingUp className="w-4 h-4 text-green-500" />
                    <span className="font-medium">最佳性能</span>
                  </div>
                  <p className="text-2xl font-bold">
                    {performanceTrends.length > 0 ? 
                      Math.max(...performanceTrends.map(t => t.performanceScore)) : 
                      0
                    }%
                  </p>
                </div>
                <div className="p-4 border rounded-lg">
                  <div className="flex items-center gap-2 mb-2">
                    <AlertTriangle className="w-4 h-4 text-red-500" />
                    <span className="font-medium">性能波动</span>
                  </div>
                  <p className="text-2xl font-bold">
                    {performanceTrends.length > 0 ? 
                      Math.round((Math.max(...performanceTrends.map(t => t.performanceScore)) - 
                                 Math.min(...performanceTrends.map(t => t.performanceScore))) / 
                                Math.max(...performanceTrends.map(t => t.performanceScore)) * 100) : 
                      0
                    }%
                  </p>
                </div>
              </div>

              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>执行ID</TableHead>
                    <TableHead>执行时间</TableHead>
                    <TableHead>耗时</TableHead>
                    <TableHead>性能评分</TableHead>
                    <TableHead>趋势</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {performanceTrends.map((trend, index) => (
                    <TableRow key={trend.executionId}>
                      <TableCell>{trend.executionId}</TableCell>
                      <TableCell>{trend.executionTime}</TableCell>
                      <TableCell>{formatDuration(trend.duration)}</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Progress value={trend.performanceScore} className="w-20" />
                          <span className="text-sm">{trend.performanceScore}%</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        {index > 0 && (
                          <span className={`text-sm ${
                            trend.performanceScore > performanceTrends[index - 1].performanceScore 
                              ? 'text-green-600' 
                              : 'text-red-600'
                          }`}>
                            {trend.performanceScore > performanceTrends[index - 1].performanceScore ? '↑' : '↓'}
                          </span>
                        )}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        {/* 血缘图谱页签 */}
        <TabsContent value="dependencies" className="space-y-4">
          <DependencyGraph data={dependencyNodes} />
        </TabsContent>

        {/* 优化建议页签 */}
        <TabsContent value="optimization" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>智能查询优化建议</CardTitle>
              <CardDescription>
                基于血缘图谱的依赖关系分析，自动生成的SQL语句优化建议
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {optimizationSuggestions.map((suggestion) => (
                  <div key={suggestion.id} className="border rounded-lg p-4">
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex items-center gap-2">
                        {getPriorityBadge(suggestion.priority)}
                        <Badge variant="outline" className="capitalize">
                          {suggestion.type === 'index' && '索引优化'}
                          {suggestion.type === 'query' && '查询优化'}
                          {suggestion.type === 'join' && '连接优化'}
                          {suggestion.type === 'filter' && '过滤优化'}
                        </Badge>
                      </div>
                      <div className="text-right">
                        <p className="text-sm text-gray-600">预计提升</p>
                        <p className="text-lg font-bold text-green-600">{suggestion.estimatedImprovement}%</p>
                      </div>
                    </div>
                    
                    <h4 className="font-medium mb-2">{suggestion.description}</h4>
                    <p className="text-sm text-gray-600 mb-3">{suggestion.impact}</p>
                    
                    <div className="bg-gray-50 p-3 rounded border">
                      <p className="text-sm font-mono text-gray-800">{suggestion.sqlSuggestion}</p>
                    </div>
                    
                    <div className="flex gap-2 mt-3">
                      <Button size="sm" variant="outline">
                        应用建议
                      </Button>
                      <Button size="sm" variant="ghost">
                        查看详情
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* 优化统计 */}
          <Card>
            <CardHeader>
              <CardTitle>优化效果统计</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center">
                  <p className="text-2xl font-bold text-blue-600">
                    {optimizationSuggestions.filter(s => s.priority === 'high').length}
                  </p>
                  <p className="text-sm text-gray-600">高优先级建议</p>
                </div>
                <div className="text-center">
                  <p className="text-2xl font-bold text-yellow-600">
                    {optimizationSuggestions.filter(s => s.priority === 'medium').length}
                  </p>
                  <p className="text-sm text-gray-600">中优先级建议</p>
                </div>
                <div className="text-center">
                  <p className="text-2xl font-bold text-green-600">
                    {optimizationSuggestions.filter(s => s.priority === 'low').length}
                  </p>
                  <p className="text-sm text-gray-600">低优先级建议</p>
                </div>
                <div className="text-center">
                  <p className="text-2xl font-bold text-purple-600">
                    {Math.round(optimizationSuggestions.reduce((sum, s) => sum + s.estimatedImprovement, 0) / optimizationSuggestions.length)}%
                  </p>
                  <p className="text-sm text-gray-600">平均提升效果</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
} 