# 产品文档

好的，根据我们前面关于“电力数据大脑”产品基座、营销稽查智能体MVP功能以及后续数据应用拓展（数据治理、数据研判、场景化分析）的讨论，我为您整理以下四份不同受众和目的的文档：

1. **MVP 产品需求说明文档 (PRD) - 面向研发**
2. **产品介绍文档 - 面向客户**
3. **产品流程文档 - 面向测试和使用者**
4. **完整的产品需求说明文档 (包含MVP及后续规划) - 面向产品和研发**

***


## **1. MVP 产品需求说明文档 (PRD) - 面向研发**

### **“电力数据大脑”暨“营销稽查智能体” MVP 产品需求说明文档**

版本： V1.1

日期： 2025年6月24日

***

#### **1. 引言**

* **1.1. 项目背景与目标：** 详细描述项目起源、当前营销稽查面临的挑战、构建“数据大脑”的战略意义，以及MVP阶段要实现的核心业务价值和验证目标。强调通过LLM赋能稽查智能化。
* **1.2. 术语与定义：** 参照之前讨论的术语表，清晰定义项目中的核心概念（如NL2SQL、语义逻辑原子、LLM、RAG、向量知识库等）。
* **1.3. MVP范围与目标：** 明确本次MVP只包含“营销稽查智能体”的核心闭环功能，包括：稽查规则的智能生成、规则生命周期管理、稽查规则知识分析、以及问题命中管理。同时覆盖“数据大脑”的基础元数据和语义原子管理，确保数据基础的可靠性。
* **1.4. 目标用户：** 明确研发团队需关注的主要用户群体及其在系统中的角色和核心操作（业务专家/稽查人员、数据分析师/数据工程师）。

#### **2. 核心功能需求 (MVP)**

* **2.1. 核心应用界面：**
  * 统一品牌标识（“电力数据大脑”顶部显示）。
  * 主导航菜单：左侧或顶部导航，明确MVP阶段可点击菜单项（元数据概览、规则智能生成、规则管理、知识分析、问题命中），非MVP功能菜单项灰显。
* **2.2. 核心业务概览 (仪表盘)：**
  * 欢迎信息与系统指引。
  * 核心指标摘要：卡片式展示“今日新增稽查规则数量”、“当月执行规则数量”、“待处理问题总数”等。
  * 快捷操作入口：按钮直达“开始设计新稽查规则”、“查看所有元数据”。
* **2.3. 数据巡查模块：**
  * **2.3.1. MaxCompute DI任务管理：**
    * 功能：MaxCompute数据集成任务的创建、配置、监控和管理，支持全量和增量同步模式。
    * 研发关注：MaxCompute SDK集成、任务状态监控、错误处理和重试机制。
  * **2.3.2. XXL-Job定时巡查：**
    * 功能：基于XXL-Job的定时数据巡查任务调度，支持Cron表达式配置和任务依赖管理。
    * 研发关注：XXL-Job集成、任务调度策略、执行日志管理。
  * **2.3.3. 数据质量监控：**
    * 功能：数据异常检测、质量指标统计、告警通知和趋势分析。
    * 研发关注：数据质量规则引擎、实时监控、告警机制设计。
* **2.4. 数据大脑：**
  * **2.4.1. 元数据概览与查询：**
    * 功能：全面搜索与筛选（表名、字段、数据库类型）、结构化列表展示（含字段类型、描述、标签）、字段详情深入（值域示例、物理路径、语义原子关联）、分页。
    * 研发关注：需要与后端数据源连接，实现元数据同步/抽取；前端组件的通用性和可配置性。
  * **2.4.2. 语义逻辑原子管理：**
    * 功能：智能搜索与分类、原子列表展示（名称、类型、描述、关联字段）、用途示例提示、"理解此原子"解释弹窗、分页。
    * 研发关注：语义原子与物理字段的映射关系管理；"理解此原子"功能需调用AI服务。
* **2.5. 营销稽查智能体：**
  * **2.5.1. 稽查主题智能化设计：**
    * **规则创建模式切换：** 自然语言生成模式、可视化编排模式（基于Element Plus组件和Vue3特性实现拖拽、连接、嵌套）。
    * **可视化编排核心交互：**
      * 条件逻辑编排区：条件元逐步引导、条件元组合（与/或、优先级）。
      * 结果列独立定义区：字段选择、属性配置、智能关联数据源。
      * 动态参数支持：定义、配置。
      * 对话式智能调整：前端文本框交互、后端LLM调用、前端画布实时更新。
      * 一键规则逻辑展示：生成逻辑树/流程图（前端图表库实现）。
      * 业务组件区：稽查对象选择、场景化筛选。
    * **实时规则描述：** 前端展示，后端实时生成。
    * **规则输出与解释：** 实时展示业务语义表达式、伪代码逻辑，生成**SQL脚本**，提供格式化与解释功能。
    * **规则保存：** 保存至规则库。
    * 研发关注：复杂的图形化拖拽逻辑实现；NL2SQL、NL2Semantic2SQL的后端AI服务集成；SQL生成器的健壮性；动态参数的解析与注入。
  * **2.5.2. 稽查规则管理：**
    * **规则列表与搜索：** 多维度搜索、状态筛选。
    * **规则生命周期管理：**
      * 规则提交审核：触发审核流程。
      * 规则审核：审核界面、审批/驳回、意见反馈。
      * 规则上线发布：**配置执行周期/例日（如每月2号）**。
      * 内测迭代与优化：**允许调整逻辑，不能删除**，需重新审核上线。
      * 规则启用与禁用：控制运行状态。
    * **状态可视化：** 清晰标记规则状态。
    * **"新建规则"入口、"解构与迭代"：** 路由跳转与数据传递。
    * **规则分页。**
    * 研发关注：严谨的状态机流转；定时任务调度系统集成；规则版本管理（MVP可简化）。
  * **2.5.3. 政策文件处理与稽查要素提取：** (新模块)
    * **政策文件解析：**
      * 功能：支持PDF、DOC、DOCX等格式的政策文件上传和解析，自动提取文本内容。
      * 研发关注：文件解析库集成、OCR技术应用、文本预处理。
    * **稽查要素智能提取：**
      * 功能：基于AI技术从政策文件中自动提取稽查要素（条件、阈值、维度、度量等）。
      * 研发关注：NLP模型集成、要素识别算法、置信度评估。
    * **稽查规则池生成：**
      * 功能：基于提取的稽查要素，智能生成稽查规则池，支持规则模板化管理。
      * 研发关注：规则生成算法、模板引擎、规则有效性验证。
    * **要素到规则转换：**
      * 功能：支持从稽查规则池中选择规则模板，跳转到可视化规则编排进行具体规则设计。
      * 研发关注：数据传递机制、界面跳转逻辑、规则模板应用。
  * **2.5.4. 稽查规则知识分析：** (增强模块)
    * **知识库升级：**
      * 功能：支持语义检索和强大知识表达的向量知识库。
      * 研发关注：向量数据库集成；RAG管道构建（文本嵌入、检索、大模型生成）；非结构化数据摄入处理。
    * **核心分析功能：**
      * 逻辑完整性分析、阈值与地域合理性分析、风险拦截准确性分析。
      * 研发关注：分析算法/逻辑实现；与LLM的交互，将分析结果结构化。
    * **知识检索与推荐：** 自然语言查询、智能推荐。
    * **分析结果应用与规则回流：** 提供"生成新规则"或"修改现有规则"入口，并将分析建议**无缝回流至"稽查规则智能生成"阶段**进行逻辑编排。
    * 研发关注：回流数据的格式定义；前端路由与参数传递。
* **2.6. 问题命中管理：**
  * **2.6.1. 问题命中列表：** 概览统计、多维度搜索与筛选、列表展示（问题ID、客户、规则、日期、状态、描述）。
  * **2.6.2. 问题命中报告：** 动态标题、摘要信息、问题命中原因解释（LLM生成）、智能整改建议（LLM生成）。
  * 研发关注：大批量问题数据的存储与查询性能；AI生成解释和建议的后端接口。

#### **3. 非功能性需求 (MVP)**

* **3.1. 性能：** 页面加载<3s，复杂查询/规则生成<10s，并发用户数50+。
* **3.2. 安全性：** RBAC权限控制，数据传输/存储加密，操作日志审计。
* **3.3. 可伸缩性：** 模块化设计，支持后续水平扩展。
* **3.4. 可维护性与可扩展性：** 代码规范，清晰架构，易于迭代。
* **3.5. 用户体验：** 简洁直观，响应迅速，错误提示友好。

#### **4. 技术栈与简要架构 (MVP)**

* **前端：** Vue3 + Vite + Element Plus + Pinia (yudao-ui-admin-vue3模板)。
* **后端：** Spring Boot (Java) 或 FastAPI (Python)。
* **数据库：** MySQL/PostgreSQL (关系型，核心业务数据) + Milvus/PGVector (向量数据库，知识库)。
* **AI/LLM：** 调用第三方大模型API (如OpenAI API) 或部署开源模型，结合LangChain/LlamaIndex构建RAG。
* **架构：** 微服务架构，前端与后端API交互，后端服务层调用数据库和AI服务层。

***

## **2. 产品介绍文档 - 面向客户**

### **"电力数据大脑"暨"营销稽查智能体"：赋能智慧稽查，洞察业务风险**

**发布日期：** 2025年6月20日

***

#### **1. 业务挑战：传统稽查的困境**

在电力业务高速发展的今天，面对海量的业务数据和不断变化的政策法规，传统的营销稽查工作正面临前所未有的挑战：

* **规则定义复杂耗时：** 业务规则难以快速转化为可执行的稽查逻辑，效率低下。
* **风险识别滞后被动：** 缺乏主动、智能的风险洞察能力，问题发现不及时。
* **知识体系分散老化：** 大量经验和政策以非结构化形式存在，难以有效利用和更新。
* **数据孤岛与质量问题：** 数据分散、质量参差不齐，难以支撑全面深入的分析。

#### **2. 产品愿景：打造智慧稽查大脑**

"电力数据大脑"旨在构建一个以数据为核心、以AI为驱动的智能平台。其中，"营销稽查智能体"作为核心应用，致力于：

* **将业务智慧转化为机器可执行规则，实现稽查自动化。**
* **变被动稽查为主动预警，精准识别潜在风险。**
* **激活海量非结构化知识，赋能智能决策。**
* **提升数据资产价值，支撑精细化运营。**

#### **3. 核心功能亮点**

* **3.1. 数据巡查模块，夯实数据质量基础**
  * **MaxCompute DI任务适配：** 无缝集成阿里云MaxCompute数据集成服务，支持全量和增量数据同步，确保数据的及时性和完整性。
  * **XXL-Job定时调度：** 基于成熟的XXL-Job框架，提供灵活的定时巡查任务调度，支持复杂的Cron表达式和任务依赖管理。
  * **智能数据质量监控：** 实时监控数据异常，自动生成质量报告，及时发现数据问题并提供告警通知。
  * **多数据源适配：** 支持MySQL、PostgreSQL、Hive等多种数据源的巡查任务，满足异构环境需求。

* **3.2. 智能规则生成，告别复杂代码**
  * **自然语言快速定义：** 业务人员只需输入日常语言描述，系统即可智能理解并转化为稽查规则，无需编写代码。
  * **可视化拖拽编排：** 提供直观的图形化界面，通过拖拽组件即可像搭积木一样构建复杂规则逻辑，所见即所得。
  * **智能参数与动态调整：** 规则支持动态参数，并能通过对话式交互进行实时优化，让规则更灵活、适应性更强。
  * **自动生成SQL脚本：** 规则定义完成后，系统自动生成可执行的SQL脚本，无缝对接数据源。
* **3.3. 政策文件智能解析，激活非结构化知识**
  * **多格式文件解析：** 支持PDF、DOC、DOCX等多种格式的政策文件上传，自动提取文本内容，无需人工转换。
  * **AI驱动要素提取：** 运用先进的自然语言处理技术，从政策文件中智能识别和提取稽查要素，包括条件、阈值、维度等关键信息。
  * **稽查规则池生成：** 基于提取的要素，自动生成结构化的稽查规则池，为规则设计提供丰富的模板库。
  * **无缝规则转换：** 支持从规则池直接跳转到可视化编排界面，快速将政策要求转化为可执行的稽查规则。

* **3.4. 稽查规则全生命周期智能管理**
  * **规则智能审核：** 规则提交后，支持多级审批，确保规则的合规性与准确性。
  * **自动化上线与排期：** 规则审核通过后，可配置自动执行周期（如"每月2号"），实现稽查任务的自动化。
  * **安全迭代与优化：** 支持在线对已上线规则进行逻辑调整和内测迭代，同时保障线上稳定性，并提供追溯能力。
  * **灵活启用与禁用：** 根据业务需求，随时控制规则的运行状态。
* **3.5. 稽查知识智能分析，深度赋能规则优化**
  * **知识库智能升级：** 将传统政策、案例等非结构化知识，升级为可语义检索的向量知识库，让知识活起来。
  * **规则智能诊断：** 运用大模型能力，智能分析稽查规则的**逻辑完整性**、**阈值地域合理性**，以及**风险拦截准确性**。
  * **智能优化建议：** 基于知识分析结果，系统自动提供规则优化建议，甚至直接提供"新规则"或"修改方案"的线索，形成规则迭代的智能闭环。
  * **智能知识检索：** 通过自然语言即可快速查询相关政策、案例，辅助规则设计和问题研判。
* **3.6. 问题命中精准识别与高效管理**
  * **问题列表与多维筛选：** 集中展示所有命中问题，支持多维度搜索，快速定位关键风险点。
  * **智能报告与原因解释：** 为每个问题生成详细报告，智能解释问题命中原因，并提供智能化的整改建议。

#### **4. 产品价值与效益**

* **提升稽查效率：** 规则自动化生成与执行，大幅减少人工投入。
* **增强风险识别能力：** 从被动发现到主动预警，稽查覆盖面更广，精准度更高。
* **降低操作风险：** 规则标准化、流程自动化，减少人为错误。
* **赋能业务创新：** 将业务专家经验沉淀为可复用的智能资产，推动业务规则迭代。
* **激活数据价值：** 将分散的数据转化为稽查洞察，实现数据驱动的业务决策。

#### **5. 未来展望**

"电力数据大脑"将持续演进，未来将集成更深层次的数据治理（如数据血缘、质量监控）、数据研判（如更复杂的预测模型）以及更多场景化分析应用，致力于成为企业数字化转型的核心引擎，全面提升数据赋能业务的能力。

***

## **3. 产品流程文档 - 面向测试和使用者**

### **"电力数据大脑"暨"营销稽查智能体" MVP 核心业务流程指南**

版本： V1.0

日期： 2025年6月20日

***

#### **1. 登录与仪表盘概览**

* **目的：** 快速进入系统，了解核心业务指标。
* **操作步骤：**
  1. 打开浏览器，输入系统登录地址。
  2. 输入用户名和密码，点击"登录"按钮。
  3. 登录成功后，系统默认进入"核心业务概览"（仪表盘）页面。
  4. **在仪表盘中：** 您可以看到"今日新增稽查规则数量"、"待处理问题总数"等关键指标的统计卡片，下方提供"开始设计新稽查规则"等快捷入口。

#### **2. 场景一：稽查规则的智能创建与上线**

* **目的：** 从零开始设计一条稽查规则，并使其上线自动运行。
* **操作步骤：**
  1. **进入规则智能生成：**
     * 在左侧导航栏点击"营销稽查智能体" -> "稽查规则智能生成"。
     * 或在仪表盘点击"开始设计新稽查规则"按钮。
  2. **选择创建模式：**
     * **如果您擅长自然语言描述：** 选择"自然语言生成模式"，在文本框中输入您对稽查规则的描述（例如："查找年龄大于60岁且电力消费异常的用户"）。点击"生成规则"。
     * **如果您偏好可视化操作：** 选择"可视化编排模式"。 
       * **选择稽查对象：** 首先在左侧区域选择本次稽查的"对象"（如"用电户"）。
       * **拖拽构建条件：** 从左侧"业务组件区"拖拽相关指标（如"年龄"、"电力消费"）到画布中央的"条件逻辑编排区"。
       * **配置条件与组合：** 点击拖拽出的条件块，在弹出的配置框中设置具体的运算符和值（如"年龄 > 60"）。然后拖拽"与/或"连接符，将多个条件块连接起来，明确逻辑关系。
       * **定义结果列：** 在"结果列独立定义区"点击"添加结果列"，选择或输入您希望规则执行后显示的数据字段（如"客户名称"、"户号"、"联系电话"）。
       * **动态调整：** 您也可以在右下角的"对话式交互"框中输入指令（如"把年龄条件改为大于50"），系统将自动调整画布上的规则。
       * **预览逻辑：** 点击"一键规则逻辑展示"按钮，可在弹窗中看到规则的逻辑树或流程图。
  3. **规则输出与保存：**
     * 无论采用哪种模式，右侧区域都会实时显示规则的自然语言描述、伪代码逻辑和**SQL脚本**。请仔细检查确认。
     * 点击"保存规则"按钮，填写规则名称等信息，将规则保存为"草稿"状态。
  4. **提交审核：**
     * 保存成功后，系统会提示您是否提交审核。点击"提交审核"或在"稽查规则管理"列表中找到该规则，点击"提交审核"按钮。规则状态变为"待审核"。
  5. **（审核员角色）进行审核：**
     * 审核员登录系统，进入"稽查规则管理"，找到"待审核"状态的规则。
     * 点击"审核"按钮，查看规则详情（逻辑图、SQL脚本、描述）。
     * 审核员可选择"通过"或"驳回"，并填写意见。
  6. **规则上线发布：**
     * 规则审核通过后，在"稽查规则管理"列表中，点击该规则的"上线"按钮。
     * 在弹出的对话框中，配置规则的**执行例日**（例如，选择"每月2号"），点击"确定"完成上线。规则状态变为"已上线"，并将按计划自动运行。

#### **3. 场景二：问题命中管理与分析**

* **目的：** 查看稽查规则发现的问题，并理解问题原因及获取整改建议。
* **操作步骤：**
  1. **进入问题命中列表：**
     * 在左侧导航栏点击"营销稽查智能体" -> "问题命中管理"。
  2. **浏览与筛选问题：**
     * 页面顶部显示待核实和已处理的问题数量。
     * 您可以使用上方的搜索框（客户名称、规则名称、问题描述关键词）或筛选条件（稽查状态、日期范围）来查找特定问题。
  3. **查看问题命中报告：**
     * 在问题列表中找到您想查看的问题，点击右侧的"查看报告"按钮。
     * 系统将展示详细的"问题命中报告"，包括问题摘要信息、稽查规则智能体对问题命中原因的**智能解释**，以及针对该问题的**智能整改建议**。
  4. **（如果支持）更新问题状态：** 在报告页面或问题列表中，根据实际处理情况更新问题的稽查状态（如"已处理"、"已驳回"）。
  5. **返回：** 点击页面上方的"返回列表"按钮，回到问题命中列表。

#### **4. 场景三：利用知识分析优化或生成规则**

* **目的：** 利用智能知识分析的结果，来优化现有规则或生成新的稽查规则。
* **操作步骤：**
  1. **进入稽查规则知识分析：**
     * 在左侧导航栏点击"营销稽查智能体" -> "稽查规则知识分析"。
  2. **执行知识检索或规则分析：**
     * **语义检索：** 在搜索框中输入您想了解的政策、案例或业务知识（如"关于分布式光伏的计量异常规定"），点击搜索。系统将返回相关知识片段和智能推荐。
     * **规则分析：** 选择您希望进行分析的现有稽查规则（可能通过列表或搜索找到），点击"分析"按钮。系统将对其进行**逻辑完整性、阈值地域合理性、风险拦截准确性**等多维度分析，并在页面上展示分析报告和优化建议。
  3. **应用分析结果：**
     * **生成新规则：** 如果分析结果提示了一个全新的稽查思路或业务场景，点击页面上的"生成新规则"按钮。系统将自动携带相关建议，跳转到"稽查规则智能生成"页面的自然语言模式，您可以基于这些建议快速构建新规则。
     * **修改现有规则：** 如果分析结果提示当前规则存在逻辑缺陷、阈值不合理或拦截不准确等问题，点击页面上的"修改现有规则"按钮。系统将把原规则加载到"稽查规则智能生成"页面的可视化编排模式，并高亮显示建议修改的区域，您可以直接进行调整和优化。
  4. **（接续场景一）提交迭代规则：** 无论是新生成的规则还是修改后的规则，都需要按照"场景一：稽查规则的智能创建与上线"的步骤，重新提交审核并进行上线操作。

#### **5. 场景四：数据巡查任务管理**

* **目的：** 创建和管理数据巡查任务，确保数据质量。
* **操作步骤：**
  1. **进入数据巡查模块：**
     * 在左侧导航栏点击"数据巡查模块" -> "任务管理"。
  2. **创建MaxCompute DI任务：**
     * 点击"新建任务"按钮，选择"MaxCompute DI任务"。
     * 配置源数据库和目标数据库连接信息。
     * 设置同步模式（全量/增量）和分区配置。
     * 配置字段映射和过滤条件。
     * 设置调度周期，点击"保存并启动"。
  3. **创建XXL-Job定时巡查：**
     * 点击"新建任务"按钮，选择"定时巡查任务"。
     * 编写或选择巡查SQL脚本。
     * 配置Cron表达式设置执行时间。
     * 设置告警阈值和通知方式。
     * 点击"保存并启动"。
  4. **监控任务执行：**
     * 在任务列表中查看任务状态和执行历史。
     * 点击"查看日志"了解任务执行详情。
     * 根据需要调整任务配置或暂停任务。

#### **6. 场景五：政策文件处理与稽查要素提取**

* **目的：** 从政策文件中提取稽查要素，生成稽查规则池。
* **操作步骤：**
  1. **上传政策文件：**
     * 在左侧导航栏点击"营销稽查智能体" -> "政策文件处理"。
     * 点击"上传文件"按钮，选择PDF、DOC或DOCX格式的政策文件。
     * 填写文件标题、类型和描述信息，点击"确认上传"。
  2. **提取稽查要素：**
     * 文件上传成功后，点击"提取要素"按钮。
     * 系统将自动解析文件内容，使用AI技术提取稽查要素。
     * 查看提取结果，包括条件、阈值、维度等要素信息。
     * 对AI提取的要素进行人工验证和修正。
  3. **生成稽查规则池：**
     * 验证要素完成后，点击"生成规则池"按钮。
     * 系统将基于要素自动生成规则模板。
     * 查看生成的规则池，包括规则分类和适用范围。
  4. **转换为具体规则：**
     * 在规则池中选择感兴趣的规则模板。
     * 点击"转换为规则"按钮，系统将跳转到可视化编排界面。
     * 基于模板进行具体的规则设计和配置。

#### **7. 元数据与语义原子查询**

* **目的：** 了解系统中的数据资产和业务概念。
* **操作步骤：**
  1. 在左侧导航栏点击"数据大脑" -> "元数据概览与查询"或"语义逻辑原子管理"。
  2. 使用搜索框和筛选功能查找您需要的数据表、字段或语义原子。
  3. 点击列表中的具体项，查看详细描述、值域、关联关系等信息。
  4. 在语义原子管理页面，点击"理解此原子"，获取该原子更详细的业务解释。

***

## **4. 完整的产品需求说明文档 (包含MVP及后续规划) - 面向产品和研发**

### **"电力数据大脑"暨"营销稽查智能体" 完整产品需求说明文档**

版本： V1.0

日期： 2025年6月20日

***

#### **1. 引言**

* **1.1. 项目背景与愿景：** 详细阐述构建企业级"数据大脑"的长期战略愿景，以及"营销稽查智能体"作为核心应用在实现这一愿景中的关键作用。强调数据驱动、AI赋能的理念，并展望其未来在整个电力业务中的渗透与影响力。
* **1.2. 术语与定义：** 参照前文的术语表，确保所有相关方对核心概念的理解一致。
* **1.3. 产品范围与阶段规划：**
  * **MVP阶段 (Phase 1 - 核心验证)：** 详细定义本阶段的核心功能，主要聚焦于营销稽查的**智能规则生成、规则生命周期管理、稽查规则知识分析以及问题命中管理**，并辅以数据大脑的基础能力。这是产品的核心闭环。
  * **第二阶段 (Phase 2 - 深度拓展与集成)：** 将围绕MVP成果，进一步拓展数据治理能力、深化数据研判模型、增加更多场景化分析应用，并加强与企业现有系统的集成。
  * **第三阶段 (Phase 3 - 平台化与生态构建)：** 目标是打造一个开放、可配置、可扩展的数据智能平台，支持更多业务线的智能化需求，并形成数据智能应用生态。
* **1.4. 目标用户：** 详细描述所有目标用户群体（业务专家、稽查人员、数据分析师、数据工程师、IT运维人员、业务高管等），及其在不同阶段对产品的期望和需求。

#### **2. 核心功能需求 (分阶段)**

* **2.1. 核心应用界面 (各阶段通用，随功能扩展)：**
  * 统一品牌标识、主导航菜单（随功能扩展，菜单项逐步点亮）。
  * 系统消息/通知中心。
  * 用户个人中心与设置。
* **2.2. 核心业务概览 (仪表盘 - 持续迭代)：**
  * **MVP：** 欢迎信息、核心指标摘要、快捷操作入口。
  * **Phase 2：** 增加个性化定制仪表盘、支持多维度下钻分析、集成更多业务系统关键指标、加入数据治理健康度评分、稽查风险Top N。
  * **Phase 3：** 演变为可配置的智能工作台，支持自定义数据源和可视化组件。
* **2.3. 数据大脑 (持续深化)：**
  * **MVP：**
    * **元数据概览与查询：** 全面搜索筛选、结构化列表、字段详情深入、分页。
    * **语义逻辑原子管理：** 智能搜索分类、列表展示、用途示例、"理解此原子"解释、分页。
  * **Phase 2：**
    * **元数据管理深化：** 增加元数据自动采集与同步能力、元数据变更历史与版本管理、数据血缘关系追溯与影响分析图谱、数据资产地图与分类分级。
    * **语义原子管理深化：** 支持"新建语义原子"功能，提供语义原子关系图谱可视化、语义原子变更审批流程。
  * **Phase 3：**
    * **数据质量管理：** 定义数据质量规则（完整性、准确性、一致性、及时性等）、自动化质量监控、质量报告与告警、数据清洗与修复建议（与LLM结合）。
    * **数据安全与隐私：** 数据脱敏、数据访问控制策略、数据隐私合规性检查。
    * **数据标准管理：** 统一数据字典、编码规范管理。
* **2.4. 营销稽查智能体 (核心迭代)：**
  * **2.4.1. 稽查主题智能化设计：**
    * **MVP：** 自然语言生成、可视化编排（条件逻辑、结果列、动态参数、对话式调整、逻辑图预览）、实时规则描述、SQL脚本生成、规则保存。
    * **Phase 2：**
      * **高级智能优化：** 规则智能校验与优化建议（如性能优化、逻辑冗余检测），规则冲突检测与解决（基于知识图谱或LLM），关联政策法规/历史案例的智能推荐。
      * **更灵活的输出：** 支持生成多种执行引擎的脚本（如Spark SQL、Presto SQL）。
      * **规则推荐：** 基于历史稽查数据和知识库，智能推荐高价值的潜在规则。
    * **Phase 3：**
      * **自适应规则学习：** 根据规则执行效果（误报、漏报），系统自动建议规则调整，甚至进行小范围的规则自动迭代。
      * **多源异构规则整合：** 支持更复杂的跨数据源、跨系统规则编排。
  * **2.4.2. 稽查规则管理：**
    * **MVP：** 规则列表与搜索、生命周期管理（提交审核、审核、上线、内测迭代、启用禁用）、状态可视化、"新建规则"入口、"解构与迭代"、分页。
    * **Phase 2：**
      * **规则版本管理与回溯：** 详细记录每次规则变更，支持版本比对、快速回滚。
      * **规则执行历史与性能分析：** 提供规则每次执行的详细日志、性能指标、命中率统计。
      * **审批流定制：** 支持可配置的规则审批流程。
    * **Phase 3：**
      * **规则效果评估与迭代建议：** 引入指标量化规则价值，并提供智能迭代建议。
      * **规则集管理：** 组织和管理相互关联的规则集。
  * **2.4.3. 稽查规则知识分析：**
    * **MVP：** 知识库升级（LLM+RAG向量知识库）、核心分析功能（逻辑完整性、阈值地域合理性、风险拦截准确性）、知识检索与推荐、分析结果应用与规则回流。
    * **Phase 2：**
      * **知识图谱构建与可视化：** 将非结构化知识转化为结构化的知识图谱，并提供可视化界面。
      * **多模态知识分析：** 支持对图片、语音等非文本知识的分析。
      * **政策法规自动更新与解读：** 自动监测外部政策变化，并智能解读其对现有规则的影响。
    * **Phase 3：**
      * **知识发现与推理：** 基于知识图谱进行复杂推理，发现潜在关联和新的稽查点。
      * **智能决策支持系统：** 将知识分析、规则引擎与业务流程深度融合，提供决策建议。
* **2.5. 问题命中管理：**
  * **2.5.1. 问题命中列表：**
    * **MVP：** 概览统计、多维度搜索筛选、列表展示、"查看报告"。
    * **Phase 2：**
      * **问题分类与聚类：** 智能识别同类问题并进行聚类，便于批量处理。
      * **误报/漏报检测：** 通过反馈机制和数据分析，智能识别规则的误报和漏报情况。
      * **问题趋势分析：** 监控问题发生频率、类型变化趋势。
  * **2.5.2. 问题命中报告：**
    * **MVP：** 动态标题、摘要信息、问题命中原因解释、智能整改建议。
    * **Phase 2：**
      * **关联业务数据透视：** 在报告中直接展示与问题相关的多维业务数据，便于深入分析。
      * **整改任务派发与追踪：** 将整改建议转化为可执行任务，并集成到工单系统或业务流程中进行派发和进度追踪。
      * **知识库关联：** 报告中直接链接到相关的知识库文档和政策条款。
    * **Phase 3：**
      * **自动化问题处置：** 对于低风险或标准化问题，提供自动化修复方案。
      * **问题风险等级评估：** 根据问题的潜在影响和严重性进行智能分级。
* **2.6. 数据应用产品 (第二阶段及以后)：**
  * **2.6.1. 数据研判：**
    * **Phase 2：** 基于"数据大脑"沉淀的高质量数据及语义层，利用高级统计分析、机器学习算法和LLM逻辑推理，实现对复杂业务数据的深度挖掘、趋势预判、风险评估和异常发现，为业务决策提供前瞻性和洞察力。
    * **Phase 3：** 演变为通用预测与智能决策平台，支持自定义模型构建与部署。
  * **2.6.2. 场景化分析：**
    * **Phase 2：** 围绕企业特定业务需求和痛点，利用"数据大脑"标准化数据资产和语义层，快速构建定制化的数据应用和分析看板（如客户行为分析、电网运行优化、供应链风险分析等），直接服务于具体业务场景，提供针对性的数据洞察和决策支持。
    * **Phase 3：** 提供开放的API和SDK，支持第三方或业务部门基于"数据大脑"构建自己的数据应用生态。

#### **3. 非功能性需求 (完整)**

* **3.1. 性能：** 严格定义各个模块的响应时间、吞吐量和并发用户数指标，尤其是针对大规模数据稽查和复杂分析任务。
* **3.2. 安全性：** 详细定义数据加密、访问控制、权限管理、漏洞防护、安全审计等企业级安全标准。
* **3.3. 可伸缩性：** 明确微服务架构下的服务扩容策略、数据库分库分表、读写分离等方案。
* **3.4. 可靠性与可用性：** 定义系统SLA、故障恢复时间（RTO/RPO）、数据备份与恢复策略、灾备方案。
* **3.5. 可维护性与可扩展性：** 代码规范、API标准化、服务解耦、组件化设计，便于持续集成与新功能开发。
* **3.6. 用户体验：** 界面统一、操作流畅、易用性、可访问性。
* **3.7. 兼容性：** 浏览器兼容性、与其他系统（如业务系统、数据湖）的集成兼容性。

#### **4. 总体架构设计与技术选型 (完整)**

* **4.1. 系统分层与模块划分：**
  * **前端应用层：** 统一的Web界面，包含数据巡查管理、营销稽查智能体、数据大脑等功能模块。
  * **后端服务层：**
    * **datamind-server-data-inspection：** 数据巡查模块服务，负责MaxCompute DI任务管理和XXL-Job定时巡查。
    * **datamind-server-emss-inspection：** 营销稽查业务服务，包含政策文件处理、稽查要素提取、规则池管理和可视化规则编排。
    * **datamind-server-data-meta：** 数据元数据管理服务。
    * **datamind-server-data-semantic：** 数据语义服务。
    * **datamind-server-rule-engine：** 规则引擎服务。
  * **数据存储层：** 关系型数据库（MySQL/PostgreSQL）、向量数据库（Milvus）、文件存储（MinIO/OSS）。
  * **智能服务层：** AI模型服务、RAG引擎、NLP文本处理、政策文件解析引擎。
  * **数据集成层：** MaxCompute SDK、XXL-Job调度、多数据源连接器。
  * **数据治理层：** 元数据管理、数据质量监控、血缘关系分析。
* **4.2. 技术栈选型：**
  * **前端技术栈：** Vue3 + Element Plus + TypeScript + Vite
  * **后端技术栈：** Spring Boot + Spring Cloud + MyBatis Plus + Java 8+
  * **数据库技术栈：** MySQL 8.0（主数据库）+ Milvus（向量数据库）+ Redis（缓存）
  * **AI与智能服务：** OpenAI GPT-4/自研大模型 + LangChain + Sentence Transformers
  * **数据集成技术：** MaxCompute SDK + XXL-Job + Apache Kafka + Spring Batch
  * **文件处理技术：** Apache Tika（文档解析）+ Apache POI（Office文档）+ PDFBox（PDF处理）
  * **消息队列：** Apache Kafka + RabbitMQ
  * **监控与运维：** Prometheus + Grafana + ELK Stack + Docker + Kubernetes
  * **CI/CD：** Jenkins + GitLab CI + Maven + SonarQube
* **4.3. 数据流与交互：** 绘制详细的数据流图，说明数据如何在各模块间流动、转换和交互。
* **4.4. 部署与运维：** 容器化（Docker/Kubernetes）、微服务治理（服务注册、发现、配置、网关）、监控报警（Prometheus/Grafana）、日志管理（ELK Stack）、CI/CD流程。

#### **5. 团队与协作**

* **5.1. 核心开发角色与职责：** 细化产品经理、前端开发、后端开发、AI/算法工程师、数据工程师、测试工程师、运维工程师等角色职责。
* **5.2. 跨团队协作机制：** 敏捷开发流程、Scrum/Kanban、定期沟通机制、统一的文档和代码管理平台。

***
