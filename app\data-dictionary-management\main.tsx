/*
 * @Author: king<PERSON><PERSON>
 * @Date: 2025-07-06 18:23:54
 * @LastEditTime: 2025-07-06 20:19:23
 * @LastEditors: kingasky
 * @Description: 
 * @FilePath: \datamind-web-prototype\app\data-dictionary-management\main.tsx
 */
"use client";
import React from "react";
import { mockDictionary } from "../../lib/dictionary-identifier";
import type { Dictionary } from "../../types/dictionary";
import DictionaryTable from "../../components/dictionary-display/DictionaryTable";

export default function DataDictionaryManagement() {
  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-4">数据字典管理</h1>
      {mockDictionary.map((entry) => (
        <DictionaryTable key={entry.table} entry={entry} />
      ))}
    </div>
  );
} 