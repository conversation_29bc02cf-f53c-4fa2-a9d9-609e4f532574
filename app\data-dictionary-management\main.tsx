"use client";
import React from "react";
import { mockDictionary } from "../../lib/dictionary-identifier";
import type { Dictionary } from "../../types/dictionary";

export default function DataDictionaryManagement() {
  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-4">数据字典管理</h1>
      {mockDictionary.map((entry) => (
        <div key={entry.table} className="mb-8 border rounded p-4 bg-white shadow">
          <h2 className="text-xl font-semibold mb-2">表：{entry.table}</h2>
          <p className="mb-2 text-gray-600">{entry.description}</p>
          <table className="w-full border mt-2">
            <thead>
              <tr className="bg-gray-100">
                <th className="border px-2 py-1">字段名</th>
                <th className="border px-2 py-1">类型</th>
                <th className="border px-2 py-1">描述</th>
                <th className="border px-2 py-1">业务含义</th>
                <th className="border px-2 py-1">值域</th>
              </tr>
            </thead>
            <tbody>
              {entry.fields.map((field) => (
                <tr key={field.name}>
                  <td className="border px-2 py-1">{field.name}</td>
                  <td className="border px-2 py-1">{field.type}</td>
                  <td className="border px-2 py-1">{field.description || '-'}</td>
                  <td className="border px-2 py-1">{field.businessMeaning || '-'}</td>
                  <td className="border px-2 py-1">
                    {field.values ? (
                      <ul>
                        {field.values.map((v) => (
                          <li key={v.value}>{v.value} - {v.label}</li>
                        ))}
                      </ul>
                    ) : '-'}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      ))}
    </div>
  );
} 