import type { 
  Relation, 
  InferredRelation, 
  TableReference, 
  ColumnReference,
  RelationMiningConfig,
  RelationGraph,
  GraphNode,
  GraphEdge
} from '@/types/sql-analysis'

export class RelationMiningEngine {
  private config: RelationMiningConfig

  constructor(config: Partial<RelationMiningConfig> = {}) {
    this.config = {
      enable_join_analysis: true,
      enable_where_analysis: true,
      enable_field_name_inference: true,
      enable_value_pattern_inference: true,
      min_confidence_threshold: 0.6,
      max_inference_depth: 3,
      ...config
    }
  }

  // 主要的关联关系挖掘方法
  mineRelations(
    explicitRelations: Relation[],
    tables: TableReference[],
    columns: ColumnReference[]
  ): InferredRelation[] {
    const inferredRelations: InferredRelation[] = []

    // 基于字段名相似性推断关系
    if (this.config.enable_field_name_inference) {
      const fieldNameRelations = this.inferFieldNameRelations(tables, columns)
      inferredRelations.push(...fieldNameRelations)
    }

    // 基于值模式推断关系
    if (this.config.enable_value_pattern_inference) {
      const valuePatternRelations = this.inferValuePatternRelations(columns)
      inferredRelations.push(...valuePatternRelations)
    }

    // 过滤低置信度关系
    return inferredRelations.filter(rel => rel.confidence >= this.config.min_confidence_threshold)
  }

  // 基于字段名相似性推断关系
  private inferFieldNameRelations(
    tables: TableReference[],
    columns: ColumnReference[]
  ): InferredRelation[] {
    const relations: InferredRelation[] = []
    const tableColumns = this.groupColumnsByTable(columns)

    // 查找可能的外键关系
    for (const [tableName, tableColumnList] of Object.entries(tableColumns)) {
      for (const column of tableColumnList) {
        // 检查是否是外键模式 (如 user_id, customer_id)
        const foreignKeyMatch = this.matchForeignKeyPattern(column.name)
        if (foreignKeyMatch) {
          const targetTable = foreignKeyMatch.targetTable
          const targetColumn = foreignKeyMatch.targetColumn

          // 检查目标表是否存在
          if (this.tableExists(targetTable, tables)) {
            relations.push({
              id: this.generateId(),
              from_table: tableName,
              from_column: column.name,
              to_table: targetTable,
              to_column: targetColumn,
              relation_type: 'WHERE_CONDITION',
              confidence: foreignKeyMatch.confidence,
              sql_source: 'INFERRED_FROM_FIELD_NAME',
              inference_method: 'FIELD_NAME_SIMILARITY',
              evidence: [
                `字段名 "${column.name}" 匹配外键模式`,
                `推断目标表: ${targetTable}`,
                `推断目标字段: ${targetColumn}`
              ],
              manual_verified: false,
              created_at: new Date()
            })
          }
        }
      }
    }

    return relations
  }

  // 基于值模式推断关系
  private inferValuePatternRelations(columns: ColumnReference[]): InferredRelation[] {
    const relations: InferredRelation[] = []
    
    // 查找相同名称的字段（可能是关联字段）
    const columnGroups = this.groupColumnsByName(columns)
    
    for (const [columnName, columnList] of Object.entries(columnGroups)) {
      if (columnList.length > 1) {
        // 为每对表创建推断关系
        for (let i = 0; i < columnList.length; i++) {
          for (let j = i + 1; j < columnList.length; j++) {
            const col1 = columnList[i]
            const col2 = columnList[j]
            
            if (col1.table !== col2.table) {
              relations.push({
                id: this.generateId(),
                from_table: col1.table,
                from_column: col1.name,
                to_table: col2.table,
                to_column: col2.name,
                relation_type: 'WHERE_CONDITION',
                confidence: this.calculateNameSimilarityConfidence(col1.name, col2.name),
                sql_source: 'INFERRED_FROM_VALUE_PATTERN',
                inference_method: 'VALUE_PATTERN',
                evidence: [
                  `字段名相同: "${columnName}"`,
                  `出现在不同表中: ${col1.table}, ${col2.table}`,
                  `可能存在关联关系`
                ],
                manual_verified: false,
                created_at: new Date()
              })
            }
          }
        }
      }
    }

    return relations
  }

  // 匹配外键命名模式
  private matchForeignKeyPattern(fieldName: string): {
    targetTable: string
    targetColumn: string
    confidence: number
  } | null {
    const patterns = [
      // user_id -> users.id
      {
        regex: /^(\w+)_id$/i,
        getTargetTable: (match: RegExpMatchArray) => `${match[1]}s`,
        getTargetColumn: () => 'id',
        confidence: 0.8
      },
      // customer_id -> customer.id
      {
        regex: /^(\w+)_id$/i,
        getTargetTable: (match: RegExpMatchArray) => match[1],
        getTargetColumn: () => 'id',
        confidence: 0.75
      },
      // userId -> user.id
      {
        regex: /^(\w+)Id$/,
        getTargetTable: (match: RegExpMatchArray) => match[1].toLowerCase(),
        getTargetColumn: () => 'id',
        confidence: 0.7
      },
      // user_code -> users.code
      {
        regex: /^(\w+)_code$/i,
        getTargetTable: (match: RegExpMatchArray) => `${match[1]}s`,
        getTargetColumn: () => 'code',
        confidence: 0.7
      }
    ]

    for (const pattern of patterns) {
      const match = fieldName.match(pattern.regex)
      if (match) {
        return {
          targetTable: pattern.getTargetTable(match),
          targetColumn: pattern.getTargetColumn(),
          confidence: pattern.confidence
        }
      }
    }

    return null
  }

  // 计算字段名相似性置信度
  private calculateNameSimilarityConfidence(name1: string, name2: string): number {
    if (name1 === name2) return 0.9
    
    const similarity = this.calculateStringSimilarity(name1.toLowerCase(), name2.toLowerCase())
    return Math.max(0.6, similarity * 0.8)
  }

  // 计算字符串相似性
  private calculateStringSimilarity(str1: string, str2: string): number {
    const longer = str1.length > str2.length ? str1 : str2
    const shorter = str1.length > str2.length ? str2 : str1
    
    if (longer.length === 0) return 1.0
    
    const editDistance = this.levenshteinDistance(longer, shorter)
    return (longer.length - editDistance) / longer.length
  }

  // 计算编辑距离
  private levenshteinDistance(str1: string, str2: string): number {
    const matrix = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(null))
    
    for (let i = 0; i <= str1.length; i++) matrix[0][i] = i
    for (let j = 0; j <= str2.length; j++) matrix[j][0] = j
    
    for (let j = 1; j <= str2.length; j++) {
      for (let i = 1; i <= str1.length; i++) {
        const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1
        matrix[j][i] = Math.min(
          matrix[j][i - 1] + 1,
          matrix[j - 1][i] + 1,
          matrix[j - 1][i - 1] + indicator
        )
      }
    }
    
    return matrix[str2.length][str1.length]
  }

  // 构建关系图谱
  buildRelationGraph(
    explicitRelations: Relation[],
    inferredRelations: InferredRelation[],
    tables: TableReference[]
  ): RelationGraph {
    const allRelations = [...explicitRelations, ...inferredRelations]
    const nodes: GraphNode[] = []
    const edges: GraphEdge[] = []

    // 创建表节点
    tables.forEach(table => {
      nodes.push({
        id: table.name,
        label: table.alias || table.name,
        type: 'table',
        metadata: {
          schema: table.schema,
          operations: table.operations,
          frequency: this.calculateTableFrequency(table.name, allRelations)
        }
      })
    })

    // 创建关系边
    allRelations.forEach(relation => {
      edges.push({
        id: relation.id,
        source: relation.from_table,
        target: relation.to_table,
        type: relation.relation_type,
        confidence: relation.confidence,
        metadata: {
          sql_sources: [relation.sql_source],
          join_conditions: [relation.join_condition || ''],
          frequency: 1
        },
        style: {
          color: this.getRelationColor(relation.relation_type, relation.confidence),
          width: Math.max(1, relation.confidence * 3),
          dashArray: 'inference_method' in relation ? '5,5' : undefined
        }
      })
    })

    return {
      nodes,
      edges,
      metadata: {
        total_nodes: nodes.length,
        total_edges: edges.length,
        confidence_distribution: this.calculateConfidenceDistribution(allRelations),
        relation_types: this.calculateRelationTypeDistribution(allRelations)
      }
    }
  }

  // 辅助方法
  private groupColumnsByTable(columns: ColumnReference[]): Record<string, ColumnReference[]> {
    return columns.reduce((acc, column) => {
      if (!acc[column.table]) acc[column.table] = []
      acc[column.table].push(column)
      return acc
    }, {} as Record<string, ColumnReference[]>)
  }

  private groupColumnsByName(columns: ColumnReference[]): Record<string, ColumnReference[]> {
    return columns.reduce((acc, column) => {
      if (!acc[column.name]) acc[column.name] = []
      acc[column.name].push(column)
      return acc
    }, {} as Record<string, ColumnReference[]>)
  }

  private tableExists(tableName: string, tables: TableReference[]): boolean {
    return tables.some(table => table.name.toLowerCase() === tableName.toLowerCase())
  }

  private calculateTableFrequency(tableName: string, relations: Relation[]): number {
    return relations.filter(rel => 
      rel.from_table === tableName || rel.to_table === tableName
    ).length
  }

  private getRelationColor(type: string, confidence: number): string {
    const baseColors = {
      'INNER_JOIN': '#2563eb',
      'LEFT_JOIN': '#059669',
      'RIGHT_JOIN': '#dc2626',
      'FULL_JOIN': '#7c3aed',
      'WHERE_CONDITION': '#ea580c'
    }
    
    const alpha = Math.max(0.3, confidence)
    return baseColors[type as keyof typeof baseColors] || '#6b7280'
  }

  private calculateConfidenceDistribution(relations: Relation[]): Record<string, number> {
    const distribution = { high: 0, medium: 0, low: 0 }
    
    relations.forEach(rel => {
      if (rel.confidence >= 0.8) distribution.high++
      else if (rel.confidence >= 0.6) distribution.medium++
      else distribution.low++
    })
    
    return distribution
  }

  private calculateRelationTypeDistribution(relations: Relation[]): Record<string, number> {
    return relations.reduce((acc, rel) => {
      acc[rel.relation_type] = (acc[rel.relation_type] || 0) + 1
      return acc
    }, {} as Record<string, number>)
  }

  private generateId(): string {
    return `rel_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }
}
