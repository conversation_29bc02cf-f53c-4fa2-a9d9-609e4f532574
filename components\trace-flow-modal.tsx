"use client"

import React from "react"
import { X, ArrowRight, CheckCircle, AlertCircle, Clock, Database, Code, Lightbulb } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import type { TraceFlowData, FlowStep } from "@/types/issue"

interface TraceFlowModalProps {
  isOpen: boolean
  onClose: () => void
  data: TraceFlowData | null
}

const TraceFlowModal: React.FC<TraceFlowModalProps> = ({ isOpen, onClose, data }) => {
  if (!isOpen || !data) return null

  const getStepIcon = (stepType: FlowStep['stepType']) => {
    switch (stepType) {
      case 'rule_trigger':
        return <AlertCircle className="h-5 w-5 text-red-500" />
      case 'logic_evaluation':
        return <Code className="h-5 w-5 text-blue-500" />
      case 'data_validation':
        return <Database className="h-5 w-5 text-green-500" />
      case 'clue_generation':
        return <Lightbulb className="h-5 w-5 text-yellow-500" />
      default:
        return <Clock className="h-5 w-5 text-gray-500" />
    }
  }

  const getStepColor = (stepType: FlowStep['stepType']) => {
    switch (stepType) {
      case 'rule_trigger':
        return 'border-red-200 bg-red-50'
      case 'logic_evaluation':
        return 'border-blue-200 bg-blue-50'
      case 'data_validation':
        return 'border-green-200 bg-green-50'
      case 'clue_generation':
        return 'border-yellow-200 bg-yellow-50'
      default:
        return 'border-gray-200 bg-gray-50'
    }
  }

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
      <div className="bg-white rounded-lg shadow-xl w-11/12 max-w-4xl max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <div>
            <h2 className="text-xl font-semibold">线索溯源流程图</h2>
            <p className="text-sm text-gray-600 mt-1">
              问题：{data.issue.title} | 线索：{data.selectedClue.title}
            </p>
          </div>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
          {/* 问题概览 */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="text-lg">问题概览</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm font-medium text-gray-600">问题标题</p>
                  <p className="text-sm">{data.issue.title}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-600">严重程度</p>
                  <Badge variant={data.issue.severity === 'critical' ? 'destructive' : 'secondary'}>
                    {data.issue.severity}
                  </Badge>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-600">创建时间</p>
                  <p className="text-sm">{data.issue.createdAt}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-600">状态</p>
                  <Badge variant={data.issue.status === 'active' ? 'destructive' : 'default'}>
                    {data.issue.status}
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 规则命中信息 */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="text-lg">规则命中信息</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm font-medium text-gray-600">规则名称</p>
                  <p className="text-sm">{data.selectedClue.ruleHit.ruleName}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-600">主题名称</p>
                  <p className="text-sm">{data.selectedClue.ruleHit.themeName}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-600">数据源</p>
                  <p className="text-sm">{data.selectedClue.ruleHit.dataSource}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-600">命中时间</p>
                  <p className="text-sm">{data.selectedClue.ruleHit.hitTime}</p>
                </div>
                <div className="col-span-2">
                  <p className="text-sm font-medium text-gray-600">命中条件</p>
                  <p className="text-sm bg-gray-100 p-2 rounded">{data.selectedClue.ruleHit.condition}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 逻辑步骤详情 */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="text-lg">逻辑步骤详情</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {data.selectedClue.ruleHit.logicSteps.map((step, index) => (
                  <div key={step.id} className="flex items-start space-x-3">
                    <div className="flex-shrink-0 w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center">
                      <span className="text-sm font-medium text-blue-600">{step.order}</span>
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-2">
                        <h4 className="font-medium">{step.stepName}</h4>
                        <Badge variant={step.isHit ? 'destructive' : 'secondary'}>
                          {step.isHit ? '命中' : '未命中'}
                        </Badge>
                        <Badge variant="outline">{step.logicType}</Badge>
                      </div>
                      <p className="text-sm text-gray-600 mb-2">{step.description}</p>
                      <div className="bg-gray-50 p-3 rounded">
                        <p className="text-sm font-medium text-gray-700 mb-1">参数：</p>
                        <pre className="text-xs text-gray-600 whitespace-pre-wrap">
                          {JSON.stringify(step.parameters, null, 2)}
                        </pre>
                        <p className="text-sm font-medium text-gray-700 mt-2 mb-1">结果：</p>
                        <p className="text-sm text-gray-600">{JSON.stringify(step.result)}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* 线索生成流程 */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">线索生成流程</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {data.flowSteps.map((step, index) => (
                  <div key={step.id}>
                    <div className={`flex items-center space-x-3 p-4 rounded-lg border ${getStepColor(step.stepType)}`}>
                      <div className="flex-shrink-0">
                        {getStepIcon(step.stepType)}
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-1">
                          <h4 className="font-medium">{step.title}</h4>
                          <Badge variant="outline">{step.stepType}</Badge>
                        </div>
                        <p className="text-sm text-gray-600 mb-2">{step.description}</p>
                        <p className="text-xs text-gray-500">{step.timestamp}</p>
                        {Object.keys(step.details).length > 0 && (
                          <div className="mt-2 bg-white p-2 rounded border">
                            <p className="text-xs font-medium text-gray-700 mb-1">详细信息：</p>
                            <pre className="text-xs text-gray-600 whitespace-pre-wrap">
                              {JSON.stringify(step.details, null, 2)}
                            </pre>
                          </div>
                        )}
                      </div>
                    </div>
                    {index < data.flowSteps.length - 1 && (
                      <div className="flex justify-center my-2">
                        <ArrowRight className="h-4 w-4 text-gray-400" />
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Footer */}
        <div className="flex justify-end p-6 border-t">
          <Button onClick={onClose}>关闭</Button>
        </div>
      </div>
    </div>
  )
}

export default TraceFlowModal 