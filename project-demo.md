<optimized_prompt>
<task>生成项目文档并分析产品设计需求</task>

<context>
一是生成当前项目的功能清单及摘要complete-list.md，二是参考todolist.md内容，根据product-requirements-description.md分析整理产品设计需求。本项目的目的是进行产品设计，纯静态页面的交互效果
</context>

<instructions>
1. 生成功能清单文档：
   - 创建complete-list.md文件
   - 列出当前项目的所有功能点
   - 为每个功能点编写简要说明
   - 标注已完成的todolist.md条目

2. 分析产品设计需求：
   - 阅读面向开发者和AI工具的todolist.md文件内容
   - 详细分析product-requirements-description.md文档
   - 整理产品设计需求要点
   - 区分已完成和未完成的需求

3. 确保输出符合项目目标：
   - 专注于纯静态页面的交互效果
   - 保持文档清晰简洁
   - 突出关键设计需求
   - 明确标记开发进度状态
</instructions>

<output_format>
1. complete-list.md文件应包含：
   - 功能标题
   - 功能描述
   - 交互效果说明
   - 完成状态标记

2. 产品设计需求分析应包含：
   - 需求分类
   - 优先级标注
   - 静态页面交互说明
   - 开发进度状态
</output_format>
</optimized_prompt>