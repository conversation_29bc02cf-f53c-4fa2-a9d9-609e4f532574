# 电力数据大脑 - 智能稽查系统

> 基于AI驱动的电力数据治理与营销稽查智能化平台，致力于将业务智慧转化为机器可执行规则，实现稽查自动化，变被动稽查为主动预警。

## 快速开始

```bash
# 安装依赖
pnpm install

# 开发环境启动
pnpm dev

# 构建生产版本
pnpm build

# 启动生产服务
pnpm start
```

## 项目概览

- **项目类型**: 企业级数据治理与智能稽查平台
- **架构模式**: 前端单页应用 + 微服务后端架构
- **核心技术**: Next.js 15 + React 19 + TypeScript + Tailwind CSS
- **UI组件库**: shadcn/ui + Radix UI
- **数据可视化**: D3.js + Recharts + Vis Network
- **构建工具**: Next.js App Router + Vite
- **部署方式**: 静态导出 + 容器化部署

## 系统架构

### 整体架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    前端应用层 (Next.js)                      │
├─────────────────────────────────────────────────────────────┤
│  数据大脑模块  │  营销稽查智能体  │  数据巡查模块  │  仪表盘  │
├─────────────────────────────────────────────────────────────┤
│                    组件库 (shadcn/ui)                       │
├─────────────────────────────────────────────────────────────┤
│                    状态管理 (React Hooks)                   │
├─────────────────────────────────────────────────────────────┤
│                    路由系统 (App Router)                    │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                    后端服务层 (微服务架构)                   │
├─────────────────────────────────────────────────────────────┤
│ 数据巡查服务 │ 稽查业务服务 │ 元数据服务 │ 语义服务 │ 规则引擎 │
├─────────────────────────────────────────────────────────────┤
│                    数据存储层                               │
├─────────────────────────────────────────────────────────────┤
│ MySQL │ Milvus │ Redis │ MinIO/OSS │ Kafka │ XXL-Job │
└─────────────────────────────────────────────────────────────┘
```

### 核心业务模块

#### 1. 数据大脑模块
- **元数据管理**: 数据资产概览、查询、同步管理
- **逻辑数据管理**: 语义层概览、语义原子管理、函数库管理
- **数据巡查**: 任务管理、命中问题管理

#### 2. 营销稽查智能体
- **稽查主题设计**: 自然语言/可视化双模式规则生成
- **政策文件处理**: AI驱动的要素提取和规则池生成
- **规则管理**: 生命周期管理、审批流程、版本控制
- **知识分析**: 智能检索、规则分析、建议回流

#### 3. 数据巡查模块
- **MaxCompute DI任务管理**: 数据集成任务配置和监控
- **XXL-Job定时调度**: 灵活的定时巡查任务调度
- **数据质量监控**: 异常检测、质量报告、告警通知

## 目录结构

```
datamind-web/
├── app/                                    # Next.js App Router 页面
│   ├── dashboard/                          # 仪表盘模块
│   ├── audit-knowledge-management/         # 稽查知识管理
│   ├── audit-scenario-analysis/            # 稽查场景分析
│   ├── audit-theme-association/            # 稽查主题要素关联
│   ├── audit-theme-execution-analysis/     # 稽查主题运行分析
│   ├── audit-theme-intelligence-analysis/  # 稽查主题智能分析
│   ├── audit-theme-intelligent-analysis/   # 稽查主题智能分析
│   ├── audit-work-order-inspection/        # 稽查工单质检
│   ├── data-inspection/                    # 数据巡查
│   ├── data-source-management/             # 数据源管理
│   ├── inspection-rule-iteration/          # 规则迭代
│   ├── issue-hits/                         # 问题命中管理
│   ├── knowledge-analysis/                 # 知识分析
│   ├── metadata-overview/                  # 元数据概览
│   ├── metadata-query/                     # 元数据查询
│   ├── metadata-script-extract/            # 脚本元数据提取
│   ├── metadata-sync/                      # 元数据同步
│   ├── policy-file-processing/             # 政策文件处理
│   ├── problem-penetration-analysis/       # 问题穿透分析
│   ├── problem-report/                     # 问题报告
│   ├── rule-generator/                     # 规则生成器
│   ├── rule-management/                    # 规则管理
│   ├── rule-pool/                          # 规则池
│   ├── rule-template-management/           # 规则模板管理
│   ├── semantic-atoms/                     # 语义原子管理
│   ├── semantic-functions/                 # 语义函数管理
│   ├── semantic-overview/                  # 语义层概览
│   ├── layout.tsx                          # 根布局
│   ├── page.tsx                            # 首页
│   └── globals.css                         # 全局样式
├── components/                             # 可复用组件
│   ├── layout/                             # 布局组件
│   │   ├── header.tsx                      # 顶部导航
│   │   ├── main-layout.tsx                 # 主布局
│   │   └── sidebar.tsx                     # 侧边栏导航
│   ├── theme-provider.tsx                  # 主题提供者
│   ├── trace-flow-modal.tsx                # 溯源流程图模态框
│   └── ui/                                 # UI组件库
│       ├── accordion.tsx                   # 手风琴组件
│       ├── alert-dialog.tsx                # 警告对话框
│       ├── button.tsx                      # 按钮组件
│       ├── card.tsx                        # 卡片组件
│       ├── chart.tsx                       # 图表组件
│       ├── dialog.tsx                      # 对话框
│       ├── form.tsx                        # 表单组件
│       ├── input.tsx                       # 输入框
│       ├── select.tsx                      # 选择器
│       ├── table.tsx                       # 表格组件
│       ├── tabs.tsx                        # 标签页
│       └── ...                             # 其他UI组件
├── hooks/                                  # 自定义Hooks
│   ├── use-mobile.tsx                      # 移动端检测
│   └── use-toast.ts                        # 消息提示
├── lib/                                    # 工具库
│   └── utils.ts                            # 通用工具函数
├── types/                                  # TypeScript类型定义
│   └── issue.ts                            # 问题相关类型
├── utils/                                  # 业务工具
│   └── draftRule.ts                        # 规则草稿工具
├── public/                                 # 静态资源
├── styles/                                 # 样式文件
├── package.json                            # 项目配置
├── next.config.mjs                         # Next.js配置
├── tailwind.config.ts                      # Tailwind配置
├── tsconfig.json                           # TypeScript配置
└── components.json                         # shadcn/ui配置
```

## 核心功能详解

### 1. 稽查主题智能化设计

**功能描述**: 支持自然语言与可视化拖拽两种规则创建模式，实现业务规则到可执行逻辑的智能转换。

**技术实现**:
- **自然语言处理**: 集成LLM进行NL2SQL转换
- **可视化编排**: 基于D3.js的拖拽式规则构建
- **实时预览**: 动态生成SQL脚本和逻辑流程图
- **规则验证**: 支持规则逻辑验证和样本测试

**关键组件**:
- `app/rule-generator/`: 规则生成器主模块
- `Step0IntentGuide.tsx`: 意图引导步骤
- `Step1TemplateSelect.tsx`: 模板选择步骤
- `Step2SemanticAnalyze.tsx`: 语义分析步骤
- `Step3SQLConfirm.tsx`: SQL确认步骤

### 2. 政策文件智能解析

**功能描述**: 基于AI技术从政策文件中自动提取稽查要素，生成结构化规则池。

**技术实现**:
- **多格式支持**: PDF、DOC、DOCX文件解析
- **AI要素提取**: 使用NLP技术识别关键要素
- **规则池生成**: 自动生成可复用的规则模板
- **置信度评估**: 为提取结果提供可信度评分

**关键组件**:
- `app/policy-file-processing/`: 政策文件处理模块
- `app/audit-knowledge-management/`: 知识管理模块

### 3. 数据巡查与质量监控

**功能描述**: 全面的数据质量监控和异常检测系统。

**技术实现**:
- **MaxCompute集成**: 阿里云数据集成任务管理
- **XXL-Job调度**: 灵活的定时任务调度
- **质量规则引擎**: 可配置的数据质量检查规则
- **实时监控**: 异常检测和告警通知

**关键组件**:
- `app/data-inspection/`: 数据巡查主模块
- `app/issue-hits/`: 问题命中管理

### 4. 元数据管理

**功能描述**: 统一的数据资产管理和语义层构建。

**技术实现**:
- **元数据同步**: 自动从数据源同步元数据
- **语义原子**: 构建业务语义的最小单元
- **血缘关系**: 追踪数据流向和依赖关系
- **智能搜索**: 基于语义的元数据检索

**关键组件**:
- `app/metadata-overview/`: 元数据概览
- `app/metadata-query/`: 元数据查询
- `app/semantic-atoms/`: 语义原子管理

## 重要文件说明

### 核心配置文件

- **`package.json`**: 项目依赖和脚本配置
  - 核心依赖: Next.js 15.2.4, React 19, TypeScript 5
  - UI组件: shadcn/ui, Radix UI, Tailwind CSS
  - 可视化: D3.js, Recharts, Vis Network
  - 表单处理: React Hook Form, Zod验证

- **`next.config.mjs`**: Next.js构建配置
  - 静态导出配置: `output: 'export'`
  - 资源处理: 图片和静态文件配置
  - 构建优化: 代码分割和性能优化

- **`tailwind.config.ts`**: 样式系统配置
  - 主题定制: 颜色、字体、间距
  - 组件样式: shadcn/ui集成
  - 响应式设计: 断点配置

### 核心业务文件

- **`components/layout/sidebar.tsx`**: 主导航组件
  - 三级菜单结构: 数据大脑/营销稽查智能体
  - 权限控制: MVP功能标记和状态管理
  - 响应式设计: 移动端适配

- **`app/rule-generator/`**: 规则生成器核心模块
  - 多步骤流程: 意图→模板→分析→确认
  - 智能转换: NL2SQL和可视化编排
  - 实时预览: 动态生成和验证

- **`app/audit-work-order-inspection/`**: 工单质检模块
  - 多模态质检: 文本、图像、文档、音频
  - 智能分析: LLM驱动的质检逻辑
  - 结果管理: 质检报告和统计分析

### 工具和辅助文件

- **`lib/utils.ts`**: 通用工具函数
  - 样式合并: `cn()`函数
  - 类型工具: TypeScript辅助函数
  - 格式化: 日期、数字格式化

- **`utils/draftRule.ts`**: 规则草稿工具
  - 规则状态管理: 草稿、审核、发布
  - 版本控制: 规则版本管理
  - 数据持久化: 本地存储和同步

## 开发命令

### 环境管理
```bash
# 检查Node.js版本 (要求18+)
node --version

# 安装依赖
pnpm install

# 清理依赖缓存
pnpm store prune
```

### 开发调试
```bash
# 开发环境启动 (http://localhost:3000)
pnpm dev

# 指定端口启动
pnpm dev --port 3001

# 生产环境预览
pnpm build && pnpm start
```

### 代码质量
```bash
# TypeScript类型检查
pnpm tsc --noEmit

# ESLint代码检查
pnpm lint

# 代码格式化 (如果配置了prettier)
pnpm format
```

### 构建部署
```bash
# 生产构建
pnpm build

# 静态文件导出
pnpm export

# 启动生产服务
pnpm start
```

### 依赖管理
```bash
# 添加新依赖
pnpm add package-name

# 添加开发依赖
pnpm add -D package-name

# 更新依赖
pnpm update

# 查看依赖树
pnpm list
```

## 开发环境设置

### 系统要求

- **操作系统**: Windows 10+, macOS 10.15+, Ubuntu 18.04+
- **Node.js**: 18.0.0 或更高版本
- **包管理器**: pnpm (推荐) 或 npm
- **内存**: 至少 8GB RAM
- **存储**: 至少 2GB 可用空间

### 安装步骤

1. **基础环境准备**
   ```bash
   # 安装Node.js (推荐使用nvm管理版本)
   nvm install 18
   nvm use 18
   
   # 安装pnpm
   npm install -g pnpm
   ```

2. **项目依赖安装**
   ```bash
   # 克隆项目
   git clone <repository-url>
   cd datamind-web
   
   # 安装依赖
   pnpm install
   ```

3. **环境变量配置**
   ```bash
   # 复制环境变量模板
   cp .env.example .env.local
   
   # 编辑环境变量
   # NEXT_PUBLIC_API_URL=http://localhost:8080
   # NEXT_PUBLIC_APP_NAME=电力数据大脑
   ```

4. **启动开发环境**
   ```bash
   # 启动开发服务器
   pnpm dev
   
   # 访问应用
   open http://localhost:3000
   ```

### 环境变量配置

| 变量名 | 说明 | 默认值 | 必需 |
|--------|------|--------|------|
| `NEXT_PUBLIC_API_URL` | 后端API地址 | `http://localhost:8080` | 是 |
| `NEXT_PUBLIC_APP_NAME` | 应用名称 | `电力数据大脑` | 否 |
| `NEXT_PUBLIC_ENV` | 环境标识 | `development` | 否 |
| `NEXT_PUBLIC_VERSION` | 应用版本 | `0.1.0` | 否 |

### 常见安装问题

| 问题 | 原因 | 解决方案 |
|------|------|----------|
| 依赖安装失败 | 网络问题或Node.js版本不兼容 | 使用国内镜像: `pnpm config set registry https://registry.npmmirror.com` |
| 端口被占用 | 3000端口已被其他应用占用 | 使用其他端口: `pnpm dev --port 3001` |
| 构建失败 | TypeScript类型错误 | 检查类型定义: `pnpm tsc --noEmit` |
| 样式不生效 | Tailwind CSS配置问题 | 检查`tailwind.config.ts`配置 |

## 测试说明

### 测试策略

- **测试金字塔**: 单元测试 > 集成测试 > E2E测试
- **覆盖率要求**: 核心业务逻辑覆盖率 > 80%
- **测试环境**: 独立的测试数据库和模拟服务

### 测试结构

```
tests/
├── unit/                    # 单元测试
│   ├── components/          # 组件测试
│   ├── utils/              # 工具函数测试
│   └── hooks/              # Hooks测试
├── integration/            # 集成测试
│   ├── api/                # API集成测试
│   └── pages/              # 页面集成测试
├── e2e/                    # 端到端测试
│   ├── workflows/          # 业务流程测试
│   └── scenarios/          # 场景测试
├── fixtures/               # 测试数据
└── helpers/                # 测试工具
```

### 测试类型详解

#### 单元测试
- **文件位置**: `tests/unit/`
- **命名规范**: `*.test.ts` 或 `*.spec.ts`
- **运行命令**: `pnpm test:unit`
- **Mock策略**: 使用Jest Mock模拟外部依赖

#### 集成测试
- **测试范围**: 组件间交互、API调用
- **数据准备**: 使用测试数据库和模拟数据
- **运行命令**: `pnpm test:integration`
- **环境要求**: 独立的测试环境

#### 端到端测试
- **测试工具**: Playwright 或 Cypress
- **浏览器配置**: Chrome, Firefox, Safari
- **运行命令**: `pnpm test:e2e`
- **测试数据**: 完整的业务流程数据

### 测试数据管理

- **测试数据库**: 独立的测试数据库实例
- **数据工厂**: 使用工厂模式生成测试数据
- **数据清理**: 每个测试后自动清理数据
- **Mock服务**: 模拟外部API和第三方服务

### 测试最佳实践

1. **测试隔离**: 每个测试独立运行，不依赖其他测试
2. **数据驱动**: 使用参数化测试覆盖多种场景
3. **断言清晰**: 明确的断言消息和错误提示
4. **性能测试**: 关键路径的性能基准测试
5. **安全测试**: 输入验证和安全漏洞测试

## 开发指南

### 编码规范

#### 命名约定
- **文件命名**: 使用kebab-case (如: `rule-generator.tsx`)
- **组件命名**: 使用PascalCase (如: `RuleGenerator`)
- **函数命名**: 使用camelCase (如: `generateRule`)
- **常量命名**: 使用UPPER_SNAKE_CASE (如: `API_BASE_URL`)

#### 文件组织
- **按功能分组**: 相关功能放在同一目录
- **组件分离**: 每个组件一个文件
- **类型定义**: 集中管理TypeScript类型
- **工具函数**: 按功能模块组织工具函数

#### API设计
- **RESTful风格**: 遵循REST API设计原则
- **统一响应格式**: 标准化的API响应结构
- **错误处理**: 统一的错误码和错误信息
- **版本控制**: API版本管理策略

#### 错误处理
- **前端验证**: 表单验证和用户输入检查
- **错误边界**: React错误边界处理组件错误
- **用户反馈**: 友好的错误提示和恢复建议
- **日志记录**: 错误日志记录和监控

### Git工作流

1. **分支策略**
   - `main`: 主分支，生产环境代码
   - `develop`: 开发分支，集成测试
   - `feature/*`: 功能分支，新功能开发
   - `hotfix/*`: 热修复分支，紧急修复

2. **提交信息规范**
   ```
   type(scope): description
   
   feat: 新功能
   fix: 修复bug
   docs: 文档更新
   style: 代码格式调整
   refactor: 代码重构
   test: 测试相关
   chore: 构建工具或辅助工具的变动
   ```

3. **代码审查流程**
   - 创建Pull Request
   - 代码审查和讨论
   - 自动化测试通过
   - 合并到目标分支

4. **合并策略**
   - 使用Squash Merge保持提交历史清晰
   - 删除功能分支保持仓库整洁
   - 添加适当的标签和里程碑

### 开发工作流

1. **需求分析阶段**
   - 理解业务需求和技术要求
   - 设计数据模型和API接口
   - 制定开发计划和测试策略

2. **设计阶段**
   - 组件设计和状态管理
   - 用户界面和交互设计
   - 性能优化和安全考虑

3. **开发阶段**
   - 遵循编码规范和最佳实践
   - 编写单元测试和集成测试
   - 代码审查和质量保证

4. **测试阶段**
   - 功能测试和回归测试
   - 性能测试和压力测试
   - 安全测试和兼容性测试

5. **发布阶段**
   - 构建和部署准备
   - 生产环境验证
   - 监控和告警配置

## 部署指南

### 环境要求

- **硬件要求**: 
  - CPU: 4核心以上
  - 内存: 8GB以上
  - 存储: 50GB以上可用空间
- **软件依赖**: 
  - Node.js 18+
  - Nginx 1.18+
  - Docker 20.10+ (可选)
- **网络配置**: 
  - 端口: 80, 443 (HTTPS)
  - 域名: 配置SSL证书

### 部署架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Nginx反向代理  │    │   CDN加速       │    │   监控告警      │
│   (负载均衡)     │    │   (静态资源)    │    │   (Prometheus)  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端应用      │    │   后端API服务   │    │   数据库集群    │
│   (Next.js)     │    │   (微服务)      │    │   (MySQL+Redis) │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 部署流程

1. **构建准备**
   ```bash
   # 安装依赖
   pnpm install
   
   # 环境变量配置
   cp .env.example .env.production
   
   # 生产构建
   pnpm build
   ```

2. **服务部署**
   ```bash
   # 使用PM2部署
   pm2 start ecosystem.config.js
   
   # 或使用Docker部署
   docker build -t datamind-web .
   docker run -d -p 3000:3000 datamind-web
   ```

3. **配置更新**
   ```bash
   # Nginx配置
   sudo cp nginx.conf /etc/nginx/sites-available/datamind-web
   sudo ln -s /etc/nginx/sites-available/datamind-web /etc/nginx/sites-enabled/
   sudo nginx -t && sudo systemctl reload nginx
   ```

4. **健康检查**
   ```bash
   # 检查服务状态
   curl -f http://localhost:3000/api/health
   
   # 检查日志
   pm2 logs datamind-web
   ```

5. **流量切换**
   ```bash
   # 蓝绿部署切换
   # 或使用负载均衡器健康检查
   ```

### 监控告警

- **系统监控**: CPU、内存、磁盘使用率
- **应用监控**: 响应时间、错误率、吞吐量
- **日志收集**: 集中化日志管理和分析
- **告警规则**: 关键指标异常告警

### 备份恢复

- **数据备份**: 定期备份数据库和配置文件
- **代码备份**: 版本控制和代码仓库备份
- **恢复策略**: 快速恢复和灾难恢复计划
- **测试验证**: 定期测试备份恢复流程

## 故障排查

### 常见问题

| 问题类型 | 症状 | 可能原因 | 解决方案 |
|---------|------|---------|---------|
| 页面加载慢 | 首屏加载时间超过3秒 | 资源过大、网络延迟 | 代码分割、CDN加速、图片优化 |
| 组件渲染错误 | 页面显示错误或空白 | 组件状态异常、数据格式错误 | 检查组件逻辑、添加错误边界 |
| API调用失败 | 接口返回错误或超时 | 网络问题、服务异常 | 检查网络连接、查看服务日志 |
| 样式显示异常 | 界面布局错乱 | CSS冲突、响应式问题 | 检查样式文件、调试响应式断点 |
| 构建失败 | 编译错误或构建中断 | 依赖冲突、配置错误 | 清理缓存、检查依赖版本 |

### 调试工具

- **浏览器开发者工具**: 网络、控制台、性能分析
- **React DevTools**: 组件状态和性能分析
- **Next.js调试**: 开发模式调试和错误追踪
- **性能分析**: Lighthouse、WebPageTest

### 日志分析

- **前端日志**: 浏览器控制台和错误监控
- **构建日志**: 构建过程中的错误和警告
- **部署日志**: 部署过程中的状态和错误
- **性能日志**: 性能指标和瓶颈分析

### 应急处理

1. **服务降级**: 关闭非核心功能，保证基本服务
2. **回滚部署**: 快速回滚到上一个稳定版本
3. **扩容处理**: 增加服务器资源应对高负载
4. **数据恢复**: 从备份恢复数据，修复数据损坏

## 性能优化

### 前端优化

- **代码分割**: 按路由和组件分割代码
- **懒加载**: 图片和组件的懒加载
- **缓存策略**: 静态资源缓存和API缓存
- **压缩优化**: 代码压缩和资源优化

### 构建优化

- **Tree Shaking**: 移除未使用的代码
- **Bundle分析**: 分析打包体积和依赖
- **并行构建**: 多进程构建加速
- **增量构建**: 只构建变更的文件

### 运行时优化

- **虚拟滚动**: 大数据列表的性能优化
- **防抖节流**: 用户输入的性能优化
- **内存管理**: 避免内存泄漏和优化内存使用
- **状态管理**: 合理使用状态和避免不必要的重渲染

## 安全规范

### 前端安全

- **输入验证**: 客户端和服务器端双重验证
- **XSS防护**: 内容安全策略和输入过滤
- **CSRF防护**: 跨站请求伪造防护
- **敏感信息**: 避免在前端存储敏感信息

### 数据安全

- **传输加密**: HTTPS协议和API加密
- **存储加密**: 敏感数据加密存储
- **访问控制**: 基于角色的权限控制
- **审计日志**: 操作日志记录和审计

### 依赖安全

- **依赖扫描**: 定期扫描安全漏洞
- **版本管理**: 及时更新安全补丁
- **许可证检查**: 检查依赖许可证合规性
- **供应链安全**: 验证依赖来源和完整性

## 扩展性设计

### 架构扩展

- **微服务化**: 按业务模块拆分服务
- **容器化**: Docker容器化部署
- **云原生**: Kubernetes编排和管理
- **服务网格**: Istio服务治理

### 功能扩展

- **插件系统**: 支持第三方插件扩展
- **API开放**: 提供开放API接口
- **自定义配置**: 支持用户自定义配置
- **多租户**: 支持多租户架构

### 技术扩展

- **AI集成**: 更多AI模型和算法集成
- **大数据**: 大数据处理和分析能力
- **实时计算**: 流式计算和实时分析
- **边缘计算**: 边缘节点部署和计算

## 总结

电力数据大脑是一个功能完整、技术先进的企业级数据治理与智能稽查平台。项目采用现代化的技术栈和架构设计，具有良好的可扩展性、可维护性和用户体验。

### 项目亮点

1. **技术先进**: 使用Next.js 15、React 19、TypeScript等最新技术
2. **架构清晰**: 模块化设计和组件化开发
3. **功能完整**: 覆盖数据治理和智能稽查全流程
4. **用户体验**: 直观友好的界面和流畅的交互
5. **扩展性强**: 支持功能扩展和技术升级
6. **安全可靠**: 完善的安全机制和错误处理

### 发展前景

随着AI技术的发展和数字化转型的深入，电力数据大脑将在以下方面持续发展：

1. **智能化升级**: 更先进的AI算法和模型集成
2. **场景扩展**: 支持更多业务场景和行业应用
3. **平台化发展**: 构建开放的数据智能平台生态
4. **国际化**: 支持多语言和国际化部署

---

*本文档持续更新，如有疑问或建议，请联系开发团队。* 