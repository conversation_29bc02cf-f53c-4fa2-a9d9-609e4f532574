"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import SpotCheckForm from "./SpotCheckForm";

interface SpotCheckItem {
  id: string;
  workOrderId: string;
  title: string;
  type: string;
  riskScore: number;
  status: "pending" | "completed";
  reviewResult?: "pass" | "fail";
  reviewedAt?: string;
  reviewer?: string;
}

const mockSpotChecks: SpotCheckItem[] = [
  {
    id: "SC-2024-001",
    workOrderId: "WO-2024-001",
    title: "系统异常访问审计",
    type: "系统安全",
    riskScore: 85,
    status: "completed",
    reviewResult: "pass",
    reviewedAt: "2024-03-20",
    reviewer: "张三",
  },
  {
    id: "SC-2024-002",
    workOrderId: "WO-2024-002",
    title: "数据泄露风险排查",
    type: "数据安全",
    riskScore: 92,
    status: "pending",
  },
];

export default function SpotCheck() {
  const [spotChecks, setSpotChecks] = useState<SpotCheckItem[]>(mockSpotChecks);
  const [selectedItem, setSelectedItem] = useState<SpotCheckItem | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const handleReview = (item: SpotCheckItem) => {
    setSelectedItem(item);
    setIsDialogOpen(true);
  };

  const handleReviewSubmit = (result: { reviewResult: "pass" | "fail"; comments: string }) => {
    if (selectedItem) {
      const updatedSpotChecks = spotChecks.map((check) =>
        check.id === selectedItem.id
          ? {
              ...check,
              status: "completed" as const,
              reviewResult: result.reviewResult,
              reviewedAt: new Date().toISOString().split("T")[0],
              reviewer: "当前用户",
            }
          : check
      );
      setSpotChecks(updatedSpotChecks);
      setIsDialogOpen(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold">工单抽检列表</h2>
        <Button>生成抽检计划</Button>
      </div>

      <Card>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>抽检编号</TableHead>
              <TableHead>工单编号</TableHead>
              <TableHead>标题</TableHead>
              <TableHead>类型</TableHead>
              <TableHead>风险分数</TableHead>
              <TableHead>状态</TableHead>
              <TableHead>审核结果</TableHead>
              <TableHead>审核时间</TableHead>
              <TableHead>审核人</TableHead>
              <TableHead>操作</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {spotChecks.map((item) => (
              <TableRow key={item.id}>
                <TableCell>{item.id}</TableCell>
                <TableCell>{item.workOrderId}</TableCell>
                <TableCell>{item.title}</TableCell>
                <TableCell>{item.type}</TableCell>
                <TableCell>
                  <Badge variant={item.riskScore >= 90 ? "destructive" : "outline"}>
                    {item.riskScore}
                  </Badge>
                </TableCell>
                <TableCell>
                  <Badge variant={item.status === "completed" ? "outline" : "secondary"}>
                    {item.status === "completed" ? "已完成" : "待审核"}
                  </Badge>
                </TableCell>
                <TableCell>
                  {item.reviewResult && (
                    <Badge variant={item.reviewResult === "pass" ? "outline" : "destructive"}>
                      {item.reviewResult === "pass" ? "通过" : "不通过"}
                    </Badge>
                  )}
                </TableCell>
                <TableCell>{item.reviewedAt || "-"}</TableCell>
                <TableCell>{item.reviewer || "-"}</TableCell>
                <TableCell>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleReview(item)}
                    disabled={item.status === "completed"}
                  >
                    {item.status === "completed" ? "查看" : "审核"}
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </Card>

      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>抽检审核</DialogTitle>
          </DialogHeader>
          {selectedItem && (
            <SpotCheckForm spotCheck={selectedItem} onSubmit={handleReviewSubmit} />
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
} 