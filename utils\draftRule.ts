// 稽查主题规则草稿本地存储工具
export interface DraftRule {
  id?: number;
  status?: string;
  [key: string]: any;
}

export function saveDraftRule(rule: DraftRule) {
  const drafts = JSON.parse(localStorage.getItem('inspectionDrafts') || '[]');
  // 只有当规则没有ID时才生成新ID
  const newRule = { 
    ...rule, 
    id: rule.id || Date.now(), 
    status: rule.status || '已起草' 
  };
  drafts.push(newRule);
  localStorage.setItem('inspectionDrafts', JSON.stringify(drafts));
}

export function getDraftRules(): DraftRule[] {
  return JSON.parse(localStorage.getItem('inspectionDrafts') || '[]');
} 