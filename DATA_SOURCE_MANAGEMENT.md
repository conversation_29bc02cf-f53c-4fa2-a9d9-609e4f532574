# 数据源管理 (Data Source Management)

## 功能概述

数据源管理是稽查知识管理系统的前置条件/集成点，用于定义和管理非结构化/半结构化数据的来源，从中提取信息用于稽查要素分析。

## 主要功能

### 1. 数据源配置
- **支持多种数据源类型**：
  - PDF文档
  - Word文档  
  - 文本文件 (TXT)
  - 网页链接 (URL)
  - 数据库连接
  - API接口

### 2. 数据源管理操作
- **添加数据源**：配置新的数据源，包括名称、类型、描述、路径/URL、标签等
- **编辑数据源**：修改现有数据源的配置信息
- **删除数据源**：移除不需要的数据源
- **批量操作**：支持批量同步、删除等操作

### 3. 数据源状态管理
- **Active**：活跃状态，正常使用
- **Inactive**：停用状态，暂停使用
- **Error**：错误状态，需要处理
- **Processing**：处理中状态，正在同步或处理

### 4. 搜索和过滤
- **搜索功能**：按数据源名称或描述搜索
- **类型过滤**：按数据源类型过滤
- **状态过滤**：按数据源状态过滤

## 用户界面设计

### 1. 一致性 (Consistency)
- 保持与平台整体设计原则一致的用户界面和交互模式
- 使用统一的组件库和设计语言
- 遵循现有的布局和导航模式

### 2. 清晰性 (Clarity)
- 清晰的标签和直观的导航
- 简洁明了的数据展示，特别是复杂比较
- 直观的操作按钮和状态指示

### 3. 反馈 (Feedback)
- 清晰的任务状态视觉反馈（如颜色编码标签）
- 加载状态和表单验证反馈
- 操作成功/失败的明确提示

### 4. 易用性 (Usability)
- 设计便于使用，让稽查人员能够快速找到、理解、管理和主动响应稽查相关信息
- 支持快速搜索和过滤
- 提供批量操作功能提高效率

## 技术实现

### 1. 页面结构
```
app/data-source-management/
├── page.tsx              # 页面入口
├── main.tsx              # 主要组件
└── loading.tsx           # 加载状态组件
```

### 2. 核心组件
- **DataSourceManagement**：主要管理组件
- **数据源列表**：表格形式展示所有数据源
- **添加/编辑对话框**：数据源配置表单
- **搜索和过滤**：数据源查找和筛选
- **批量操作**：多选操作功能

### 3. 数据模型
```typescript
interface DataSource {
  id: string
  name: string
  type: 'PDF' | 'Word' | 'TXT' | 'URL' | 'Database' | 'API'
  description: string
  url?: string
  filePath?: string
  status: 'Active' | 'Inactive' | 'Error' | 'Processing'
  lastUpdate: string
  lastSync: string
  tags: string[]
  metadata?: {
    size?: string
    pages?: number
    encoding?: string
    lastModified?: string
  }
}
```

## 使用场景

### 1. 政策文件管理
- 上传和管理PDF格式的政策文件
- 配置Word文档作为数据源
- 管理文本格式的法规文件

### 2. 外部数据集成
- 配置政府网站URL作为数据源
- 集成第三方API接口
- 连接外部数据库

### 3. 稽查案例库
- 管理历史稽查案例数据库
- 配置案例文档存储路径
- 同步最新的稽查案例

## 后续扩展

### 1. 数据源监控
- 实时监控数据源状态
- 自动检测数据源可用性
- 异常情况告警

### 2. 数据同步
- 自动同步数据源内容
- 增量更新机制
- 同步历史记录

### 3. 权限管理
- 数据源访问权限控制
- 用户角色管理
- 操作日志记录

## 访问路径

数据源管理页面可通过以下路径访问：
- 侧边栏菜单：营销稽查主题规则智能化应用 > 数据源管理
- 直接URL：`/data-source-management` 