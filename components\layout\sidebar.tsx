"use client"

import { useState } from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Badge } from "@/components/ui/badge"
import {
  Database,
  GitBranch,
  FileBarChart,
  Bot,
  Wand2,
  Palette,
  List,
  AlertTriangle,
  History,
  TrendingUp,
  Settings,
  Users,
  FileText,
  ChevronDown,
  ChevronRight,
  Menu,
  X,
  Network,
  Search,
  BookOpen,
  Link as LinkIcon,
  Target,
  Activity,
} from "lucide-react"

interface MenuItem {
  id: string
  name: string
  path?: string
  icon: any
  enabled: boolean
  isMvp?: boolean
  futureFeature?: boolean
  children?: MenuItem[]
}

interface MenuGroup {
  id: string
  name: string
  icon: any
  children: MenuItem[]
}

const menuConfig: MenuGroup[] = [
  {
    id: 'datamind',
    name: '数据大脑',
    icon: Database,
    children: [
      {
        id: 'metadata-management',
        name: '元数据管理',
        icon: Database,
        enabled: true,
        isMvp: true,
        children: [
          {
            id: 'metadata-overview',
            name: '元数据概览',
            path: '/metadata-overview',
            icon: Database,
            enabled: true,
            isMvp: true,
          },
          {
            id: 'metadata-query',
            name: '元数据查询',
            path: '/metadata-query',
            icon: Search,
            enabled: true,
            isMvp: true,
          },
          {
            id: 'metadata-sync',
            name: '元数据同步管理',
            path: '/metadata-sync',
            icon: GitBranch,
            enabled: true,
            isMvp: true,
          },
          {
            id: 'metadata-script-extract',
            name: 'SQL脚本解析增强',
            path: '/metadata-script-extract',
            icon: FileText,
            enabled: true,
            isMvp: true,
          },
          {
            id: 'data-dictionary-management',
            name: '数据字典管理',
            path: '/data-dictionary-management',
            icon: BookOpen,
            enabled: true,
            isMvp: true,
          },
          {
            id: 'data-lineage-analysis',
            name: '数据血缘溯源',
            path: '/data-lineage-analysis',
            icon: GitBranch,
            enabled: true,
            isMvp: true,
          },
        ],
      },
      {
        id: 'logic-management',
        name: '逻辑数据管理',
        icon: Network,
        enabled: true,
        isMvp: true,
        children: [
          {
            id: 'semantic-overview',
            name: '语义层概览',
            path: '/semantic-overview',
            icon: Network,
            enabled: true,
            isMvp: true,
          },
          {
            id: 'semantic-atoms',
            name: '语义逻辑原子管理',
            path: '/semantic-atoms',
            icon: Network,
            enabled: true,
            isMvp: true,
          },
          {
            id: 'semantic-functions',
            name: '语义逻辑函数库管理',
            path: '/semantic-functions',
            icon: BookOpen,
            enabled: true,
            isMvp: true,
          },
        ],
      },
      {
        id: 'data-inspection',
        name: '数据巡查',
        icon: History,
        enabled: true,
        isMvp: true,
        children: [
          {
            id: 'inspection-task-management',
            name: '巡查任务管理',
            path: '/data-inspection',
            icon: List,
            enabled: true,
            isMvp: true,
          },
          {
            id: 'inspection-issue-hits-management',
            name: '巡查命中问题管理',
            path: '/issue-hits',
            icon: AlertTriangle,
            enabled: true,
            isMvp: true,
          },
        ],
      },
    ],
  },
  {
    id: 'audit-agent',
    name: '营销稽查主题规则智能化应用',
    icon: Bot,
    children: [
      {
        id: 'rule-template-management',
        name: '稽查主题设计模板',
        path: '/rule-template-management',
        icon: FileText,
        enabled: true,
        isMvp: true,
      },
      {
        id: 'rule-generator',
        name: '稽查主题智能化设计',
        path: '/rule-generator',
        icon: Wand2,
        enabled: true,
        isMvp: true,
      },
      {
        id: 'inspection-rule-iteration',
        name: '稽查主题规则迭代',
        path: '/inspection-rule-iteration',
        icon: List,
        enabled: true,
        isMvp: true,
      },
      // {
      //   id: 'policy-file',
      //   name: '政策文件处理与稽查要素提取',
      //   path: '/policy-file-processing',
      //   icon: FileText,
      //   enabled: true,
      //   isMvp: true,
      // },
      // {
      //   id: 'rule-management',
      //   name: '稽查主题规则管理',
      //   path: '/rule-management',
      //   icon: List,
      //   enabled: true,
      //   isMvp: true,
      // },
      // {
      //   id: 'audit-knowledge-management',
      //   name: '稽查知识管理',
      //   path: '/audit-knowledge-management',
      //   icon: TrendingUp,
      //   enabled: true,
      //   isMvp: true,
      // },
      {
        id: 'data-source-management',
        name: '稽查知识源管理',
        path: '/data-source-management',
        icon: Database,
        enabled: true,
        isMvp: true,
      },
      {
        id: 'audit-knowledge-management',
        name: '稽查知识要素管理',
        path: '/audit-knowledge-management',
        icon: BookOpen,
        enabled: true,
        isMvp: true,
      },
      // {
      //   id: 'audit-theme-association',
      //   name: '稽查主题要素关联',
      //   path: '/audit-theme-association',
      //   icon: LinkIcon,
      //   enabled: true,
      //   isMvp: true,
      // },
      // {
      //   id: 'audit-theme-intelligence-analysis',
      //   name: '稽查主题要素智能分析',
      //   path: '/audit-theme-intelligence-analysis',
      //   icon: Target,
      //   enabled: true,
      //   isMvp: true,
      // },
      {
        id: 'audit-theme-intelligent-analysis',
        name: '稽查主题要素智能化分析',
        path: '/audit-theme-intelligent-analysis',
        icon: Target,
        enabled: true,
        isMvp: true,
      },
      {
        id: 'audit-theme-execution-analysis',
        name: '稽查主题运行分析',
        path: '/audit-theme-execution-analysis',
        icon: Activity,
        enabled: true,
        isMvp: true,
      },
      {
        id: 'audit-scenario-analysis',
        name: '重点稽查场景要素分析',
        path: '/audit-scenario-analysis',
        icon: Target,
        enabled: true,
        isMvp: true,
      },
      {
        id: 'problem-penetration-analysis',
        name: '问题命中穿透分析',
        path: '/problem-penetration-analysis',
        icon: AlertTriangle,
        enabled: true,
        isMvp: true,
      },
      // {
      //   id: 'issue-hits',
      //   name: '问题命中管理',
      //   path: '/issue-hits',
      //   icon: AlertTriangle,
      //   enabled: true,
      //   isMvp: true,
      // },
    ],
  },
  // {
  //   id: "system-settings",
  //   name: "系统设置",
  //   icon: Settings,
  //   children: [
  //     {
  //       id: "user-permissions",
  //       name: "用户与权限",
  //       icon: Users,
  //       enabled: false,
  //       futureFeature: true,
  //     },
  //     {
  //       id: "log-management",
  //       name: "日志管理",
  //       icon: FileText,
  //       enabled: false,
  //       futureFeature: true,
  //     },
  //   ],
  // },
]

interface SidebarProps {
  className?: string
}

export function Sidebar({ className }: SidebarProps) {
  const pathname = usePathname()
  const [expandedGroups, setExpandedGroups] = useState<Set<string>>(new Set(menuConfig.map((group) => group.id)))
  const [isMobileOpen, setIsMobileOpen] = useState(false)

  const toggleGroup = (groupId: string) => {
    const newExpanded = new Set(expandedGroups)
    if (newExpanded.has(groupId)) {
      newExpanded.delete(groupId)
    } else {
      newExpanded.add(groupId)
    }
    setExpandedGroups(newExpanded)
  }

  const closeMobile = () => setIsMobileOpen(false)

  function renderMenuItems(items: MenuItem[], pathname: string, expandedGroups: Set<string>, toggleGroup: (id: string) => void, level = 1) {
    return items.map((item) => {
      const isActive = item.path && pathname.startsWith(item.path)
      const hasChildren = item.children && item.children.length > 0
      const isExpanded = expandedGroups.has(item.id)
      return (
        <div key={item.id} style={{ paddingLeft: level * 16 }}>
          <div className={cn("flex items-center gap-2 py-1", isActive && "font-bold text-primary")}
            onClick={() => hasChildren && toggleGroup(item.id)}
          >
            {item.icon && <item.icon className="w-4 h-4" />}
            {item.path ? (
              <Link href={item.path} className="flex-1 truncate">
                {item.name}
              </Link>
            ) : (
              <span className="flex-1 truncate cursor-pointer">{item.name}</span>
            )}
            {hasChildren && (isExpanded ? <ChevronDown className="w-4 h-4" /> : <ChevronRight className="w-4 h-4" />)}
          </div>
          {hasChildren && isExpanded && (
            <div>{renderMenuItems(item.children!, pathname, expandedGroups, toggleGroup, level + 1)}</div>
          )}
        </div>
      )
    })
  }

  return (
    <>
      {/* Mobile menu button */}
      <Button
        variant="ghost"
        size="sm"
        className="fixed top-4 left-4 z-50 md:hidden"
        onClick={() => setIsMobileOpen(true)}
      >
        <Menu className="h-5 w-5" />
      </Button>

      {/* Mobile overlay */}
      {isMobileOpen && <div className="fixed inset-0 z-40 bg-black/50 md:hidden" onClick={closeMobile} />}

      {/* Sidebar */}
      <div
        className={cn(
          "fixed left-0 top-0 z-50 h-full w-72 transform border-r bg-white transition-transform duration-200 ease-in-out md:relative md:translate-x-0",
          isMobileOpen ? "translate-x-0" : "-translate-x-full",
          className,
        )}
      >
        <div className="flex h-full flex-col">
          {/* Header */}
          <div className="flex h-16 items-center justify-between border-b px-6">
            <Link href="/" className="flex items-center gap-2">
              <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-blue-600">
                <Database className="h-5 w-5 text-white" />
              </div>
              <div className="flex flex-col">
                <span className="text-sm font-semibold">电力数据大脑</span>
                <span className="text-xs text-gray-500">智能稽查系统</span>
              </div>
            </Link>
            <Button variant="ghost" size="sm" className="md:hidden" onClick={closeMobile}>
              <X className="h-4 w-4" />
            </Button>
          </div>

          {/* Menu */}
          <ScrollArea className="flex-1 px-3 py-4">
            <div className="space-y-2">
              {menuConfig.map((group) => (
                <div key={group.id} className="space-y-1">
                  <Button
                    variant="ghost"
                    className="w-full justify-start px-3 py-2 text-sm font-medium"
                    onClick={() => toggleGroup(group.id)}
                  >
                    <group.icon className="mr-2 h-4 w-4" />
                    {group.name}
                    {expandedGroups.has(group.id) ? (
                      <ChevronDown className="ml-auto h-4 w-4" />
                    ) : (
                      <ChevronRight className="ml-auto h-4 w-4" />
                    )}
                  </Button>

                  {expandedGroups.has(group.id) && (
                    <div className="ml-4 space-y-1">
                      {renderMenuItems(group.children, pathname, expandedGroups, toggleGroup)}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </ScrollArea>
        </div>
      </div>
    </>
  )
}
