"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, <PERSON>bsTrigger } from "@/components/ui/tabs"
import { Button } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Separator } from "@/components/ui/separator"
import {
  Wand2,
  Palette,
  Code,
  Save,
  ReplaceIcon as Update,
  AlertTriangle,
  Shield,
  BookOpen,
  Plus,
  Trash2,
} from "lucide-react"

// Types for rule components
interface RuleComponent {
  id: string
  type: "indicator" | "dimension" | "operator" | "logic"
  label: string
  value?: string
  category: string
}

interface CanvasComponent extends RuleComponent {
  x: number
  y: number
  connections?: string[]
}

// Mock data for rule components palette
const ruleComponentsPalette: RuleComponent[] = [
  // Business Indicators
  { id: "monthly_usage", type: "indicator", label: "月度用电量", category: "业务指标" },
  { id: "electricity_fee", type: "indicator", label: "电费金额", category: "业务指标" },
  { id: "peak_demand", type: "indicator", label: "最大需量", category: "业务指标" },
  { id: "power_factor", type: "indicator", label: "功率因数", category: "业务指标" },

  // Business Dimensions
  { id: "customer_type", type: "dimension", label: "客户类型", category: "业务维度" },
  { id: "geographic_region", type: "dimension", label: "地理区域", category: "业务维度" },
  { id: "voltage_level", type: "dimension", label: "电压等级", category: "业务维度" },
  { id: "industry_type", type: "dimension", label: "行业类型", category: "业务维度" },

  // Operators
  { id: "greater_than", type: "operator", label: "大于", category: "运算符" },
  { id: "less_than", type: "operator", label: "小于", category: "运算符" },
  { id: "equals", type: "operator", label: "等于", category: "运算符" },
  { id: "contains", type: "operator", label: "包含", category: "运算符" },
  { id: "between", type: "operator", label: "介于", category: "运算符" },

  // Logic Connectors
  { id: "and", type: "logic", label: "并且", category: "逻辑连接符" },
  { id: "or", type: "logic", label: "或者", category: "逻辑连接符" },
  { id: "not", type: "logic", label: "非", category: "逻辑连接符" },
]

export default function RuleGenerationPage() {
  const [activeTab, setActiveTab] = useState("natural")
  const [naturalLanguageInput, setNaturalLanguageInput] = useState("")
  const [canvasComponents, setCanvasComponents] = useState<CanvasComponent[]>([])
  const [canvasInput, setCanvasInput] = useState("")
  const [showCanvasInput, setShowCanvasInput] = useState(false)
  const [generatedDescription, setGeneratedDescription] = useState("")
  const [selectedCodeBlock, setSelectedCodeBlock] = useState<string | null>(null)
  const [ruleData, setRuleData] = useState<any>(null)

  // Mock generated code outputs
  const [generatedOutputs, setGeneratedOutputs] = useState({
    semanticExpression: `{
  "rule_id": "RULE_001",
  "rule_name": "高耗能客户识别规则",
  "conditions": [
    {
      "field": "monthly_usage",
      "operator": "greater_than",
      "value": 10000,
      "unit": "kWh"
    },
    {
      "logic": "AND",
      "field": "customer_type",
      "operator": "equals",
      "value": "工商业"
    }
  ],
  "actions": ["flag_high_consumption", "generate_alert"]
}`,
    semanticSQL: `SELECT 
  customer_id,
  customer_name,
  monthly_usage,
  customer_type
FROM semantic_layer.customer_power_consumption 
WHERE monthly_usage > 10000 
  AND customer_type = '工商业'
  AND audit_period = CURRENT_MONTH()`,
    executableSQL: `SELECT 
  c.customer_id,
  c.customer_name,
  SUM(m.consumption_value) as monthly_usage,
  c.customer_type
FROM customers c
JOIN meter_readings m ON c.customer_id = m.customer_id
WHERE m.reading_date >= DATE_TRUNC('month', CURRENT_DATE)
  AND m.reading_date < DATE_TRUNC('month', CURRENT_DATE) + INTERVAL '1 month'
GROUP BY c.customer_id, c.customer_name, c.customer_type
HAVING SUM(m.consumption_value) > 10000 
  AND c.customer_type = '工商业'`,
  })

  // Initialize with rule data if provided (e.g., from rule management page)
  useEffect(() => {
    // This would typically come from props or URL parameters
    const urlParams = new URLSearchParams(window.location.search)
    const ruleId = urlParams.get("ruleId")

    if (ruleId) {
      // Mock pre-populated rule data
      const mockRuleData = {
        id: ruleId,
        name: "高耗能客户识别规则",
        description: "识别月度用电量超过10000千瓦时的工商业客户",
        components: [
          { id: "comp1", type: "indicator", label: "月度用电量", x: 100, y: 100, value: "10000" },
          { id: "comp2", type: "operator", label: "大于", x: 250, y: 100 },
          { id: "comp3", type: "logic", label: "并且", x: 400, y: 100 },
          { id: "comp4", type: "dimension", label: "客户类型", x: 550, y: 100, value: "工商业" },
        ],
      }

      setRuleData(mockRuleData)
      setNaturalLanguageInput(mockRuleData.description)
      setCanvasComponents(mockRuleData.components)
      setGeneratedDescription(mockRuleData.description)
    }
  }, [])

  const handleGenerateRule = () => {
    if (naturalLanguageInput.trim()) {
      // Mock rule generation
      setGeneratedDescription(naturalLanguageInput)
      // In real implementation, this would call an AI service
    }
  }

  const handleAddCanvasComponent = () => {
    if (canvasInput.trim()) {
      // Mock parsing of natural language input into components
      const newComponent: CanvasComponent = {
        id: `comp_${Date.now()}`,
        type: "indicator",
        label: canvasInput,
        category: "自定义",
        x: Math.random() * 400 + 50,
        y: Math.random() * 200 + 50,
      }

      setCanvasComponents([...canvasComponents, newComponent])
      setCanvasInput("")
      setShowCanvasInput(false)

      // Update generated description
      updateGeneratedDescription([...canvasComponents, newComponent])
    }
  }

  const updateGeneratedDescription = (components: CanvasComponent[]) => {
    // Mock natural language generation from visual components
    if (components.length > 0) {
      const description = components.map((comp) => comp.label).join(" ")
      setGeneratedDescription(`基于可视化组件生成的规则: ${description}`)
    }
  }

  const handleDragStart = (e: React.DragEvent, component: RuleComponent) => {
    e.dataTransfer.setData("component", JSON.stringify(component))
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    const componentData = e.dataTransfer.getData("component")
    if (componentData) {
      const component = JSON.parse(componentData)
      const rect = e.currentTarget.getBoundingClientRect()
      const newComponent: CanvasComponent = {
        ...component,
        id: `${component.id}_${Date.now()}`,
        x: e.clientX - rect.left,
        y: e.clientY - rect.top,
      }

      const updatedComponents = [...canvasComponents, newComponent]
      setCanvasComponents(updatedComponents)
      updateGeneratedDescription(updatedComponents)
    }
  }

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
  }

  const removeCanvasComponent = (id: string) => {
    const updatedComponents = canvasComponents.filter((comp) => comp.id !== id)
    setCanvasComponents(updatedComponents)
    updateGeneratedDescription(updatedComponents)
  }

  const getComponentColor = (type: string) => {
    switch (type) {
      case "indicator":
        return "bg-blue-100 border-blue-300 text-blue-800"
      case "dimension":
        return "bg-green-100 border-green-300 text-green-800"
      case "operator":
        return "bg-orange-100 border-orange-300 text-orange-800"
      case "logic":
        return "bg-purple-100 border-purple-300 text-purple-800"
      default:
        return "bg-gray-100 border-gray-300 text-gray-800"
    }
  }

  const groupedComponents = ruleComponentsPalette.reduce(
    (acc, component) => {
      if (!acc[component.category]) {
        acc[component.category] = []
      }
      acc[component.category].push(component)
      return acc
    },
    {} as Record<string, RuleComponent[]>,
  )

  return (
    <div className="flex-1 p-6 space-y-6">
      {/* Page Header */}
      <div className="space-y-2">
        <h1 className="text-3xl font-bold tracking-tight text-gray-900">稽查规则智能生成</h1>
        <p className="text-gray-600">通过自然语言或可视化编排方式智能生成稽查规则</p>
      </div>

      <div className="flex gap-6">
        {/* Main Content Area */}
        <div className="flex-1 space-y-6">
          {/* Mode Switching Tabs */}
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="natural" className="flex items-center gap-2">
                <Wand2 className="h-4 w-4" />
                自然语言生成模式
              </TabsTrigger>
              <TabsTrigger value="visual" className="flex items-center gap-2">
                <Palette className="h-4 w-4" />
                可视化编排模式
              </TabsTrigger>
              <TabsTrigger value="knowledge" className="flex items-center gap-2">
                <BookOpen className="h-4 w-4" />
                知识分析
              </TabsTrigger>
            </TabsList>

            {/* Natural Language Mode */}
            <TabsContent value="natural" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>自然语言描述</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <Textarea
                    placeholder="请输入稽查规则的自然语言描述..."
                    value={naturalLanguageInput}
                    onChange={(e) => setNaturalLanguageInput(e.target.value)}
                    className="min-h-32"
                  />
                  <Button onClick={handleGenerateRule} className="w-full">
                    <Wand2 className="mr-2 h-4 w-4" />
                    生成规则
                  </Button>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Visual Orchestration Mode */}
            <TabsContent value="visual" className="space-y-4">
              <div className="flex gap-4 h-96">
                {/* Left Palette */}
                <Card className="w-64 flex-shrink-0">
                  <CardHeader className="pb-3">
                    <CardTitle className="text-sm">规则组件</CardTitle>
                  </CardHeader>
                  <CardContent className="p-0">
                    <ScrollArea className="h-80">
                      <div className="p-3 space-y-3">
                        {Object.entries(groupedComponents).map(([category, components]) => (
                          <div key={category}>
                            <h4 className="text-xs font-medium text-gray-500 mb-2">{category}</h4>
                            <div className="space-y-1">
                              {components.map((component) => (
                                <div
                                  key={component.id}
                                  draggable
                                  onDragStart={(e) => handleDragStart(e, component)}
                                  className={`p-2 rounded border cursor-move text-xs ${getComponentColor(component.type)} hover:shadow-sm transition-shadow`}
                                >
                                  {component.label}
                                </div>
                              ))}
                            </div>
                          </div>
                        ))}
                      </div>
                    </ScrollArea>
                  </CardContent>
                </Card>

                {/* Middle Canvas */}
                <Card className="flex-1">
                  <CardHeader className="pb-3">
                    <CardTitle className="text-sm flex items-center justify-between">
                      拖拽组件或输入条件片段构建规则
                      <Button size="sm" variant="outline" onClick={() => setShowCanvasInput(true)}>
                        <Plus className="h-3 w-3 mr-1" />
                        输入条件
                      </Button>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="p-0">
                    <div
                      className="relative h-80 border-2 border-dashed border-gray-200 bg-gray-50/50"
                      onDrop={handleDrop}
                      onDragOver={handleDragOver}
                    >
                      {canvasComponents.length === 0 && !showCanvasInput && (
                        <div className="absolute inset-0 flex items-center justify-center text-gray-400 text-sm">
                          拖拽组件到此处或点击"输入条件"开始构建规则
                        </div>
                      )}

                      {/* Canvas Input */}
                      {showCanvasInput && (
                        <div className="absolute top-4 left-4 right-4 bg-white border rounded-lg p-3 shadow-sm">
                          <Textarea
                            placeholder="在此输入条件片段..."
                            value={canvasInput}
                            onChange={(e) => setCanvasInput(e.target.value)}
                            className="mb-2 min-h-16"
                          />
                          <div className="flex gap-2">
                            <Button size="sm" onClick={handleAddCanvasComponent}>
                              添加到画布
                            </Button>
                            <Button size="sm" variant="outline" onClick={() => setShowCanvasInput(false)}>
                              取消
                            </Button>
                          </div>
                        </div>
                      )}

                      {/* Canvas Components */}
                      {canvasComponents.map((component) => (
                        <div
                          key={component.id}
                          className={`absolute p-2 rounded border text-xs ${getComponentColor(component.type)} shadow-sm group`}
                          style={{ left: component.x, top: component.y }}
                        >
                          <div className="flex items-center gap-1">
                            {component.label}
                            {component.value && (
                              <Badge variant="secondary" className="text-xs">
                                {component.value}
                              </Badge>
                            )}
                            <Button
                              size="sm"
                              variant="ghost"
                              className="h-4 w-4 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                              onClick={() => removeCanvasComponent(component.id)}
                            >
                              <Trash2 className="h-3 w-3" />
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Real-time Natural Language Output */}
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm">自动生成规则描述</CardTitle>
                </CardHeader>
                <CardContent>
                  <Textarea
                    value={generatedDescription}
                    readOnly
                    className="min-h-20 bg-gray-50"
                    placeholder="基于可视化组件自动生成的规则描述将显示在这里..."
                  />
                </CardContent>
              </Card>
            </TabsContent>

            {/* Knowledge Analysis Tab */}
            <TabsContent value="knowledge" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>稽查知识分析</CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* 规则选择/输入区域 */}
                  <div>
                    <div className="font-medium text-gray-700 mb-2">分析对象</div>
                    <Textarea
                      value={generatedDescription || "请输入或选择要分析的规则描述..."}
                      readOnly
                      className="min-h-20 bg-gray-50"
                    />
                  </div>
                  {/* 分析结果区域 */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* 结构分析 */}
                    <Card className="col-span-1">
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm flex items-center gap-2">
                          <Shield className="h-4 w-4 text-blue-400" /> 规则结构完整性分析
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <ul className="text-sm text-gray-700 space-y-1">
                          <li>✔️ 条件逻辑完整，未检测到明显缺失</li>
                          <li>✔️ 结果列定义规范</li>
                          <li>✔️ 动态参数配置合理</li>
                        </ul>
                      </CardContent>
                    </Card>
                    {/* 阈值与地域合理性分析 */}
                    <Card className="col-span-1">
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm flex items-center gap-2">
                          <AlertTriangle className="h-4 w-4 text-yellow-400" /> 阈值与地域合理性分析
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <ul className="text-sm text-gray-700 space-y-1">
                          <li>⚠️ 阈值10000千瓦时高于本地区平均水平（建议参考行业标准）</li>
                          <li>✔️ 地域适用范围明确</li>
                        </ul>
                      </CardContent>
                    </Card>
                    {/* 风险拦截准确性分析 */}
                    <Card className="col-span-1">
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm flex items-center gap-2">
                          <Shield className="h-4 w-4 text-green-400" /> 风险拦截准确性分析
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <ul className="text-sm text-gray-700 space-y-1">
                          <li>✔️ 规则可有效识别高耗能客户</li>
                          <li>✔️ 误报率低，命中率高（基于历史数据模拟）</li>
                        </ul>
                      </CardContent>
                    </Card>
                    {/* 智能建议与回流 */}
                    <Card className="col-span-1">
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm flex items-center gap-2">
                          <Wand2 className="h-4 w-4 text-purple-400" /> 智能优化建议
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <ul className="text-sm text-gray-700 space-y-1">
                          <li>• 建议将阈值动态调整为行业均值±20%</li>
                          <li>• 可增加客户类型细分，提升规则适用性</li>
                          <li>• 可关联最新政策条款，增强合规性</li>
                        </ul>
                        <Button className="mt-4 w-full" variant="outline">
                          一键回流到规则生成
                        </Button>
                      </CardContent>
                    </Card>
                  </div>
                  {/* 分析结果可视化区域 */}
                  <div>
                    <div className="font-medium text-gray-700 mb-2">分析结果可视化</div>
                    <div className="flex flex-wrap gap-4">
                      <Card className="flex-1 min-w-[220px]">
                        <CardHeader className="pb-2">
                          <CardTitle className="text-xs text-gray-500">命中率趋势</CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="h-24 flex items-center justify-center text-gray-400 text-xs">[趋势图占位]</div>
                        </CardContent>
                      </Card>
                      <Card className="flex-1 min-w-[220px]">
                        <CardHeader className="pb-2">
                          <CardTitle className="text-xs text-gray-500">地域分布</CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="h-24 flex items-center justify-center text-gray-400 text-xs">[地域分布图占位]</div>
                        </CardContent>
                      </Card>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>

          {/* Shared Output Area */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">生成结果</h3>

            <div className="grid gap-4">
              {/* Semantic Expression */}
              <Card>
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-sm">生成的语义表达式 (JSON)</CardTitle>
                    <Button size="sm" variant="outline" onClick={() => setSelectedCodeBlock("semantic")}>
                      <Code className="h-3 w-3 mr-1" />
                      格式化与解释
                    </Button>
                  </div>
                </CardHeader>
                <CardContent>
                  <pre className="text-xs bg-gray-50 p-3 rounded overflow-x-auto">
                    {generatedOutputs.semanticExpression}
                  </pre>
                </CardContent>
              </Card>

              {/* Semantic SQL */}
              <Card>
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-sm">语义层伪SQL (逻辑SQL)</CardTitle>
                    <Button size="sm" variant="outline" onClick={() => setSelectedCodeBlock("logical")}>
                      <Code className="h-3 w-3 mr-1" />
                      格式化与解释
                    </Button>
                  </div>
                </CardHeader>
                <CardContent>
                  <pre className="text-xs bg-gray-50 p-3 rounded overflow-x-auto">{generatedOutputs.semanticSQL}</pre>
                </CardContent>
              </Card>

              {/* Executable SQL */}
              <Card>
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-sm">可执行SQL (物理SQL)</CardTitle>
                    <Button size="sm" variant="outline" onClick={() => setSelectedCodeBlock("physical")}>
                      <Code className="h-3 w-3 mr-1" />
                      格式化与解释
                    </Button>
                  </div>
                </CardHeader>
                <CardContent>
                  <pre className="text-xs bg-gray-50 p-3 rounded overflow-x-auto">{generatedOutputs.executableSQL}</pre>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-4 pt-4 border-t">
            <Button className="flex-1">
              <Save className="mr-2 h-4 w-4" />
              保存为新规则
            </Button>
            <Button variant="outline" disabled className="flex-1">
              <Update className="mr-2 h-4 w-4" />
              更新原规则 (未来功能)
            </Button>
          </div>
        </div>

        {/* Right Sidebar - Future Features */}
        <div className="w-80 space-y-4">
          <h3 className="text-lg font-semibold">扩展功能</h3>

          <Card className="border-2 border-dashed border-gray-300 bg-gray-50/50">
            <CardHeader className="text-center pb-4">
              <div className="mx-auto w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center mb-3">
                <Shield className="h-6 w-6 text-gray-400" />
              </div>
              <CardTitle className="text-sm font-medium text-gray-600">规则校验与优化建议</CardTitle>
            </CardHeader>
            <CardContent className="text-center">
              <p className="text-xs text-gray-500">未来功能</p>
              <p className="text-xs text-gray-400 mt-1">智能检测规则逻辑并提供优化建议</p>
            </CardContent>
          </Card>

          <Card className="border-2 border-dashed border-gray-300 bg-gray-50/50">
            <CardHeader className="text-center pb-4">
              <div className="mx-auto w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center mb-3">
                <AlertTriangle className="h-6 w-6 text-gray-400" />
              </div>
              <CardTitle className="text-sm font-medium text-gray-600">规则冲突检测</CardTitle>
            </CardHeader>
            <CardContent className="text-center">
              <p className="text-xs text-gray-500">未来功能</p>
              <p className="text-xs text-gray-400 mt-1">自动检测与现有规则的冲突</p>
            </CardContent>
          </Card>

          <Card className="border-2 border-dashed border-gray-300 bg-gray-50/50">
            <CardHeader className="text-center pb-4">
              <div className="mx-auto w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center mb-3">
                <BookOpen className="h-6 w-6 text-gray-400" />
              </div>
              <CardTitle className="text-sm font-medium text-gray-600">关联政策法规/案例推荐</CardTitle>
            </CardHeader>
            <CardContent className="text-center">
              <p className="text-xs text-gray-500">未来功能</p>
              <p className="text-xs text-gray-400 mt-1">推荐相关政策法规和案例</p>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Code Explanation Modal */}
      <Dialog open={!!selectedCodeBlock} onOpenChange={() => setSelectedCodeBlock(null)}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Code className="h-5 w-5" />
              代码格式化与解释
            </DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <h4 className="font-medium mb-2">格式化代码</h4>
              <pre className="text-sm bg-gray-50 p-4 rounded border overflow-x-auto">
                {selectedCodeBlock === "semantic" && generatedOutputs.semanticExpression}
                {selectedCodeBlock === "logical" && generatedOutputs.semanticSQL}
                {selectedCodeBlock === "physical" && generatedOutputs.executableSQL}
              </pre>
            </div>
            <Separator />
            <div>
              <h4 className="font-medium mb-2">详细解释</h4>
              <div className="text-sm text-gray-700 space-y-2">
                {selectedCodeBlock === "semantic" && (
                  <div>
                    <p>
                      <strong>语义表达式说明：</strong>
                    </p>
                    <p>• rule_id: 规则的唯一标识符</p>
                    <p>• conditions: 定义规则的判断条件，包括字段、操作符和值</p>
                    <p>• actions: 规则触发后执行的动作</p>
                    <p>• 该JSON结构提供了规则的完整语义描述，便于系统理解和执行</p>
                  </div>
                )}
                {selectedCodeBlock === "logical" && (
                  <div>
                    <p>
                      <strong>语义层SQL说明：</strong>
                    </p>
                    <p>• 使用语义层的抽象表名和字段名</p>
                    <p>• 隐藏了底层物理表的复杂性</p>
                    <p>• 提供业务友好的查询接口</p>
                    <p>• 支持跨数据源的统一查询</p>
                  </div>
                )}
                {selectedCodeBlock === "physical" && (
                  <div>
                    <p>
                      <strong>物理SQL说明：</strong>
                    </p>
                    <p>• 直接操作底层数据库表</p>
                    <p>• 包含具体的表连接和聚合逻辑</p>
                    <p>• 针对特定数据库优化的查询语句</p>
                    <p>• 可直接在数据库中执行</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}
