"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import StandardForm from "./StandardForm";

interface Standard {
  id: string;
  name: string;
  description: string;
  type: "text" | "image" | "document" | "audio";
  status: "active" | "inactive";
  createdAt: string;
}

const mockStandards: Standard[] = [
  {
    id: "1",
    name: "工单响应完整性",
    description: "检查工单响应是否包含所有必要信息",
    type: "text",
    status: "active",
    createdAt: "2024-03-20",
  },
  {
    id: "2",
    name: "图片证据规范",
    description: "检查上传的图片是否符合清晰度和内容相关性要求",
    type: "image",
    status: "active",
    createdAt: "2024-03-20",
  },
];

export default function StandardManagement() {
  const [standards, setStandards] = useState<Standard[]>(mockStandards);
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const handleAddStandard = (newStandard: Omit<Standard, "id" | "createdAt">) => {
    const standard: Standard = {
      ...newStandard,
      id: (standards.length + 1).toString(),
      createdAt: new Date().toISOString().split("T")[0],
    };
    setStandards([...standards, standard]);
    setIsDialogOpen(false);
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold">质检标准列表</h2>
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button>新增标准</Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[600px]">
            <DialogHeader>
              <DialogTitle>新增质检标准</DialogTitle>
            </DialogHeader>
            <StandardForm onSubmit={handleAddStandard} />
          </DialogContent>
        </Dialog>
      </div>

      <Card>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>标准名称</TableHead>
              <TableHead>描述</TableHead>
              <TableHead>类型</TableHead>
              <TableHead>状态</TableHead>
              <TableHead>创建日期</TableHead>
              <TableHead>操作</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {standards.map((standard) => (
              <TableRow key={standard.id}>
                <TableCell>{standard.name}</TableCell>
                <TableCell>{standard.description}</TableCell>
                <TableCell>
                  <Badge variant="outline">
                    {standard.type === "text" && "文本"}
                    {standard.type === "image" && "图片"}
                    {standard.type === "document" && "文档"}
                    {standard.type === "audio" && "音频"}
                  </Badge>
                </TableCell>
                <TableCell>
                  <Badge variant={standard.status === "active" ? "success" : "secondary"}>
                    {standard.status === "active" ? "启用" : "停用"}
                  </Badge>
                </TableCell>
                <TableCell>{standard.createdAt}</TableCell>
                <TableCell>
                  <Button variant="outline" size="sm">
                    编辑
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </Card>
    </div>
  );
} 