"use client"

import { useState } from "react"
import { Search, Eye, AlertTriangle, Calendar, User, FileText } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import Link from "next/link"

// Mock data for issue hits
const mockIssueHits = [
  {
    id: "PROB_20240115_001",
    ruleName: "高耗能客户识别规则",
    customerName: "上海某制造有限公司",
    hitDate: "2024-01-15",
    status: "待处理",
    severity: "高",
    description: "月度用电量12,500kWh超过阈值10,000kWh",
    category: "用电异常",
  },
  {
    id: "PROB_20240115_002",
    ruleName: "功率因数不达标检测",
    customerName: "北京某钢铁有限公司",
    hitDate: "2024-01-15",
    status: "处理中",
    severity: "中",
    description: "功率因数0.75低于标准值0.9",
    category: "电能质量",
  },
  {
    id: "PROB_20240114_003",
    ruleName: "电费缴纳异常规则",
    customerName: "广州某商场",
    hitDate: "2024-01-14",
    status: "已完成",
    severity: "低",
    description: "连续3个月延迟缴费",
    category: "缴费异常",
  },
  {
    id: "PROB_20240114_004",
    ruleName: "异常用电模式检测",
    customerName: "深圳某工厂",
    hitDate: "2024-01-14",
    status: "待处理",
    severity: "高",
    description: "夜间用电量异常增加200%",
    category: "用电异常",
  },
  {
    id: "PROB_20240113_005",
    ruleName: "电表数据完整性检查",
    customerName: "天津某化工厂",
    hitDate: "2024-01-13",
    status: "已关闭",
    severity: "中",
    description: "电表数据缺失率超过5%",
    category: "数据质量",
  },
  {
    id: "PROB_20240113_006",
    ruleName: "峰谷用电比例异常",
    customerName: "重庆某医院",
    hitDate: "2024-01-13",
    status: "处理中",
    severity: "低",
    description: "峰谷用电比例1:3偏离正常范围",
    category: "用电模式",
  },
]

export default function IssueHitsPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("全部")
  const [severityFilter, setSeverityFilter] = useState("全部")
  const [currentPage, setCurrentPage] = useState(1)
  const itemsPerPage = 10

  // Filter data based on search, status, and severity
  const filteredData = mockIssueHits.filter((issue) => {
    const matchesSearch =
      searchTerm === "" ||
      issue.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      issue.ruleName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      issue.description.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesStatus = statusFilter === "全部" || issue.status === statusFilter
    const matchesSeverity = severityFilter === "全部" || issue.severity === severityFilter

    return matchesSearch && matchesStatus && matchesSeverity
  })

  // Pagination
  const totalPages = Math.ceil(filteredData.length / itemsPerPage)
  const startIndex = (currentPage - 1) * itemsPerPage
  const paginatedData = filteredData.slice(startIndex, startIndex + itemsPerPage)

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "待处理":
        return <Badge className="bg-yellow-100 text-yellow-800 border-yellow-300">待处理</Badge>
      case "处理中":
        return <Badge className="bg-blue-100 text-blue-800 border-blue-300">处理中</Badge>
      case "已完成":
        return <Badge className="bg-green-100 text-green-800 border-green-300">已完成</Badge>
      case "已关闭":
        return <Badge className="bg-gray-100 text-gray-800 border-gray-300">已关闭</Badge>
      default:
        return <Badge variant="secondary">{status}</Badge>
    }
  }

  const getSeverityBadge = (severity: string) => {
    switch (severity) {
      case "高":
        return <Badge className="bg-red-100 text-red-800 border-red-300">高</Badge>
      case "中":
        return <Badge className="bg-orange-100 text-orange-800 border-orange-300">中</Badge>
      case "低":
        return <Badge className="bg-blue-100 text-blue-800 border-blue-300">低</Badge>
      default:
        return <Badge variant="secondary">{severity}</Badge>
    }
  }

  return (
    <div className="flex-1 space-y-6 p-6">
      {/* Page Header */}
      <div className="space-y-2">
        <h1 className="text-3xl font-bold tracking-tight text-gray-900">问题命中列表</h1>
        <p className="text-gray-600">查看和管理稽查规则命中的问题</p>
      </div>

      {/* Summary Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总问题数</CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{mockIssueHits.length}</div>
            <p className="text-xs text-muted-foreground">累计发现问题</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">待处理</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">
              {mockIssueHits.filter((issue) => issue.status === "待处理").length}
            </div>
            <p className="text-xs text-muted-foreground">需要处理的问题</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">处理中</CardTitle>
            <User className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">
              {mockIssueHits.filter((issue) => issue.status === "处理中").length}
            </div>
            <p className="text-xs text-muted-foreground">正在处理的问题</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">已完成</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {mockIssueHits.filter((issue) => issue.status === "已完成").length}
            </div>
            <p className="text-xs text-muted-foreground">已处理完成</p>
          </CardContent>
        </Card>
      </div>

      {/* Controls Section */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="搜索客户名称、规则名称或问题描述..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-full sm:w-48">
                <SelectValue placeholder="处理状态" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="全部">全部状态</SelectItem>
                <SelectItem value="待处理">待处理</SelectItem>
                <SelectItem value="处理中">处理中</SelectItem>
                <SelectItem value="已完成">已完成</SelectItem>
                <SelectItem value="已关闭">已关闭</SelectItem>
              </SelectContent>
            </Select>
            <Select value={severityFilter} onValueChange={setSeverityFilter}>
              <SelectTrigger className="w-full sm:w-48">
                <SelectValue placeholder="严重程度" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="全部">全部级别</SelectItem>
                <SelectItem value="高">高</SelectItem>
                <SelectItem value="中">中</SelectItem>
                <SelectItem value="低">低</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Main Content - Issue Hits Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5" />
            问题命中记录
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>问题ID</TableHead>
                  <TableHead>命中规则</TableHead>
                  <TableHead>客户名称</TableHead>
                  <TableHead>命中日期</TableHead>
                  <TableHead>严重程度</TableHead>
                  <TableHead>处理状态</TableHead>
                  <TableHead>操作</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {paginatedData.map((issue) => (
                  <TableRow key={issue.id}>
                    <TableCell className="font-mono text-sm">{issue.id}</TableCell>
                    <TableCell>
                      <div className="space-y-1">
                        <div className="font-medium">{issue.ruleName}</div>
                        <div className="text-sm text-gray-500">{issue.category}</div>
                      </div>
                    </TableCell>
                    <TableCell className="font-medium">{issue.customerName}</TableCell>
                    <TableCell className="text-sm text-gray-600">{issue.hitDate}</TableCell>
                    <TableCell>{getSeverityBadge(issue.severity)}</TableCell>
                    <TableCell>{getStatusBadge(issue.status)}</TableCell>
                    <TableCell>
                      <Link href={`/problem-report?problemId=${issue.id}`}>
                        <Button size="sm" variant="outline">
                          <Eye className="h-3 w-3 mr-1" />
                          查看详情
                        </Button>
                      </Link>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>

          {/* Pagination */}
          <div className="flex items-center justify-between space-x-2 py-4">
            <div className="text-sm text-gray-500">
              显示 {startIndex + 1} 到 {Math.min(startIndex + itemsPerPage, filteredData.length)} 条， 共{" "}
              {filteredData.length} 条记录
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
                disabled={currentPage === 1}
              >
                上一页
              </Button>
              <div className="flex items-center space-x-1">
                {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                  <Button
                    key={page}
                    variant={currentPage === page ? "default" : "outline"}
                    size="sm"
                    onClick={() => setCurrentPage(page)}
                    className="w-8 h-8"
                  >
                    {page}
                  </Button>
                ))}
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage((prev) => Math.min(prev + 1, totalPages))}
                disabled={currentPage === totalPages}
              >
                下一页
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
