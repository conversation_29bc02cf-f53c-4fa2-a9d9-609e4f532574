"use client"

import { useState, use<PERSON><PERSON>back } from "react"
import { <PERSON>, <PERSON>Content, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { 
  Upload, 
  Edit2, 
  Eye, 
  CheckCircle, 
  FileText, 
  Database, 
  GitBranch, 
  Zap,
  AlertTriangle,
  TrendingUp,
  Network,
  Clock,
  BarChart3,
  Search
} from "lucide-react"
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/lib/sql-parser"
import { <PERSON><PERSON><PERSON>iningE<PERSON><PERSON> } from "@/lib/relation-mining"
import { mockSQLStatements, mockParseResults, mockAnalysisStats, mockRelationGraphData } from "@/lib/mock-sql-data"
import SQLAnalysisResults from "@/components/sql-analysis/SQLAnalysisResults"
import RelationGraphVisualization from "@/components/sql-analysis/RelationGraphVisualization"
import type { SQLParseResult, RelationGraph } from "@/types/sql-analysis"

export default function EnhancedMetadataScriptExtract() {
  const [activeTab, setActiveTab] = useState("upload")
  const [sqlContent, setSqlContent] = useState("")
  const [selectedExample, setSelectedExample] = useState<string>("")
  const [parseResult, setParseResult] = useState<SQLParseResult | null>(null)
  const [relationGraph, setRelationGraph] = useState<RelationGraph | null>(null)
  const [isAnalyzing, setIsAnalyzing] = useState(false)
  const [analysisProgress, setAnalysisProgress] = useState(0)
  const [step, setStep] = useState(0) // 0: 输入, 1: 解析中, 2: 结果展示, 3: 关系图谱

  // 初始化分析器
  const sqlAnalyzer = new SQLAnalyzer({ dialect: 'mysql' })
  const relationMiner = new RelationMiningEngine()

  // 模拟分析进度
  const simulateAnalysisProgress = useCallback(() => {
    setAnalysisProgress(0)
    const interval = setInterval(() => {
      setAnalysisProgress(prev => {
        if (prev >= 100) {
          clearInterval(interval)
          return 100
        }
        return prev + 10
      })
    }, 200)
    return interval
  }, [])

  // 处理SQL解析
  const handleAnalyzeSQL = async () => {
    if (!sqlContent.trim()) return

    setIsAnalyzing(true)
    setStep(1)
    
    const progressInterval = simulateAnalysisProgress()

    try {
      // 模拟延迟以展示进度
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      // 使用Mock数据或实际解析
      const result = mockParseResults[0] // 使用第一个Mock结果
      result.sql_content = sqlContent
      
      setParseResult(result)
      
      // 构建关系图谱 (使用Mock数据)
      setRelationGraph(mockRelationGraphData)
      
      setStep(2)
    } catch (error) {
      console.error('SQL解析失败:', error)
    } finally {
      clearInterval(progressInterval)
      setIsAnalyzing(false)
      setAnalysisProgress(100)
    }
  }

  // 加载示例SQL
  const loadExampleSQL = (exampleId: string) => {
    const example = mockSQLStatements.find(stmt => stmt.id === exampleId)
    if (example) {
      setSqlContent(example.sql)
      setSelectedExample(exampleId)
    }
  }

  // 重置分析
  const resetAnalysis = () => {
    setStep(0)
    setParseResult(null)
    setRelationGraph(null)
    setAnalysisProgress(0)
  }

  return (
    <div className="flex-1 space-y-6 p-6">
      {/* 页面标题 */}
      <div className="space-y-2">
        <h1 className="text-3xl font-bold tracking-tight text-gray-900 flex items-center gap-2">
          <Zap className="h-7 w-7 text-blue-500" />
          SQL脚本解析增强
        </h1>
        <p className="text-gray-600">
          智能解析SQL脚本，提取表间关联关系，补充物理外键约束缺失的关联信息
        </p>
      </div>

      {/* 步骤指示器 */}
      <div className="flex items-center space-x-4 mb-6">
        {[
          { step: 0, label: "SQL输入", icon: FileText },
          { step: 1, label: "智能解析", icon: Zap },
          { step: 2, label: "结果展示", icon: Eye },
          { step: 3, label: "关系图谱", icon: Network }
        ].map(({ step: stepNum, label, icon: Icon }) => (
          <div key={stepNum} className="flex items-center space-x-2">
            <div className={`
              w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium
              ${step >= stepNum 
                ? 'bg-blue-500 text-white' 
                : 'bg-gray-200 text-gray-500'
              }
            `}>
              {step > stepNum ? (
                <CheckCircle className="w-4 h-4" />
              ) : (
                <Icon className="w-4 h-4" />
              )}
            </div>
            <span className={`text-sm ${step >= stepNum ? 'text-blue-600 font-medium' : 'text-gray-500'}`}>
              {label}
            </span>
            {stepNum < 3 && (
              <div className={`w-8 h-0.5 ${step > stepNum ? 'bg-blue-500' : 'bg-gray-200'}`} />
            )}
          </div>
        ))}
      </div>

      {/* 主要内容区域 */}
      {step === 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="w-5 h-5" />
              SQL脚本输入
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* 示例SQL选择 */}
            <div className="space-y-2">
              <label className="text-sm font-medium">选择示例SQL（可选）</label>
              <Select value={selectedExample} onValueChange={loadExampleSQL}>
                <SelectTrigger>
                  <SelectValue placeholder="选择一个示例SQL进行体验" />
                </SelectTrigger>
                <SelectContent>
                  {mockSQLStatements.map(stmt => (
                    <SelectItem key={stmt.id} value={stmt.id}>
                      <div className="flex items-center gap-2">
                        <Badge variant="outline" className="text-xs">
                          {stmt.category}
                        </Badge>
                        {stmt.name}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList>
                <TabsTrigger value="upload">
                  <Upload className="w-4 h-4 mr-1" />
                  上传文件
                </TabsTrigger>
                <TabsTrigger value="paste">
                  <Edit2 className="w-4 h-4 mr-1" />
                  粘贴内容
                </TabsTrigger>
              </TabsList>
              
              <TabsContent value="upload" className="space-y-4">
                <Input 
                  type="file" 
                  accept=".sql,.txt" 
                  onChange={(e) => {
                    const file = e.target.files?.[0]
                    if (file) {
                      const reader = new FileReader()
                      reader.onload = (ev) => setSqlContent(ev.target?.result as string || "")
                      reader.readAsText(file)
                    }
                  }} 
                />
                <p className="text-sm text-gray-500">
                  支持 .sql 和 .txt 文件格式
                </p>
              </TabsContent>
              
              <TabsContent value="paste" className="space-y-4">
                <Textarea 
                  rows={12} 
                  value={sqlContent} 
                  onChange={(e) => setSqlContent(e.target.value)} 
                  placeholder="请粘贴SQL脚本内容..."
                  className="font-mono text-sm"
                />
                <p className="text-sm text-gray-500">
                  支持多条SQL语句，系统将自动识别和解析
                </p>
              </TabsContent>
            </Tabs>

            <div className="flex gap-2">
              <Button 
                onClick={handleAnalyzeSQL} 
                disabled={!sqlContent.trim() || isAnalyzing}
                className="flex items-center gap-2"
              >
                <Zap className="w-4 h-4" />
                开始智能解析
              </Button>
              {sqlContent && (
                <Button variant="outline" onClick={() => setSqlContent("")}>
                  清空内容
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* 解析进度 */}
      {step === 1 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Zap className="w-5 h-5 animate-pulse" />
              正在智能解析SQL脚本...
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <Progress value={analysisProgress} className="w-full" />
            <div className="text-sm text-gray-600">
              {analysisProgress < 30 && "正在解析SQL语法结构..."}
              {analysisProgress >= 30 && analysisProgress < 60 && "正在提取表和字段信息..."}
              {analysisProgress >= 60 && analysisProgress < 90 && "正在分析关联关系..."}
              {analysisProgress >= 90 && "正在生成分析报告..."}
            </div>
          </CardContent>
        </Card>
      )}

      {/* 解析结果展示 */}
      {step === 2 && parseResult && (
        <div className="space-y-6">
          {/* 解析概览 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="w-5 h-5" />
                解析结果概览
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center p-4 bg-blue-50 rounded-lg">
                  <Database className="w-8 h-8 text-blue-500 mx-auto mb-2" />
                  <div className="text-2xl font-bold text-blue-600">
                    {parseResult.extracted_tables.length}
                  </div>
                  <div className="text-sm text-gray-600">数据表</div>
                </div>
                <div className="text-center p-4 bg-green-50 rounded-lg">
                  <GitBranch className="w-8 h-8 text-green-500 mx-auto mb-2" />
                  <div className="text-2xl font-bold text-green-600">
                    {parseResult.extracted_relations.length}
                  </div>
                  <div className="text-sm text-gray-600">关联关系</div>
                </div>
                <div className="text-center p-4 bg-purple-50 rounded-lg">
                  <TrendingUp className="w-8 h-8 text-purple-500 mx-auto mb-2" />
                  <div className="text-2xl font-bold text-purple-600">
                    {Math.round(parseResult.confidence_score * 100)}%
                  </div>
                  <div className="text-sm text-gray-600">置信度</div>
                </div>
                <div className="text-center p-4 bg-orange-50 rounded-lg">
                  <Clock className="w-8 h-8 text-orange-500 mx-auto mb-2" />
                  <div className="text-2xl font-bold text-orange-600">
                    {parseResult.parsing_time_ms}ms
                  </div>
                  <div className="text-sm text-gray-600">解析耗时</div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 提取的表信息 */}
          <Card>
            <CardHeader>
              <CardTitle>提取的数据表</CardTitle>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>表名</TableHead>
                    <TableHead>别名</TableHead>
                    <TableHead>类型</TableHead>
                    <TableHead>操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {parseResult.extracted_tables.map((table, index) => (
                    <TableRow key={index}>
                      <TableCell className="font-mono">{table.name}</TableCell>
                      <TableCell>{table.alias || '-'}</TableCell>
                      <TableCell>
                        <Badge variant="outline">{table.type}</Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex gap-1">
                          {table.operations.map((op, i) => (
                            <Badge key={i} variant="secondary" className="text-xs">
                              {op}
                            </Badge>
                          ))}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>

          {/* 关联关系 */}
          <Card>
            <CardHeader>
              <CardTitle>发现的关联关系</CardTitle>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>源表.字段</TableHead>
                    <TableHead>目标表.字段</TableHead>
                    <TableHead>关系类型</TableHead>
                    <TableHead>置信度</TableHead>
                    <TableHead>JOIN条件</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {parseResult.extracted_relations.map((relation) => (
                    <TableRow key={relation.id}>
                      <TableCell className="font-mono">
                        {relation.from_table}.{relation.from_column}
                      </TableCell>
                      <TableCell className="font-mono">
                        {relation.to_table}.{relation.to_column}
                      </TableCell>
                      <TableCell>
                        <Badge 
                          variant={relation.relation_type.includes('JOIN') ? 'default' : 'secondary'}
                        >
                          {relation.relation_type}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <div className="w-16 bg-gray-200 rounded-full h-2">
                            <div 
                              className="bg-blue-500 h-2 rounded-full" 
                              style={{ width: `${relation.confidence * 100}%` }}
                            />
                          </div>
                          <span className="text-sm">
                            {Math.round(relation.confidence * 100)}%
                          </span>
                        </div>
                      </TableCell>
                      <TableCell className="font-mono text-xs">
                        {relation.join_condition || '-'}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>

          <SQLAnalysisResults
            parseResult={parseResult}
            onViewGraph={() => setStep(3)}
            onExport={() => {
              // 导出功能实现
              const dataStr = JSON.stringify(parseResult, null, 2)
              const dataBlob = new Blob([dataStr], { type: 'application/json' })
              const url = URL.createObjectURL(dataBlob)
              const link = document.createElement('a')
              link.href = url
              link.download = 'sql-analysis-result.json'
              link.click()
            }}
          />

          <div className="flex gap-2">
            <Button onClick={() => setStep(3)} className="flex items-center gap-2">
              <Network className="w-4 h-4" />
              查看关系图谱
            </Button>
            <Button variant="outline" onClick={resetAnalysis}>
              重新分析
            </Button>
          </div>
        </div>
      )}

      {/* 关系图谱展示 */}
      {step === 3 && relationGraph && (
        <div className="space-y-6">
          <RelationGraphVisualization
            data={relationGraph}
            onNodeClick={(node) => {
              console.log('点击节点:', node)
              // 可以添加节点详情展示逻辑
            }}
            onEdgeClick={(edge) => {
              console.log('点击边:', edge)
              // 可以添加边详情展示逻辑
            }}
          />

          <div className="flex gap-2">
            <Button variant="outline" onClick={() => setStep(2)}>
              <Eye className="w-4 h-4 mr-1" />
              返回结果
            </Button>
            <Button variant="outline" onClick={resetAnalysis}>
              重新分析
            </Button>
            <Button onClick={() => {
              // 导出图谱数据
              const dataStr = JSON.stringify(relationGraph, null, 2)
              const dataBlob = new Blob([dataStr], { type: 'application/json' })
              const url = URL.createObjectURL(dataBlob)
              const link = document.createElement('a')
              link.href = url
              link.download = 'relation-graph.json'
              link.click()
            }}>
              <Download className="w-4 h-4 mr-1" />
              导出图谱
            </Button>
          </div>
        </div>
      )}
    </div>
  )
}
