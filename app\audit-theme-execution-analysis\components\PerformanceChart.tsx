'use client'

import React from 'react'
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'

interface PerformanceData {
  executionId: string
  executionTime: string
  duration: number
  performanceScore: number
}

interface PerformanceChartProps {
  data: PerformanceData[]
}

export default function PerformanceChart({ data }: PerformanceChartProps) {
  if (!data || data.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>运行性能趋势图</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-64 flex items-center justify-center text-gray-500">
            暂无数据
          </div>
        </CardContent>
      </Card>
    )
  }

  // 准备图表数据
  const chartData = data.map((item, index) => ({
    x: index,
    y: item.duration / 1000, // 转换为秒
    label: item.executionTime.split(' ')[1], // 只显示时间部分
    performance: item.performanceScore
  }))

  const width = 800
  const height = 300
  const margin = { top: 20, right: 30, bottom: 40, left: 60 }
  const chartWidth = width - margin.left - margin.right
  const chartHeight = height - margin.top - margin.bottom

  // 计算比例尺
  const xScale = (x: number) => margin.left + (x / (chartData.length - 1)) * chartWidth
  const yScale = (y: number) => {
    const maxDuration = Math.max(...chartData.map(d => d.y))
    return margin.top + chartHeight - (y / maxDuration) * chartHeight
  }

  // 生成路径
  const linePath = chartData.map((point, index) => {
    if (index === 0) return `M ${xScale(point.x)} ${yScale(point.y)}`
    return `L ${xScale(point.x)} ${yScale(point.y)}`
  }).join(' ')

  return (
    <Card>
      <CardHeader>
        <CardTitle>运行性能趋势图</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="overflow-x-auto">
          <svg width={width} height={height} className="mx-auto">
            {/* 背景网格 */}
            <defs>
              <pattern id="grid" width="40" height="40" patternUnits="userSpaceOnUse">
                <path d="M 40 0 L 0 0 0 40" fill="none" stroke="#f0f0f0" strokeWidth="1"/>
              </pattern>
            </defs>
            <rect width="100%" height="100%" fill="url(#grid)" />
            
            {/* Y轴标签 */}
            {[0, 5, 10, 15, 20].map((tick) => (
              <g key={tick}>
                <line
                  x1={margin.left}
                  y1={yScale(tick)}
                  x2={margin.left - 5}
                  y2={yScale(tick)}
                  stroke="#666"
                  strokeWidth="1"
                />
                <text
                  x={margin.left - 10}
                  y={yScale(tick) + 4}
                  textAnchor="end"
                  fontSize="12"
                  fill="#666"
                >
                  {tick}s
                </text>
              </g>
            ))}
            
            {/* X轴标签 */}
            {chartData.map((point, index) => (
              <g key={index}>
                <line
                  x1={xScale(point.x)}
                  y1={margin.top + chartHeight}
                  x2={xScale(point.x)}
                  y2={margin.top + chartHeight + 5}
                  stroke="#666"
                  strokeWidth="1"
                />
                <text
                  x={xScale(point.x)}
                  y={margin.top + chartHeight + 20}
                  textAnchor="middle"
                  fontSize="10"
                  fill="#666"
                  transform={`rotate(-45 ${xScale(point.x)} ${margin.top + chartHeight + 20})`}
                >
                  {point.label}
                </text>
              </g>
            ))}
            
            {/* 趋势线 */}
            <path
              d={linePath}
              fill="none"
              stroke="#3b82f6"
              strokeWidth="2"
            />
            
            {/* 数据点 */}
            {chartData.map((point, index) => (
              <g key={index}>
                <circle
                  cx={xScale(point.x)}
                  cy={yScale(point.y)}
                  r="4"
                  fill="#3b82f6"
                  stroke="white"
                  strokeWidth="2"
                />
                {/* 性能评分标签 */}
                <text
                  x={xScale(point.x)}
                  y={yScale(point.y) - 10}
                  textAnchor="middle"
                  fontSize="10"
                  fill="#666"
                >
                  {point.performance}%
                </text>
              </g>
            ))}
            
            {/* 坐标轴 */}
            <line
              x1={margin.left}
              y1={margin.top}
              x2={margin.left}
              y2={margin.top + chartHeight}
              stroke="#333"
              strokeWidth="2"
            />
            <line
              x1={margin.left}
              y1={margin.top + chartHeight}
              x2={margin.left + chartWidth}
              y2={margin.top + chartHeight}
              stroke="#333"
              strokeWidth="2"
            />
          </svg>
        </div>
        
        {/* 图例 */}
        <div className="flex justify-center gap-8 mt-4 text-sm text-gray-600">
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
            <span>执行时间</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-gray-400 rounded-full"></div>
            <span>性能评分</span>
          </div>
        </div>
      </CardContent>
    </Card>
  )
} 