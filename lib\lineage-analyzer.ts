import type {
  FieldReference,
  TableReference,
  LineageNode,
  LineageEdge,
  LineagePath,
  FieldLineage,
  TableLineage,
  LineageGraph,
  LineageGraphNode,
  LineageGraphEdge,
  LineageAnalysisConfig,
  LineageAnalysisResult,
  LineageAnalysisService,
  Transformation,
  LineageStatistics
} from '@/types/lineage'

// Mock数据生成器
class MockLineageDataGenerator {
  private static instance: MockLineageDataGenerator
  
  static getInstance(): MockLineageDataGenerator {
    if (!MockLineageDataGenerator.instance) {
      MockLineageDataGenerator.instance = new MockLineageDataGenerator()
    }
    return MockLineageDataGenerator.instance
  }

  // 生成Mock血缘节点
  generateMockLineageNodes(field: FieldReference, depth: number = 3): LineageNode[] {
    const nodes: LineageNode[] = []
    const operations = ['SELECT', 'INSERT', 'UPDATE', 'TRANSFORM'] as const
    
    for (let i = 0; i < depth; i++) {
      const node: LineageNode = {
        id: `node_${field.table}_${field.column}_${i}`,
        field: {
          schema: field.schema,
          table: i === 0 ? field.table : `source_table_${i}`,
          column: field.column,
          alias: i > 0 ? `alias_${i}` : undefined
        },
        table: {
          schema: field.schema,
          name: i === 0 ? field.table : `source_table_${i}`,
          alias: i > 0 ? `t${i}` : undefined
        },
        operation: operations[i % operations.length],
        sqlContext: this.generateMockSQL(field, i),
        timestamp: new Date(Date.now() - i * 24 * 60 * 60 * 1000),
        confidence: Math.max(0.6, 1 - i * 0.1),
        metadata: {
          description: `数据处理步骤 ${i + 1}`,
          dataType: this.getRandomDataType(),
          businessMeaning: `业务含义描述 ${i + 1}`,
          tags: [`step_${i}`, 'processed']
        }
      }
      nodes.push(node)
    }
    
    return nodes
  }

  // 生成Mock SQL语句
  private generateMockSQL(field: FieldReference, step: number): string {
    const sqls = [
      `SELECT ${field.column} FROM ${field.schema}.${field.table} WHERE id > 0`,
      `INSERT INTO ${field.schema}.${field.table} (${field.column}) SELECT source_${field.column} FROM source_table_${step}`,
      `UPDATE ${field.schema}.${field.table} SET ${field.column} = UPPER(${field.column}) WHERE status = 'active'`,
      `SELECT t1.${field.column}, t2.related_field FROM ${field.table} t1 JOIN source_table_${step} t2 ON t1.id = t2.ref_id`
    ]
    return sqls[step % sqls.length]
  }

  private getRandomDataType(): string {
    const types = ['VARCHAR(255)', 'INT', 'DECIMAL(10,2)', 'DATE', 'TIMESTAMP', 'TEXT']
    return types[Math.floor(Math.random() * types.length)]
  }

  // 生成Mock转换操作
  generateMockTransformations(sourceField: FieldReference, targetField: FieldReference): Transformation[] {
    return [
      {
        id: `transform_${sourceField.table}_to_${targetField.table}`,
        type: 'CALCULATION',
        sourceFields: [sourceField],
        targetField,
        logic: `UPPER(TRIM(${sourceField.column}))`,
        confidence: 0.9,
        sqlContext: `SELECT UPPER(TRIM(${sourceField.column})) as ${targetField.column} FROM ${sourceField.schema}.${sourceField.table}`
      },
      {
        id: `transform_aggregation_${targetField.table}`,
        type: 'AGGREGATION',
        sourceFields: [sourceField],
        targetField,
        logic: `SUM(${sourceField.column})`,
        confidence: 0.85,
        sqlContext: `SELECT SUM(${sourceField.column}) as ${targetField.column} FROM ${sourceField.schema}.${sourceField.table} GROUP BY category`
      }
    ]
  }
}

// 血缘分析器实现
export class LineageAnalyzer implements LineageAnalysisService {
  private mockDataGenerator: MockLineageDataGenerator
  private defaultConfig: LineageAnalysisConfig = {
    maxDepth: 5,
    minConfidence: 0.6,
    includeTransformations: true,
    includeSystemTables: false,
    analysisDirection: 'BOTH'
  }

  constructor() {
    this.mockDataGenerator = MockLineageDataGenerator.getInstance()
  }

  // 分析字段血缘
  async analyzeFieldLineage(
    field: FieldReference, 
    config: LineageAnalysisConfig = this.defaultConfig
  ): Promise<LineageAnalysisResult> {
    const startTime = Date.now()
    
    try {
      // 获取上游和下游路径
      const upstreamPaths = await this.traceWritePath(field, config)
      const downstreamPaths = await this.traceUsagePath(field, config)
      
      // 构建血缘图
      const allNodes = this.extractNodesFromPaths([...upstreamPaths, ...downstreamPaths])
      const allEdges = this.buildEdgesFromPaths([...upstreamPaths, ...downstreamPaths])
      const lineageGraph = this.buildLineageGraph(allNodes, allEdges)
      
      const result: LineageAnalysisResult = {
        targetField: field,
        analysisType: 'FIELD_LINEAGE',
        lineageGraph,
        paths: [...upstreamPaths, ...downstreamPaths],
        statistics: {
          totalPaths: upstreamPaths.length + downstreamPaths.length,
          averagePathLength: this.calculateAveragePathLength([...upstreamPaths, ...downstreamPaths]),
          highConfidencePaths: [...upstreamPaths, ...downstreamPaths].filter(p => p.confidence > 0.8).length,
          analysisTimeMs: Date.now() - startTime
        },
        recommendations: this.generateRecommendations(field, [...upstreamPaths, ...downstreamPaths]),
        warnings: this.generateWarnings([...upstreamPaths, ...downstreamPaths])
      }
      
      return result
    } catch (error) {
      throw new Error(`字段血缘分析失败: ${error}`)
    }
  }

  // 分析表血缘
  async analyzeTableLineage(
    table: TableReference, 
    config: LineageAnalysisConfig = this.defaultConfig
  ): Promise<LineageAnalysisResult> {
    const startTime = Date.now()
    
    try {
      // 模拟表级血缘分析
      const mockFields = this.generateMockTableFields(table)
      const allPaths: LineagePath[] = []
      
      for (const field of mockFields) {
        const fieldPaths = await this.traceWritePath(field, config)
        allPaths.push(...fieldPaths)
      }
      
      const allNodes = this.extractNodesFromPaths(allPaths)
      const allEdges = this.buildEdgesFromPaths(allPaths)
      const lineageGraph = this.buildLineageGraph(allNodes, allEdges)
      
      const result: LineageAnalysisResult = {
        targetTable: table,
        analysisType: 'TABLE_LINEAGE',
        lineageGraph,
        paths: allPaths,
        statistics: {
          totalPaths: allPaths.length,
          averagePathLength: this.calculateAveragePathLength(allPaths),
          highConfidencePaths: allPaths.filter(p => p.confidence > 0.8).length,
          analysisTimeMs: Date.now() - startTime
        }
      }
      
      return result
    } catch (error) {
      throw new Error(`表血缘分析失败: ${error}`)
    }
  }

  // 追踪写入路径（数据来源）
  async traceWritePath(
    field: FieldReference, 
    config: LineageAnalysisConfig = this.defaultConfig
  ): Promise<LineagePath[]> {
    const paths: LineagePath[] = []
    const visited = new Set<string>()
    
    await this.dfsWritePath(field, [], visited, paths, config.maxDepth)
    
    return paths
      .filter(path => path.confidence >= config.minConfidence)
      .sort((a, b) => b.confidence - a.confidence)
      .slice(0, 10) // 限制返回路径数量
  }

  // 追踪使用路径（数据去向）
  async traceUsagePath(
    field: FieldReference, 
    config: LineageAnalysisConfig = this.defaultConfig
  ): Promise<LineagePath[]> {
    const paths: LineagePath[] = []
    const visited = new Set<string>()
    
    await this.dfsUsagePath(field, [], visited, paths, config.maxDepth)
    
    return paths
      .filter(path => path.confidence >= config.minConfidence)
      .sort((a, b) => b.confidence - a.confidence)
      .slice(0, 10) // 限制返回路径数量
  }

  // 影响分析
  async analyzeImpact(
    field: FieldReference, 
    config: LineageAnalysisConfig = this.defaultConfig
  ): Promise<LineageAnalysisResult> {
    // 主要分析下游影响
    const downstreamPaths = await this.traceUsagePath(field, config)
    
    const allNodes = this.extractNodesFromPaths(downstreamPaths)
    const allEdges = this.buildEdgesFromPaths(downstreamPaths)
    const lineageGraph = this.buildLineageGraph(allNodes, allEdges)
    
    return {
      targetField: field,
      analysisType: 'IMPACT_ANALYSIS',
      lineageGraph,
      paths: downstreamPaths,
      statistics: {
        totalPaths: downstreamPaths.length,
        averagePathLength: this.calculateAveragePathLength(downstreamPaths),
        highConfidencePaths: downstreamPaths.filter(p => p.confidence > 0.8).length,
        analysisTimeMs: 0
      },
      recommendations: [`字段 ${field.schema}.${field.table}.${field.column} 的变更将影响 ${downstreamPaths.length} 个下游路径`],
      warnings: downstreamPaths.length > 5 ? ['影响范围较大，建议谨慎操作'] : []
    }
  }

  // 构建血缘图
  buildLineageGraph(nodes: LineageNode[], edges: LineageEdge[]): LineageGraph {
    const graphNodes: LineageGraphNode[] = nodes.map((node, index) => ({
      id: node.id,
      label: `${node.table.name}.${node.field.column}`,
      type: 'FIELD',
      level: index,
      metadata: {
        schema: node.field.schema,
        table: node.field.table,
        column: node.field.column,
        dataType: node.metadata?.dataType || 'VARCHAR',
        businessMeaning: node.metadata?.businessMeaning || '',
        confidence: node.confidence,
        operations: [node.operation]
      },
      style: {
        color: this.getNodeColor(node.confidence),
        size: Math.max(20, node.confidence * 40),
        shape: 'circle'
      }
    }))

    const graphEdges: LineageGraphEdge[] = edges.map(edge => ({
      id: edge.id,
      source: edge.sourceNodeId,
      target: edge.targetNodeId,
      type: edge.relationshipType as any,
      confidence: edge.confidence,
      label: edge.relationshipType,
      metadata: {
        sqlContext: edge.sqlContext,
        transformationLogic: edge.transformationLogic,
        relationshipType: edge.relationshipType
      },
      style: {
        color: this.getEdgeColor(edge.confidence),
        width: Math.max(1, edge.confidence * 3),
        dashArray: edge.relationshipType === 'TRANSFORMATION' ? '5,5' : undefined
      }
    }))

    return {
      nodes: graphNodes,
      edges: graphEdges,
      metadata: {
        totalNodes: graphNodes.length,
        totalEdges: graphEdges.length,
        maxDepth: Math.max(...graphNodes.map(n => n.level)),
        analysisTimestamp: new Date(),
        confidenceDistribution: this.calculateConfidenceDistribution(nodes)
      }
    }
  }

  // 私有辅助方法
  private async dfsWritePath(
    field: FieldReference,
    currentPath: LineageNode[],
    visited: Set<string>,
    paths: LineagePath[],
    maxDepth: number
  ): Promise<void> {
    if (currentPath.length >= maxDepth) return
    
    const fieldKey = `${field.schema}.${field.table}.${field.column}`
    if (visited.has(fieldKey)) return
    
    visited.add(fieldKey)
    
    // 生成Mock上游节点
    const upstreamNodes = this.mockDataGenerator.generateMockLineageNodes(field, 2)
    
    for (const node of upstreamNodes) {
      const newPath = [...currentPath, node]
      
      if (Math.random() > 0.7) { // 30%概率到达源头
        paths.push({
          id: `write_path_${paths.length}`,
          path: newPath,
          pathType: 'WRITE_PATH',
          totalHops: newPath.length,
          confidence: this.calculatePathConfidence(newPath),
          startField: newPath[0]?.field || field,
          endField: field,
          pathDescription: `数据写入路径 ${newPath.length} 步`
        })
      } else {
        // 继续向上追溯
        await this.dfsWritePath(node.field, newPath, visited, paths, maxDepth)
      }
    }
    
    visited.delete(fieldKey)
  }

  private async dfsUsagePath(
    field: FieldReference,
    currentPath: LineageNode[],
    visited: Set<string>,
    paths: LineagePath[],
    maxDepth: number
  ): Promise<void> {
    if (currentPath.length >= maxDepth) return
    
    const fieldKey = `${field.schema}.${field.table}.${field.column}`
    if (visited.has(fieldKey)) return
    
    visited.add(fieldKey)
    
    // 生成Mock下游节点
    const downstreamNodes = this.mockDataGenerator.generateMockLineageNodes(field, 2)
    
    for (const node of downstreamNodes) {
      const newPath = [...currentPath, node]
      
      if (Math.random() > 0.6) { // 40%概率到达终点
        paths.push({
          id: `usage_path_${paths.length}`,
          path: newPath,
          pathType: 'USAGE_PATH',
          totalHops: newPath.length,
          confidence: this.calculatePathConfidence(newPath),
          startField: field,
          endField: newPath[newPath.length - 1]?.field || field,
          pathDescription: `数据使用路径 ${newPath.length} 步`
        })
      } else {
        // 继续向下追溯
        await this.dfsUsagePath(node.field, newPath, visited, paths, maxDepth)
      }
    }
    
    visited.delete(fieldKey)
  }

  private extractNodesFromPaths(paths: LineagePath[]): LineageNode[] {
    const nodeMap = new Map<string, LineageNode>()
    
    paths.forEach(path => {
      path.path.forEach(node => {
        nodeMap.set(node.id, node)
      })
    })
    
    return Array.from(nodeMap.values())
  }

  private buildEdgesFromPaths(paths: LineagePath[]): LineageEdge[] {
    const edges: LineageEdge[] = []
    
    paths.forEach(path => {
      for (let i = 0; i < path.path.length - 1; i++) {
        const sourceNode = path.path[i]
        const targetNode = path.path[i + 1]
        
        edges.push({
          id: `edge_${sourceNode.id}_${targetNode.id}`,
          sourceNodeId: sourceNode.id,
          targetNodeId: targetNode.id,
          relationshipType: this.inferRelationshipType(sourceNode, targetNode),
          confidence: Math.min(sourceNode.confidence, targetNode.confidence),
          sqlContext: targetNode.sqlContext,
          metadata: {
            description: `从 ${sourceNode.field.column} 到 ${targetNode.field.column}`,
            transformationFunction: this.inferTransformationFunction(sourceNode, targetNode)
          }
        })
      }
    })
    
    return edges
  }

  private calculatePathConfidence(path: LineageNode[]): number {
    if (path.length === 0) return 0
    return path.reduce((sum, node) => sum + node.confidence, 0) / path.length
  }

  private calculateAveragePathLength(paths: LineagePath[]): number {
    if (paths.length === 0) return 0
    return paths.reduce((sum, path) => sum + path.totalHops, 0) / paths.length
  }

  private generateMockTableFields(table: TableReference): FieldReference[] {
    const commonFields = ['id', 'name', 'status', 'created_at', 'updated_at', 'amount', 'description']
    return commonFields.map(column => ({
      schema: table.schema,
      table: table.name,
      column
    }))
  }

  private getNodeColor(confidence: number): string {
    if (confidence > 0.8) return '#10B981' // 绿色 - 高置信度
    if (confidence > 0.6) return '#F59E0B' // 黄色 - 中等置信度
    return '#EF4444' // 红色 - 低置信度
  }

  private getEdgeColor(confidence: number): string {
    if (confidence > 0.8) return '#059669'
    if (confidence > 0.6) return '#D97706'
    return '#DC2626'
  }

  private calculateConfidenceDistribution(nodes: LineageNode[]) {
    const total = nodes.length
    const high = nodes.filter(n => n.confidence > 0.8).length
    const medium = nodes.filter(n => n.confidence >= 0.5 && n.confidence <= 0.8).length
    const low = nodes.filter(n => n.confidence < 0.5).length
    
    return {
      high: total > 0 ? high / total : 0,
      medium: total > 0 ? medium / total : 0,
      low: total > 0 ? low / total : 0
    }
  }

  private inferRelationshipType(sourceNode: LineageNode, targetNode: LineageNode): 'DIRECT_COPY' | 'TRANSFORMATION' | 'AGGREGATION' | 'JOIN' | 'FILTER' | 'UNION' {
    // 简单的推断逻辑
    if (sourceNode.operation === 'SELECT' && targetNode.operation === 'INSERT') {
      return 'DIRECT_COPY'
    }
    if (targetNode.sqlContext.includes('SUM(') || targetNode.sqlContext.includes('COUNT(')) {
      return 'AGGREGATION'
    }
    if (targetNode.sqlContext.includes('JOIN')) {
      return 'JOIN'
    }
    if (targetNode.sqlContext.includes('WHERE')) {
      return 'FILTER'
    }
    return 'TRANSFORMATION'
  }

  private inferTransformationFunction(sourceNode: LineageNode, targetNode: LineageNode): string {
    if (targetNode.sqlContext.includes('UPPER(')) return 'UPPER'
    if (targetNode.sqlContext.includes('SUM(')) return 'SUM'
    if (targetNode.sqlContext.includes('COUNT(')) return 'COUNT'
    return 'DIRECT'
  }

  private generateRecommendations(field: FieldReference, paths: LineagePath[]): string[] {
    const recommendations: string[] = []
    
    if (paths.length === 0) {
      recommendations.push('未发现血缘关系，建议检查字段名称或扩大搜索范围')
    }
    
    const lowConfidencePaths = paths.filter(p => p.confidence < 0.7)
    if (lowConfidencePaths.length > 0) {
      recommendations.push(`发现 ${lowConfidencePaths.length} 个低置信度路径，建议人工验证`)
    }
    
    const longPaths = paths.filter(p => p.totalHops > 5)
    if (longPaths.length > 0) {
      recommendations.push(`发现 ${longPaths.length} 个较长的血缘路径，可能存在数据质量风险`)
    }
    
    return recommendations
  }

  private generateWarnings(paths: LineagePath[]): string[] {
    const warnings: string[] = []
    
    const veryLowConfidencePaths = paths.filter(p => p.confidence < 0.5)
    if (veryLowConfidencePaths.length > 0) {
      warnings.push(`警告：发现 ${veryLowConfidencePaths.length} 个极低置信度路径`)
    }
    
    if (paths.length > 20) {
      warnings.push('警告：血缘关系过于复杂，可能影响性能')
    }
    
    return warnings
  }
}

// 导出单例实例
export const lineageAnalyzer = new LineageAnalyzer()
