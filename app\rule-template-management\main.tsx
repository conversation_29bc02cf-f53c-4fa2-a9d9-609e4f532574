"use client"

import { useState } from "react"
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Table, TableHeader, TableRow, TableHead, TableBody, TableCell } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Plus, Eye, Edit, Trash2 } from "lucide-react"

const mockTemplates = [
  {
    id: "ev_charging_anomaly",
    name: "电动汽车充电桩电量异常",
    description: "针对充电桩用户的电量异常监控",
    category: "用电异常",
    structure: {
      scenario: "电动汽车充电桩电量异常",
      targetUsers: ["营销业务用户"],
      baseConditions: ["用户名称包含'充电'字段", "用电地址包含'充电'字段", "电价执行居民合表电价"],
      branchConditions: [
        { condition: "抄表周期为'单月'", threshold: "5000", unit: "千瓦时" },
        { condition: "抄表周期为'双月'", threshold: "10000", unit: "千瓦时" },
      ],
    },
    creator: "张三",
    createTime: "2024-06-01 10:00",
    status: "启用"
  },
  {
    id: "high_consumption_commercial",
    name: "工商业高耗能异常",
    description: "工商业用户异常高耗能监控",
    category: "用电异常",
    structure: {
      scenario: "工商业高耗能异常",
      targetUsers: ["工商业用户"],
      baseConditions: ["客户类型为'工商业'", "电压等级为'10kV'或'35kV'"],
      branchConditions: [
        { condition: "行业类型为'制造业'", threshold: "50000", unit: "千瓦时" },
        { condition: "行业类型为'服务业'", threshold: "20000", unit: "千瓦时" },
      ],
    },
    creator: "李四",
    createTime: "2024-06-02 09:30",
    status: "启用"
  },
  {
    id: "power_factor_anomaly",
    name: "功率因数不达标",
    description: "功率因数低于标准值的监控",
    category: "电能质量",
    structure: {
      scenario: "功率因数不达标",
      targetUsers: ["工商业用户", "大工业用户"],
      baseConditions: ["电压等级为'10kV'以上", "用电性质为'生产用电'"],
      branchConditions: [
        { condition: "装机容量≥100kW", threshold: "0.9", unit: "功率因数" },
        { condition: "装机容量<100kW", threshold: "0.85", unit: "功率因数" },
      ],
    },
    creator: "王五",
    createTime: "2024-06-03 14:20",
    status: "停用"
  },
]

export default function RuleTemplateManagement() {
  const [selectedTemplate, setSelectedTemplate] = useState<any>(null)

  return (
    <div className="flex-1 space-y-6 p-6">
      <div className="flex items-center justify-between">
        {/* <div>
          <h1 className="text-3xl font-bold tracking-tight">稽查主题设计模板</h1>
          <p className="text-gray-600">集中管理所有稽查规则模板，支持新建、编辑、查看和删除</p>
        </div> */}
        <Button>
          <Plus className="h-4 w-4 mr-2" />新建模板
        </Button>
      </div>
      <Card>
        <CardHeader>
          <CardTitle>模板列表</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>模板名称</TableHead>
                <TableHead>描述</TableHead>
                <TableHead>分类</TableHead>
                <TableHead>创建人</TableHead>
                <TableHead>创建时间</TableHead>
                <TableHead>状态</TableHead>
                <TableHead>操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {mockTemplates.map((tpl) => (
                <TableRow key={tpl.id}>
                  <TableCell>{tpl.name}</TableCell>
                  <TableCell>{tpl.description}</TableCell>
                  <TableCell>{tpl.category}</TableCell>
                  <TableCell>{tpl.creator}</TableCell>
                  <TableCell>{tpl.createTime}</TableCell>
                  <TableCell><Badge variant={tpl.status === "启用" ? "default" : "secondary"}>{tpl.status}</Badge></TableCell>
                  <TableCell>
                    <Button size="sm" variant="ghost" onClick={() => setSelectedTemplate(tpl)}><Eye className="h-4 w-4" /></Button>
                    <Button size="sm" variant="ghost"><Edit className="h-4 w-4" /></Button>
                    <Button size="sm" variant="ghost"><Trash2 className="h-4 w-4" /></Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
      {/* 模板详情弹窗 */}
      <Dialog open={!!selectedTemplate} onOpenChange={() => setSelectedTemplate(null)}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>模板详情：{selectedTemplate?.name}</DialogTitle>
          </DialogHeader>
          {selectedTemplate && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <div className="text-sm text-gray-500">分类</div>
                  <div className="font-medium">{selectedTemplate.category}</div>
                </div>
                <div>
                  <div className="text-sm text-gray-500">状态</div>
                  <div className="font-medium">{selectedTemplate.status}</div>
                </div>
                <div>
                  <div className="text-sm text-gray-500">创建人</div>
                  <div className="font-medium">{selectedTemplate.creator}</div>
                </div>
                <div>
                  <div className="text-sm text-gray-500">创建时间</div>
                  <div className="font-medium">{selectedTemplate.createTime}</div>
                </div>
              </div>
              <div>
                <div className="text-sm text-gray-500 mb-1">模板描述</div>
                <div className="bg-gray-50 p-2 rounded text-sm">{selectedTemplate.description}</div>
              </div>
              <div>
                <div className="text-sm text-gray-500 mb-1">结构定义</div>
                <div className="bg-gray-50 p-2 rounded text-sm">
                  <div>业务场景：{selectedTemplate.structure.scenario}</div>
                  <div>筛查对象：{selectedTemplate.structure.targetUsers.join("、")}</div>
                  <div>基础条件：</div>
                  <ul className="list-disc ml-6">
                    {selectedTemplate.structure.baseConditions.map((c: string, i: number) => (
                      <li key={i}>{c}</li>
                    ))}
                  </ul>
                  <div>分支条件：</div>
                  <ul className="list-disc ml-6">
                    {selectedTemplate.structure.branchConditions.map((b: any, i: number) => (
                      <li key={i}>{b.condition}，阈值：{b.threshold}{b.unit}</li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
} 