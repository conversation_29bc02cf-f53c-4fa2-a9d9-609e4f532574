import React, { useState } from "react"
import { <PERSON>, CardHeader, Card<PERSON><PERSON><PERSON>, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger, Ta<PERSON>Content } from "@/components/ui/tabs"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { 
  FileText, 
  GitCompare, 
  Plus, 
  Minus, 
  Edit3,
  Eye,
  ArrowRight,
  Calendar,
  Users,
  Target,
  Activity
} from "lucide-react"

// 主题版本接口
interface ThemeVersion {
  id: string
  version: string
  createdAt: string
  description: string
  targetUsers: string[]
  baseConditions: any[]
  branchConditions: any[]
  hitCount: number
  executionCount: number
  status: 'draft' | 'published' | 'archived'
  changes: {
    type: 'added' | 'removed' | 'modified'
    field: string
    oldValue?: string
    newValue?: string
    description: string
  }[]
}

// 主题演进明细组件
interface ThemeEvolutionDetailProps {
  themeId: string
  themeName: string
  versions: ThemeVersion[]
  onClose: () => void
}

const ThemeEvolutionDetail: React.FC<ThemeEvolutionDetailProps> = ({
  themeId,
  themeName,
  versions,
  onClose
}) => {
  const [selectedVersion1, setSelectedVersion1] = useState<string>("")
  const [selectedVersion2, setSelectedVersion2] = useState<string>("")
  const [compareMode, setCompareMode] = useState<'side-by-side' | 'unified'>('side-by-side')

  // 按时间排序版本
  const sortedVersions = [...versions].sort((a, b) => 
    new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
  )

  // 获取版本变化统计
  const getVersionStats = (version: ThemeVersion) => {
    const added = version.changes.filter(c => c.type === 'added').length
    const removed = version.changes.filter(c => c.type === 'removed').length
    const modified = version.changes.filter(c => c.type === 'modified').length
    
    return { added, removed, modified }
  }

  // 高亮显示变化
  const highlightChanges = (text: string, changes: any[]) => {
    let highlightedText = text
    
    changes.forEach(change => {
      if (change.type === 'added') {
        highlightedText = highlightedText.replace(
          change.newValue,
          `<span class="bg-green-100 text-green-800 px-1 rounded font-medium">${change.newValue}</span>`
        )
      } else if (change.type === 'removed') {
        highlightedText = highlightedText.replace(
          change.oldValue,
          `<span class="bg-red-100 text-red-800 px-1 rounded line-through font-medium">${change.oldValue}</span>`
        )
      } else if (change.type === 'modified') {
        highlightedText = highlightedText.replace(
          change.oldValue,
          `<span class="bg-yellow-100 text-yellow-800 px-1 rounded font-medium">${change.oldValue} → ${change.newValue}</span>`
        )
      }
    })
    
    return highlightedText
  }

  // 渲染条件对比
  const renderConditionsComparison = (v1: ThemeVersion, v2: ThemeVersion) => {
    const allConditions = new Set([
      ...v1.baseConditions.map(c => c.label),
      ...v2.baseConditions.map(c => c.label)
    ])

    return Array.from(allConditions).map(condition => {
      const inV1 = v1.baseConditions.find(c => c.label === condition)
      const inV2 = v2.baseConditions.find(c => c.label === condition)
      
      let status = 'unchanged'
      if (!inV1 && inV2) status = 'added'
      if (inV1 && !inV2) status = 'removed'
      if (inV1 && inV2 && JSON.stringify(inV1) !== JSON.stringify(inV2)) status = 'modified'

      return (
        <div key={condition} className="flex items-center gap-4 p-3 border rounded-lg">
          <div className="flex-1">
            <div className="flex items-center gap-2">
              {status === 'added' && <Plus className="h-4 w-4 text-green-500" />}
              {status === 'removed' && <Minus className="h-4 w-4 text-red-500" />}
              {status === 'modified' && <Edit3 className="h-4 w-4 text-yellow-500" />}
              {status === 'unchanged' && <div className="h-4 w-4" />}
              <span className="font-medium">{condition}</span>
              <Badge 
                variant={status === 'added' ? 'default' : 
                       status === 'removed' ? 'destructive' : 
                       status === 'modified' ? 'secondary' : 'outline'}
              >
                {status === 'added' ? '新增' : 
                 status === 'removed' ? '删除' : 
                 status === 'modified' ? '修改' : '未变'}
              </Badge>
            </div>
          </div>
          <div className="flex-1 text-sm text-gray-600">
            {inV1 && (
              <div className="mb-1">
                <span className="text-gray-500">v{v1.version}:</span> {inV1.operator} {inV1.value} {inV1.unit}
              </div>
            )}
            {inV2 && (
              <div>
                <span className="text-gray-500">v{v2.version}:</span> {inV2.operator} {inV2.value} {inV2.unit}
              </div>
            )}
          </div>
        </div>
      )
    })
  }

  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogContent className="max-w-7xl max-h-[95vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <GitCompare className="h-5 w-5" />
            主题演进明细 - {themeName}
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-6">
          {/* 版本选择器 */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">版本对比设置</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center gap-4 mb-4">
                <Select value={selectedVersion1} onValueChange={setSelectedVersion1}>
                  <SelectTrigger className="w-64">
                    <SelectValue placeholder="选择版本1" />
                  </SelectTrigger>
                  <SelectContent>
                    {sortedVersions.map(version => (
                      <SelectItem key={version.id} value={version.id}>
                        v{version.version} - {new Date(version.createdAt).toLocaleDateString()} ({version.status})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <ArrowRight className="h-4 w-4 text-gray-400" />
                <Select value={selectedVersion2} onValueChange={setSelectedVersion2}>
                  <SelectTrigger className="w-64">
                    <SelectValue placeholder="选择版本2" />
                  </SelectTrigger>
                  <SelectContent>
                    {sortedVersions.map(version => (
                      <SelectItem key={version.id} value={version.id}>
                        v{version.version} - {new Date(version.createdAt).toLocaleDateString()} ({version.status})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div className="flex items-center gap-4">
                <Button
                  variant={compareMode === 'side-by-side' ? 'default' : 'outline'}
                  onClick={() => setCompareMode('side-by-side')}
                >
                  并排对比
                </Button>
                <Button
                  variant={compareMode === 'unified' ? 'default' : 'outline'}
                  onClick={() => setCompareMode('unified')}
                >
                  统一视图
                </Button>
              </div>
            </CardContent>
          </Card>

          {selectedVersion1 && selectedVersion2 && (
            <Tabs defaultValue="overview" className="w-full">
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="overview">概览对比</TabsTrigger>
                <TabsTrigger value="conditions">条件对比</TabsTrigger>
                <TabsTrigger value="description">描述对比</TabsTrigger>
                <TabsTrigger value="metrics">指标对比</TabsTrigger>
              </TabsList>

              <TabsContent value="overview" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Eye className="h-4 w-4" />
                      版本概览对比
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    {(() => {
                      const v1 = sortedVersions.find(v => v.id === selectedVersion1)
                      const v2 = sortedVersions.find(v => v.id === selectedVersion2)
                      if (!v1 || !v2) return <div>请选择要对比的版本</div>

                      const stats1 = getVersionStats(v1)
                      const stats2 = getVersionStats(v2)

                      return (
                        <div className="grid grid-cols-2 gap-6">
                          {/* 版本1 */}
                          <div className="space-y-4">
                            <div className="flex items-center gap-2">
                              <Badge variant={v1.status === 'published' ? 'default' : 'secondary'}>
                                v{v1.version}
                              </Badge>
                              <span className="text-sm text-gray-500">
                                {new Date(v1.createdAt).toLocaleDateString()}
                              </span>
                            </div>
                            
                            <div className="space-y-2">
                              <div className="flex items-center gap-2">
                                <Users className="h-4 w-4 text-gray-400" />
                                <span className="text-sm">目标用户: {v1.targetUsers.join(', ')}</span>
                              </div>
                              <div className="flex items-center gap-2">
                                <Activity className="h-4 w-4 text-gray-400" />
                                <span className="text-sm">命中: {v1.hitCount} | 执行: {v1.executionCount}</span>
                              </div>
                              <div className="flex items-center gap-2">
                                <FileText className="h-4 w-4 text-gray-400" />
                                <span className="text-sm">变更: +{stats1.added} -{stats1.removed} ~{stats1.modified}</span>
                              </div>
                            </div>
                          </div>

                          {/* 版本2 */}
                          <div className="space-y-4">
                            <div className="flex items-center gap-2">
                              <Badge variant={v2.status === 'published' ? 'default' : 'secondary'}>
                                v{v2.version}
                              </Badge>
                              <span className="text-sm text-gray-500">
                                {new Date(v2.createdAt).toLocaleDateString()}
                              </span>
                            </div>
                            
                            <div className="space-y-2">
                              <div className="flex items-center gap-2">
                                <Users className="h-4 w-4 text-gray-400" />
                                <span className="text-sm">目标用户: {v2.targetUsers.join(', ')}</span>
                              </div>
                              <div className="flex items-center gap-2">
                                <Activity className="h-4 w-4 text-gray-400" />
                                <span className="text-sm">命中: {v2.hitCount} | 执行: {v2.executionCount}</span>
                              </div>
                              <div className="flex items-center gap-2">
                                <FileText className="h-4 w-4 text-gray-400" />
                                <span className="text-sm">变更: +{stats2.added} -{stats2.removed} ~{stats2.modified}</span>
                              </div>
                            </div>
                          </div>
                        </div>
                      )
                    })()}
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="conditions" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Target className="h-4 w-4" />
                      条件对比
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    {(() => {
                      const v1 = sortedVersions.find(v => v.id === selectedVersion1)
                      const v2 = sortedVersions.find(v => v.id === selectedVersion2)
                      if (!v1 || !v2) return <div>请选择要对比的版本</div>

                      return (
                        <div className="space-y-4">
                          <h4 className="font-medium">基础条件对比</h4>
                          {renderConditionsComparison(v1, v2)}
                        </div>
                      )
                    })()}
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="description" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <FileText className="h-4 w-4" />
                      描述对比
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    {(() => {
                      const v1 = sortedVersions.find(v => v.id === selectedVersion1)
                      const v2 = sortedVersions.find(v => v.id === selectedVersion2)
                      if (!v1 || !v2) return <div>请选择要对比的版本</div>

                      return (
                        <div className="grid grid-cols-2 gap-6">
                          <div>
                            <h4 className="font-medium mb-2">v{v1.version} 描述</h4>
                            <div 
                              className="p-4 bg-gray-50 rounded-lg text-sm whitespace-pre-line"
                              dangerouslySetInnerHTML={{ __html: highlightChanges(v1.description, v1.changes) }}
                            />
                          </div>
                          <div>
                            <h4 className="font-medium mb-2">v{v2.version} 描述</h4>
                            <div 
                              className="p-4 bg-gray-50 rounded-lg text-sm whitespace-pre-line"
                              dangerouslySetInnerHTML={{ __html: highlightChanges(v2.description, v2.changes) }}
                            />
                          </div>
                        </div>
                      )
                    })()}
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="metrics" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Activity className="h-4 w-4" />
                      指标对比
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    {(() => {
                      const v1 = sortedVersions.find(v => v.id === selectedVersion1)
                      const v2 = sortedVersions.find(v => v.id === selectedVersion2)
                      if (!v1 || !v2) return <div>请选择要对比的版本</div>

                      const rate1 = v1.executionCount > 0 ? (v1.hitCount / v1.executionCount) * 100 : 0
                      const rate2 = v2.executionCount > 0 ? (v2.hitCount / v2.executionCount) * 100 : 0

                      return (
                        <div className="space-y-6">
                          <div className="grid grid-cols-2 gap-6">
                            <div className="space-y-4">
                              <h4 className="font-medium">v{v1.version} 指标</h4>
                              <div className="space-y-3">
                                <div className="flex items-center justify-between">
                                  <span className="text-sm">命中率</span>
                                  <div className="flex items-center gap-2">
                                    <div className="w-32 bg-gray-200 rounded-full h-2">
                                      <div 
                                        className="bg-blue-600 h-2 rounded-full" 
                                        style={{ width: `${Math.min(rate1, 100)}%` }}
                                      ></div>
                                    </div>
                                    <span className="text-sm font-medium">{rate1.toFixed(1)}%</span>
                                  </div>
                                </div>
                                <div className="flex items-center justify-between">
                                  <span className="text-sm">命中数</span>
                                  <span className="text-sm font-medium">{v1.hitCount}</span>
                                </div>
                                <div className="flex items-center justify-between">
                                  <span className="text-sm">执行数</span>
                                  <span className="text-sm font-medium">{v1.executionCount}</span>
                                </div>
                              </div>
                            </div>
                            <div className="space-y-4">
                              <h4 className="font-medium">v{v2.version} 指标</h4>
                              <div className="space-y-3">
                                <div className="flex items-center justify-between">
                                  <span className="text-sm">命中率</span>
                                  <div className="flex items-center gap-2">
                                    <div className="w-32 bg-gray-200 rounded-full h-2">
                                      <div 
                                        className="bg-green-600 h-2 rounded-full" 
                                        style={{ width: `${Math.min(rate2, 100)}%` }}
                                      ></div>
                                    </div>
                                    <span className="text-sm font-medium">{rate2.toFixed(1)}%</span>
                                  </div>
                                </div>
                                <div className="flex items-center justify-between">
                                  <span className="text-sm">命中数</span>
                                  <span className="text-sm font-medium">{v2.hitCount}</span>
                                </div>
                                <div className="flex items-center justify-between">
                                  <span className="text-sm">执行数</span>
                                  <span className="text-sm font-medium">{v2.executionCount}</span>
                                </div>
                              </div>
                            </div>
                          </div>
                          
                          <div className="border-t pt-4">
                            <h4 className="font-medium mb-3">变化分析</h4>
                            <div className="space-y-2 text-sm">
                              <div className="flex items-center justify-between">
                                <span>命中率变化</span>
                                <span className={`font-medium ${rate2 > rate1 ? 'text-green-600' : rate2 < rate1 ? 'text-red-600' : 'text-gray-600'}`}>
                                  {rate2 > rate1 ? '+' : ''}{(rate2 - rate1).toFixed(1)}%
                                </span>
                              </div>
                              <div className="flex items-center justify-between">
                                <span>命中数变化</span>
                                <span className={`font-medium ${v2.hitCount > v1.hitCount ? 'text-green-600' : v2.hitCount < v1.hitCount ? 'text-red-600' : 'text-gray-600'}`}>
                                  {v2.hitCount > v1.hitCount ? '+' : ''}{v2.hitCount - v1.hitCount}
                                </span>
                              </div>
                              <div className="flex items-center justify-between">
                                <span>执行数变化</span>
                                <span className={`font-medium ${v2.executionCount > v1.executionCount ? 'text-green-600' : v2.executionCount < v1.executionCount ? 'text-red-600' : 'text-gray-600'}`}>
                                  {v2.executionCount > v1.executionCount ? '+' : ''}{v2.executionCount - v1.executionCount}
                                </span>
                              </div>
                            </div>
                          </div>
                        </div>
                      )
                    })()}
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          )}
        </div>
      </DialogContent>
    </Dialog>
  )
}

export default ThemeEvolutionDetail 