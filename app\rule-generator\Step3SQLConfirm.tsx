import React from "react"
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Ta<PERSON>, <PERSON><PERSON>List, TabsTrigger, TabsContent } from "@/components/ui/tabs"
import { DragDropContext, Droppable, Draggable, DroppableProvided, DraggableProvided, DraggableStateSnapshot } from 'react-beautiful-dnd'
import { TrendingUp } from "lucide-react"
import ScriptNamingChecker from './ScriptNamingChecker'
import { Dialog, DialogTrigger, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"


interface Step3SQLConfirmProps {
  generatedDescription: string
  semanticResult: string
  sqlResult: string
  setSqlResult: (s: string) => void
  prevStep: () => void
  semanticNLResult: string
  resultFields: { name: string }[]
  isIteratingExistingRule?: boolean
  onShowEvolutionAnalysis?: () => void
}


const Step3SQLConfirm: React.FC<Step3SQLConfirmProps> = (props) => {
  // mock SQL explain 语义化中文解释
  const explainSteps = [
    "1. 从客户信息表（customers）中选取客户ID、客户名称、地址等信息。",
    "2. 关联电表信息表（meters），获取客户的电表信息。",
    "3. 关联用电量明细表（meter_readings），统计每月用电量。",
    "4. 关联电价策略表（price_policies），获取价格类型。",
    "5. 过滤条件：本月内，客户名称或地址包含'充电'，价格类型为'居民合表电价'。",
    "6. 按客户、地址、周期、价格类型分组，统计用电量。",
    "7. 只保留单月用电量大于5000或双月大于10000的记录。"
  ]

  // 组件内useState
  const [showNamingChecker, setShowNamingChecker] = React.useState(false)
  const [checked, setChecked] = React.useState(false)

  // 只读目标字段拖拽排序
  const [fields, setFields] = React.useState<Array<{ name: string }>>(props.resultFields || [])
  React.useEffect(() => { setFields(props.resultFields || []) }, [props.resultFields])
  const onDragEnd = (result: any) => {
    if (!result.destination) return
    const newOrder = Array.from(fields)
    const [removed] = newOrder.splice(result.source.index, 1)
    newOrder.splice(result.destination.index, 0, removed)
    setFields(newOrder)
  }

  React.useEffect(() => {
    if (!checked) {
      setChecked(true)
    }
  }, [])

  return (
    <div className="space-y-6">
      {/* 顶部横向布局：主题描述+目标字段 */}
      <div className="flex flex-col md:flex-row gap-4">
        <div className="flex-1 md:basis-1/3 min-w-0">
          <Card className="bg-gradient-to-br from-blue-50 to-blue-100 border-blue-100 rounded-xl transition-colors duration-300 shadow-sm hover:shadow-lg h-full">
            <CardHeader className="pb-3">
              <CardTitle className="text-blue-500 text-sm flex items-center gap-2">主题规则自然语言定义</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="bg-blue-100 p-4 rounded-lg border min-h-24 border-blue-200 text-blue-900 transition-colors duration-300 whitespace-pre-line">{props.generatedDescription}</div>
            </CardContent>
          </Card>
        </div>
        <div className="flex-1 md:basis-2/3 min-w-0">
          <Card className="bg-gradient-to-br from-blue-50 to-blue-100 border-blue-100 rounded-xl shadow-sm hover:shadow-lg h-full">
            <CardHeader className="pb-3">
              <CardTitle className="text-blue-500 text-sm flex items-center gap-2">目标结果字段 <span className="text-xs text-blue-400">（可拖拽排序，仅展示）</span></CardTitle>
            </CardHeader>
            <CardContent>
              <div className="max-h-[360px] overflow-auto">
                <DragDropContext onDragEnd={onDragEnd}>
                  <Droppable droppableId="readonly-droppable">
                    {(provided: DroppableProvided) => (
                      <div ref={provided.innerRef} {...provided.droppableProps}>
                        {(fields || []).map((f, idx) => (
                          <Draggable key={f.name} draggableId={f.name} index={idx}>
                            {(provided: DraggableProvided, snapshot: DraggableStateSnapshot) => (
                              <div
                                ref={provided.innerRef}
                                {...provided.draggableProps}
                                {...provided.dragHandleProps}
                                className={`flex items-center gap-2 py-1 px-2 mb-1 bg-white rounded border border-blue-100 select-none ${snapshot.isDragging ? 'shadow-lg' : ''}`}
                                style={{ cursor: 'move', ...provided.draggableProps.style }}
                              >
                                <span>{f.name}</span>
                              </div>
                            )}
                          </Draggable>
                        ))}
                        {provided.placeholder}
                        {(fields || []).length === 0 && <div className="text-xs text-blue-300 py-2">无</div>}
                      </div>
                    )}
                  </Droppable>
                </DragDropContext>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
      
      {/* 脚本命名规范检查-删除原有平铺ScriptNamingChecker */}
      {/* {props.sqlResult && (
        <ScriptNamingChecker
          sqlScript={props.sqlResult}
          scenario={props.generatedDescription.split('，')[0] || '数据巡查'}
          onScriptUpdate={props.setSqlResult}
        />
      )} */}
      
      <Card className="bg-gradient-to-br from-blue-50 to-blue-100 border-blue-100 rounded-xl transition-colors duration-300 shadow-sm hover:shadow-lg">
        <CardHeader className="pb-3">
          <CardTitle className="text-blue-500 text-sm flex items-center gap-2">主题规则语义化解释</CardTitle>
        </CardHeader>
        <CardContent>
          <pre className="text-xs bg-blue-100 p-3 rounded-lg border-blue-200 text-blue-900 transition-colors duration-300 overflow-x-auto">{props.semanticNLResult}</pre>
        </CardContent>
      </Card>
      <Card className="bg-gradient-to-br from-blue-50 to-blue-100 border-blue-100 rounded-xl transition-colors duration-300 shadow-sm hover:shadow-lg">
        <CardHeader className="pb-3">
          <CardTitle className="text-blue-500 text-sm flex items-center gap-2">可执行脚本信息
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="item-1" className="flex-1">
            <TabsList className="flex-1">
              <TabsTrigger value="item-1">物理可执行脚本</TabsTrigger>
              <TabsTrigger value="item-2">图谱解释</TabsTrigger>
            </TabsList>
            <TabsContent value="item-1">
              <pre className="text-xs bg-blue-100 p-3 rounded-lg border-blue-200 text-blue-900 transition-colors duration-300 overflow-x-auto">{props.sqlResult || '请点击下方按钮生成'}</pre>
            </TabsContent>
            <TabsContent value="item-2">
              <pre className="text-xs bg-blue-100 p-3 rounded-lg border-blue-200 text-blue-900 transition-colors duration-300 overflow-x-auto">{explainSteps.join('\n')}</pre>
            </TabsContent>
          </Tabs>
          <div className="mt-4 flex justify-end">
            <Dialog open={showNamingChecker} onOpenChange={setShowNamingChecker}>
              <DialogTrigger asChild>
                <Button
                  variant="outline"
                  className="bg-blue-100 text-blue-700 border-blue-200 hover:bg-blue-200 rounded-full"
                  onClick={() => setShowNamingChecker(true)}
                  disabled={!props.sqlResult}
                >
                  脚本命名规范审查
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-2xl">
                <DialogHeader>
                  <DialogTitle>脚本命名规范审查</DialogTitle>
                </DialogHeader>
                {checked ? (
                  <ScriptNamingChecker
                    sqlScript={props.sqlResult}
                    scenario={props.generatedDescription.split('，')[0] || '数据巡查'}
                    onScriptUpdate={props.setSqlResult}
                  />
                ) : (
                  <div>检查中...</div>
                )}
              </DialogContent>
            </Dialog>
          </div>
        </CardContent>
      </Card>
      <div className="flex gap-4 pt-4 border-t">
        <Button variant="outline" className="flex-1 rounded-full bg-blue-100 text-blue-700 border-blue-200 transition-colors duration-300 hover:bg-blue-200" onClick={props.prevStep}>上一步</Button>
        <Button className="flex-1 bg-blue-400/80 text-white hover:bg-blue-400 rounded-full transition-colors duration-300 shadow-sm hover:shadow-lg"
          onClick={() => props.setSqlResult(`SELECT * FROM table WHERE ... -- ${props.generatedDescription.slice(0, 10)}...`)}>
          生成脚本
        </Button>
        <Button className="flex-1 bg-green-500 text-white hover:bg-green-600 rounded-full transition-colors duration-300 shadow-sm hover:shadow-lg">
          完成
        </Button>
        {props.isIteratingExistingRule && (
          <Button 
            variant="outline"
            className="flex items-center gap-2 bg-purple-100 text-purple-700 border-purple-200 hover:bg-purple-200 rounded-full transition-colors duration-300"
            onClick={props.onShowEvolutionAnalysis}
          >
            <TrendingUp className="h-4 w-4" />
            历史演进趋势分析
          </Button>
        )}
      </div>
    </div>
  )
}

export default Step3SQLConfirm 