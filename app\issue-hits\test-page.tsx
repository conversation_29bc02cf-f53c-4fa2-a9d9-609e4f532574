"use client"

import React from "react"
import IssueHits from "./issue-hits"
import type { Issue } from "@/types/issue"

// 简化的测试数据
const testIssue: Issue = {
  id: 1,
  title: "测试问题",
  description: "这是一个测试问题，用于验证线索溯源功能。",
  status: "active",
  severity: "high",
  createdAt: "2024-01-15 10:30:00",
  updatedAt: "2024-01-15 14:20:00",
  clues: [
    {
      id: "test-clue-1",
      title: "测试线索1",
      description: "这是一个测试线索",
      confidence: 95,
      createdAt: "2024-01-15 10:30:00",
      evidence: ["测试证据1", "测试证据2"],
      ruleHit: {
        id: "test-rule-hit-1",
        ruleName: "测试规则",
        ruleId: "TEST_RULE_001",
        themeName: "测试主题",
        hitTime: "2024-01-15 10:30:00",
        dataSource: "test_db",
        tableName: "test_table",
        columnName: "test_column",
        condition: "test_column > 100",
        result: "发现测试异常",
        logicSteps: [
          {
            id: "test-step-1",
            stepName: "测试步骤1",
            description: "这是一个测试逻辑步骤",
            logicType: "validation",
            parameters: { field: "test_column", threshold: 100 },
            result: true,
            isHit: true,
            order: 1
          }
        ]
      }
    }
  ]
}

export default function TestPage() {
  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-6">线索溯源功能测试</h1>
      <IssueHits issue={testIssue} />
    </div>
  )
} 