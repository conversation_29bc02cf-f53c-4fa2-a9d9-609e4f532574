'use client'

import React from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Activity } from 'lucide-react'

export default function TestComponent() {
  return (
    <div className="container mx-auto p-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="w-5 h-5" />
            稽查主题运行分析 - 测试页面
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-gray-600 mb-4">
            这是一个测试页面，用于验证稽查主题运行分析功能是否正常工作。
          </p>
          <Button>测试按钮</Button>
        </CardContent>
      </Card>
    </div>
  )
} 