"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Progress } from "@/components/ui/progress"
import { 
  Brain, 
  Network, 
  Layers, 
  GitBranch, 
  TrendingUp, 
  RefreshCw,
  Search,
  Filter,
  Eye,
  Link
} from "lucide-react"

// 模拟数据
const mockData = {
  totalAtoms: 1250,
  totalFunctions: 320,
  totalRelations: 856,
  growthRate: 15.2,
  categories: [
    { name: "基础原子", count: 480, color: "bg-blue-500", relations: 156, description: "最基础的语义单元，不可再分解" },
    { name: "派生原子", count: 420, color: "bg-green-500", relations: 234, description: "基于基础原子派生的语义单元" },
    { name: "复合原子", count: 350, color: "bg-purple-500", relations: 466, description: "多个原子组合形成的复杂语义单元" }
  ],
  businessTypes: [
    { name: "电力营销-业扩用检", count: 280, color: "bg-orange-500", atoms: 156 },
    { name: "电力营销-营销稽查", count: 320, color: "bg-red-500", atoms: 234 },
    { name: "电力营销-计量管理", count: 195, color: "bg-yellow-500", atoms: 98 },
    { name: "电力营销-客户服务", count: 165, color: "bg-pink-500", atoms: 87 },
    { name: "电力营销-电费管理", count: 290, color: "bg-indigo-500", atoms: 281 }
  ],
  recentAtoms: [
    { name: "客户信用等级", type: "基础原子", usage: 45, status: "active", businessType: "电力营销-营销稽查" },
    { name: "交易风险系数", type: "派生原子", usage: 32, status: "active", businessType: "电力营销-业扩用检" },
    { name: "产品生命周期", type: "复合原子", usage: 28, status: "draft", businessType: "电力营销-计量管理" }
  ],
  topRelations: [
    { from: "客户信息", to: "交易记录", strength: 0.95, type: "一对一" },
    { from: "产品目录", to: "销售订单", strength: 0.88, type: "一对多" },
    { from: "用户权限", to: "数据访问", strength: 0.82, type: "多对多" }
  ]
}

export default function SemanticOverview() {
  const [activeTab, setActiveTab] = useState("overview")

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">语义层概览</h1>
          <p className="text-muted-foreground">
            语义逻辑原子统计、关联关系分析和语义层管理
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            刷新数据
          </Button>
          <Button size="sm">
            <Search className="h-4 w-4 mr-2" />
            语义搜索
          </Button>
        </div>
      </div>

      {/* 核心指标卡片 */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">语义原子总数</CardTitle>
            <Brain className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{mockData.totalAtoms.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              <span className="text-green-600">+{mockData.growthRate}%</span> 较上月
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">逻辑函数库</CardTitle>
            <Layers className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{mockData.totalFunctions.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              <span className="text-green-600">+12.8%</span> 较上月
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">关联关系</CardTitle>
            <Network className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{mockData.totalRelations.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              <span className="text-green-600">+18.5%</span> 较上月
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">活跃语义</CardTitle>
            <GitBranch className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">1,180</div>
            <p className="text-xs text-muted-foreground">
              <span className="text-green-600">+5.2%</span> 较上月
            </p>
          </CardContent>
        </Card>
      </div>

      {/* 主要内容区域 */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">语义概览</TabsTrigger>
          <TabsTrigger value="categories">原子分类</TabsTrigger>
          <TabsTrigger value="business">业务分类</TabsTrigger>
          <TabsTrigger value="relations">关联关系</TabsTrigger>
          <TabsTrigger value="atoms">原子管理</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            {/* 语义分类分布 */}
            <Card>
              <CardHeader>
                <CardTitle>语义原子分类分布</CardTitle>
                <CardDescription>按原子类型分类的语义单元分布</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {mockData.categories.map((category, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <div className={`w-3 h-3 rounded-full ${category.color}`}></div>
                        <div>
                          <span className="text-sm font-medium">{category.name}</span>
                          <p className="text-xs text-muted-foreground">{category.description}</p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <span className="text-sm text-muted-foreground">
                          {category.count} 原子
                        </span>
                        <Badge variant="secondary">
                          {category.relations} 关系
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* 语义质量指标 */}
            <Card>
              <CardHeader>
                <CardTitle>语义质量指标</CardTitle>
                <CardDescription>语义层质量和完整性监控</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>语义完整性</span>
                    <span>96.8%</span>
                  </div>
                  <Progress value={96.8} className="h-2" />
                </div>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>关联准确性</span>
                    <span>94.2%</span>
                  </div>
                  <Progress value={94.2} className="h-2" />
                </div>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>逻辑一致性</span>
                    <span>97.5%</span>
                  </div>
                  <Progress value={97.5} className="h-2" />
                </div>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>使用活跃度</span>
                    <span>89.3%</span>
                  </div>
                  <Progress value={89.3} className="h-2" />
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="categories" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>语义原子分类管理</CardTitle>
              <CardDescription>管理和配置语义原子分类</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-1 lg:grid-cols-3">
                {mockData.categories.map((category, index) => (
                  <Card key={index} className="cursor-pointer hover:shadow-md transition-shadow">
                    <CardContent className="p-4">
                      <div className="flex items-center space-x-3">
                        <div className={`w-4 h-4 rounded-full ${category.color}`}></div>
                        <div className="flex-1">
                          <h3 className="font-medium">{category.name}</h3>
                          <p className="text-sm text-muted-foreground">
                            {category.count} 原子 · {category.relations} 关系
                          </p>
                          <p className="text-xs text-muted-foreground mt-1">
                            {category.description}
                          </p>
                        </div>
                        <Button variant="ghost" size="sm">
                          <Eye className="h-4 w-4" />
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="business" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>业务关联分类</CardTitle>
              <CardDescription>按电力营销业务域分类的语义原子</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                {mockData.businessTypes.map((business, index) => (
                  <Card key={index} className="cursor-pointer hover:shadow-md transition-shadow">
                    <CardContent className="p-4">
                      <div className="flex items-center space-x-3">
                        <div className={`w-4 h-4 rounded-full ${business.color}`}></div>
                        <div className="flex-1">
                          <h3 className="font-medium">{business.name}</h3>
                          <p className="text-sm text-muted-foreground">
                            {business.count} 原子 · {business.atoms} 关联
                          </p>
                        </div>
                        <Button variant="ghost" size="sm">
                          <Eye className="h-4 w-4" />
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="relations" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>关联关系分析</CardTitle>
              <CardDescription>语义原子间的关联关系图谱</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {mockData.topRelations.map((relation, index) => (
                  <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center space-x-3">
                      <Link className="h-4 w-4 text-blue-500" />
                      <div>
                        <p className="font-medium">{relation.from} → {relation.to}</p>
                        <p className="text-sm text-muted-foreground">{relation.type}</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Badge variant="outline">
                        强度: {(relation.strength * 100).toFixed(0)}%
                      </Badge>
                      <Button variant="ghost" size="sm">
                        <Eye className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="atoms" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>最近语义原子</CardTitle>
              <CardDescription>最近创建和更新的语义原子</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {mockData.recentAtoms.map((atom, index) => (
                  <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div className={`w-2 h-2 rounded-full ${atom.status === 'active' ? 'bg-green-500' : 'bg-yellow-500'}`}></div>
                      <div>
                        <p className="font-medium">{atom.name}</p>
                        <p className="text-sm text-muted-foreground">
                          {atom.type} · {atom.businessType} · 使用 {atom.usage} 次
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Badge variant={atom.status === 'active' ? 'default' : 'secondary'}>
                        {atom.status === 'active' ? '已激活' : '草稿'}
                      </Badge>
                      <Button variant="ghost" size="sm">
                        <Eye className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
} 