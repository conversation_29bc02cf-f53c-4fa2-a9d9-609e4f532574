"use client"

import { useState } from "react"
import { Search, Database, GitBranch, FileBarChart, Settings } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Badge } from "@/components/ui/badge"

// Mock data for the metadata table
const mockMetadata = [
  {
    id: 1,
    databaseType: "MySQL",
    tableName: "user_info",
    fieldName: "user_id",
    dataType: "BIGINT",
    description: "用户唯一标识",
    businessTags: ["主键", "用户"],
    businessDescription: "系统中每个用户的唯一标识符，用于关联用户相关的所有数据",
    valueExample: "1001, 1002, 1003...",
    physicalPath: "/data/mysql/user_db/user_info/user_id",
    semanticAtoms: ["用户实体", "标识符"],
  },
  {
    id: 2,
    databaseType: "MySQL",
    tableName: "user_info",
    fieldName: "username",
    dataType: "VARCHAR(50)",
    description: "用户名",
    businessTags: ["用户", "登录"],
    businessDescription: "用户登录系统时使用的用户名，具有唯一性约束",
    valueExample: "zhangsan, lisi, wangwu...",
    physicalPath: "/data/mysql/user_db/user_info/username",
    semanticAtoms: ["用户实体", "名称"],
  },
  {
    id: 3,
    databaseType: "MaxCompute",
    tableName: "power_consumption",
    fieldName: "meter_id",
    dataType: "STRING",
    description: "电表编号",
    businessTags: ["电表", "设备"],
    businessDescription: "电力系统中电表的唯一编号，用于标识具体的计量设备",
    valueExample: "MT001, MT002, MT003...",
    physicalPath: "/data/maxcompute/power_db/power_consumption/meter_id",
    semanticAtoms: ["设备实体", "编号"],
  },
  {
    id: 4,
    databaseType: "MaxCompute",
    tableName: "power_consumption",
    fieldName: "consumption_value",
    dataType: "DECIMAL(10,2)",
    description: "用电量数值",
    businessTags: ["用电量", "数值"],
    businessDescription: "记录特定时间段内的电力消耗量，单位为千瓦时",
    valueExample: "123.45, 234.56, 345.67...",
    physicalPath: "/data/maxcompute/power_db/power_consumption/consumption_value",
    semanticAtoms: ["度量值", "电力消耗"],
  },
  {
    id: 5,
    databaseType: "MySQL",
    tableName: "audit_rules",
    fieldName: "rule_name",
    dataType: "VARCHAR(100)",
    description: "稽查规则名称",
    businessTags: ["稽查", "规则"],
    businessDescription: "数据稽查规则的名称，用于标识和管理不同的稽查逻辑",
    valueExample: "数据完整性检查, 数值范围验证, 格式规范检查...",
    physicalPath: "/data/mysql/audit_db/audit_rules/rule_name",
    semanticAtoms: ["规则实体", "名称"],
  },
]

export default function MetadataOverviewPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [databaseFilter, setDatabaseFilter] = useState("全部")
  const [selectedField, setSelectedField] = useState<(typeof mockMetadata)[0] | null>(null)
  const [currentPage, setCurrentPage] = useState(1)
  const itemsPerPage = 10

  // Filter data based on search and database type
  const filteredData = mockMetadata.filter((item) => {
    const matchesSearch =
      searchTerm === "" ||
      item.tableName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.fieldName.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesDatabase = databaseFilter === "全部" || item.databaseType === databaseFilter

    return matchesSearch && matchesDatabase
  })

  // Pagination
  const totalPages = Math.ceil(filteredData.length / itemsPerPage)
  const startIndex = (currentPage - 1) * itemsPerPage
  const paginatedData = filteredData.slice(startIndex, startIndex + itemsPerPage)

  const handleFieldClick = (field: (typeof mockMetadata)[0]) => {
    setSelectedField(field)
  }

  return (
    <div className="flex-1 space-y-6 p-6">
      {/* Page Header */}
      <div className="space-y-2">
        <h1 className="text-3xl font-bold tracking-tight text-gray-900">元数据概览与查询</h1>
        <p className="text-gray-600">查看和管理系统中的所有元数据信息</p>
      </div>

      {/* Controls Section */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="搜索表/字段名称..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={databaseFilter} onValueChange={setDatabaseFilter}>
              <SelectTrigger className="w-full sm:w-48">
                <SelectValue placeholder="数据库类型" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="全部">全部</SelectItem>
                <SelectItem value="MySQL">MySQL</SelectItem>
                <SelectItem value="MaxCompute">MaxCompute</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Main Content - Metadata Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="h-5 w-5" />
            元数据列表
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>数据库类型</TableHead>
                  <TableHead>表名称</TableHead>
                  <TableHead>字段名称</TableHead>
                  <TableHead>数据类型</TableHead>
                  <TableHead>描述</TableHead>
                  <TableHead>业务标签</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {paginatedData.map((item) => (
                  <TableRow key={item.id}>
                    <TableCell>
                      <Badge variant={item.databaseType === "MySQL" ? "default" : "secondary"}>
                        {item.databaseType}
                      </Badge>
                    </TableCell>
                    <TableCell className="font-medium">{item.tableName}</TableCell>
                    <TableCell>
                      <Button
                        variant="link"
                        className="p-0 h-auto font-medium text-blue-600 hover:text-blue-800"
                        onClick={() => handleFieldClick(item)}
                      >
                        {item.fieldName}
                      </Button>
                    </TableCell>
                    <TableCell>
                      <code className="bg-gray-100 px-2 py-1 rounded text-sm">{item.dataType}</code>
                    </TableCell>
                    <TableCell>{item.description}</TableCell>
                    <TableCell>
                      <div className="flex flex-wrap gap-1">
                        {item.businessTags.map((tag, index) => (
                          <Badge key={index} variant="outline" className="text-xs">
                            {tag}
                          </Badge>
                        ))}
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>

          {/* Pagination */}
          <div className="flex items-center justify-between space-x-2 py-4">
            <div className="text-sm text-gray-500">
              显示 {startIndex + 1} 到 {Math.min(startIndex + itemsPerPage, filteredData.length)} 条， 共{" "}
              {filteredData.length} 条记录
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
                disabled={currentPage === 1}
              >
                上一页
              </Button>
              <div className="flex items-center space-x-1">
                {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                  <Button
                    key={page}
                    variant={currentPage === page ? "default" : "outline"}
                    size="sm"
                    onClick={() => setCurrentPage(page)}
                    className="w-8 h-8"
                  >
                    {page}
                  </Button>
                ))}
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage((prev) => Math.min(prev + 1, totalPages))}
                disabled={currentPage === totalPages}
              >
                下一页
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Future Features Section */}
      <div className="grid gap-4 md:grid-cols-2">
        <Card className="border-2 border-dashed border-gray-300 bg-gray-50/50">
          <CardHeader className="text-center pb-4">
            <div className="mx-auto w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center mb-3">
              <GitBranch className="h-6 w-6 text-gray-400" />
            </div>
            <CardTitle className="text-sm font-medium text-gray-600">数据血缘与影响分析</CardTitle>
          </CardHeader>
          <CardContent className="text-center">
            <p className="text-xs text-gray-500">未来功能</p>
            <p className="text-xs text-gray-400 mt-1">可视化展示数据流转关系和影响范围</p>
          </CardContent>
        </Card>

        <Card className="border-2 border-dashed border-gray-300 bg-gray-50/50">
          <CardHeader className="text-center pb-4">
            <div className="mx-auto w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center mb-3">
              <FileBarChart className="h-6 w-6 text-gray-400" />
            </div>
            <CardTitle className="text-sm font-medium text-gray-600">元数据质量报告</CardTitle>
          </CardHeader>
          <CardContent className="text-center">
            <p className="text-xs text-gray-500">未来功能</p>
            <p className="text-xs text-gray-400 mt-1">自动生成元数据质量评估报告</p>
          </CardContent>
        </Card>
      </div>

      {/* Disabled Future Feature Button */}
      <div className="flex justify-center">
        <Button disabled className="flex items-center gap-2">
          <Settings className="h-4 w-4" />
          元数据同步配置 (未来功能)
        </Button>
      </div>

      {/* Field Details Modal */}
      <Dialog open={!!selectedField} onOpenChange={() => setSelectedField(null)}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Database className="h-5 w-5" />
              字段详细信息: {selectedField?.fieldName}
            </DialogTitle>
          </DialogHeader>
          {selectedField && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-700">表名称</label>
                  <p className="text-sm text-gray-900 mt-1">{selectedField.tableName}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">数据类型</label>
                  <p className="text-sm text-gray-900 mt-1">
                    <code className="bg-gray-100 px-2 py-1 rounded">{selectedField.dataType}</code>
                  </p>
                </div>
              </div>

              <div>
                <label className="text-sm font-medium text-gray-700">业务描述</label>
                <p className="text-sm text-gray-900 mt-1">{selectedField.businessDescription}</p>
              </div>

              <div>
                <label className="text-sm font-medium text-gray-700">值域示例</label>
                <p className="text-sm text-gray-900 mt-1 font-mono bg-gray-50 p-2 rounded">
                  {selectedField.valueExample}
                </p>
              </div>

              <div>
                <label className="text-sm font-medium text-gray-700">物理路径</label>
                <p className="text-sm text-gray-900 mt-1 font-mono bg-gray-50 p-2 rounded break-all">
                  {selectedField.physicalPath}
                </p>
              </div>

              <div>
                <label className="text-sm font-medium text-gray-700">关联语义原子</label>
                <div className="flex flex-wrap gap-2 mt-1">
                  {selectedField.semanticAtoms.map((atom, index) => (
                    <Badge key={index} variant="secondary">
                      {atom}
                    </Badge>
                  ))}
                </div>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}
