// 数据字典Mock数据与字段识别算法
import type { Dictionary } from '../types/dictionary';

// Mock数据示例
export const mockDictionary: Dictionary = [
  {
    table: 'user',
    description: '用户信息表',
    fields: [
      {
        name: 'user_id',
        type: 'string',
        description: '用户唯一标识',
        businessMeaning: '系统内唯一用户编号',
      },
      {
        name: 'user_name',
        type: 'string',
        description: '用户名',
        businessMeaning: '用户在系统中的显示名称',
      },
      {
        name: 'gender',
        type: 'enum',
        description: '性别',
        values: [
          { value: 'M', label: '男' },
          { value: 'F', label: '女' },
          { value: 'U', label: '未知' },
        ],
        businessMeaning: '用户性别',
      },
      {
        name: 'status',
        type: 'enum',
        description: '账号状态',
        values: [
          { value: 'active', label: '激活' },
          { value: 'inactive', label: '未激活' },
          { value: 'banned', label: '封禁' },
        ],
        businessMeaning: '账号当前状态',
      },
    ],
  },
  {
    table: 'order',
    description: '订单表',
    fields: [
      {
        name: 'order_id',
        type: 'string',
        description: '订单编号',
        businessMeaning: '唯一订单标识',
      },
      {
        name: 'user_id',
        type: 'string',
        description: '下单用户ID',
        businessMeaning: '订单归属用户',
      },
      {
        name: 'amount',
        type: 'number',
        description: '订单金额',
        businessMeaning: '本次订单的总金额',
      },
      {
        name: 'status',
        type: 'enum',
        description: '订单状态',
        values: [
          { value: 'pending', label: '待支付' },
          { value: 'paid', label: '已支付' },
          { value: 'cancelled', label: '已取消' },
        ],
        businessMeaning: '订单当前处理状态',
      },
    ],
  },
  {
    table: 'device',
    description: '设备信息表',
    fields: [
      {
        name: 'device_id',
        type: 'string',
        description: '设备编号',
        businessMeaning: '唯一设备标识',
      },
      {
        name: 'device_type',
        type: 'enum',
        description: '设备类型',
        values: [
          { value: 'meter', label: '电表' },
          { value: 'transformer', label: '变压器' },
          { value: 'sensor', label: '传感器' },
        ],
        businessMeaning: '设备所属类别',
      },
      {
        name: 'install_date',
        type: 'date',
        description: '安装日期',
        businessMeaning: '设备安装的日期',
      },
    ],
  },
];

// 字段识别算法（示例：根据字段名和类型简单识别）
export function identifyDictionaryFields(tableName: string, columns: { name: string; type: string }[]): Dictionary {
  // 真实场景可用更复杂的规则和NLP等
  return [
    {
      table: tableName,
      fields: columns.map(col => ({
        name: col.name,
        type: col.type,
        description: '',
        businessMeaning: '',
      })),
    },
  ];
} 