# 业务前端独立应用基础规范约束文档

## 1. 项目概述与约束原则

### 1.1 项目定位
**电力数据大脑 - 智能稽查系统**是一个基于AI驱动的电力数据治理与营销稽查智能化平台，采用现代化前端技术栈构建企业级单页应用。

### 1.2 技术选型说明
- **核心框架**: Next.js 15.2.4 + React 19 + TypeScript 5
- **UI组件库**: shadcn/ui + Radix UI + Tailwind CSS
- **数据可视化**: D3.js + Recharts + Vis Network
- **状态管理**: React Hooks (无全局状态管理库)
- **构建工具**: Next.js App Router + pnpm
- **部署方式**: 静态导出 + 容器化部署

### 1.3 约束制定指导原则
1. **一致性优先**: 确保代码风格、命名规范、架构模式的统一性
2. **可维护性**: 代码结构清晰，易于理解和修改
3. **性能导向**: 优化加载速度、运行效率和用户体验
4. **安全第一**: 遵循前端安全最佳实践
5. **团队协作**: 便于多人协作开发和代码审查

### 1.4 适用范围与例外
- **适用范围**: 所有前端代码、配置文件、文档
- **例外情况**: 第三方库内部代码、自动生成代码
- **优先级**: 必须(MUST) > 推荐(SHOULD) > 可选(MAY)

## 2. 目录结构规范

### 2.1 标准目录结构
```
datamind-web/
├── app/                                    # Next.js App Router 页面 [MUST]
│   ├── (业务模块)/                         # 业务功能模块
│   │   ├── components/                     # 模块专用组件
│   │   ├── hooks/                          # 模块专用Hooks
│   │   ├── types/                          # 模块类型定义
│   │   ├── utils/                          # 模块工具函数
│   │   ├── main.tsx                        # 主要业务组件
│   │   └── page.tsx                        # 路由页面入口
│   ├── layout.tsx                          # 根布局 [MUST]
│   ├── page.tsx                            # 首页 [MUST]
│   ├── globals.css                         # 全局样式 [MUST]
│   └── not-found.tsx                       # 404页面 [SHOULD]
├── components/                             # 全局可复用组件 [MUST]
│   ├── layout/                             # 布局组件
│   ├── ui/                                 # UI基础组件库
│   └── business/                           # 业务通用组件
├── lib/                                    # 核心工具库 [MUST]
│   ├── utils.ts                            # 通用工具函数
│   ├── constants.ts                        # 全局常量
│   └── api.ts                              # API客户端
├── hooks/                                  # 全局自定义Hooks [SHOULD]
├── types/                                  # 全局类型定义 [MUST]
├── utils/                                  # 业务工具函数 [SHOULD]
├── styles/                                 # 样式文件 [MAY]
├── public/                                 # 静态资源 [MUST]
├── tests/                                  # 测试文件 [SHOULD]
└── docs/                                   # 项目文档 [MAY]
```

### 2.2 目录命名规则
- **业务模块**: 使用kebab-case，如`audit-knowledge-management`
- **组件目录**: 使用kebab-case，如`data-inspection`
- **文件名**: 使用kebab-case，如`main-layout.tsx`
- **组件文件**: 使用PascalCase，如`DataTable.tsx`

### 2.3 文件组织原则
1. **按功能分组**: 相关功能放在同一目录下
2. **就近原则**: 组件和相关文件放在同一目录
3. **层次清晰**: 避免过深的目录嵌套（≤4层）
4. **职责单一**: 每个文件只负责一个功能

## 3. 技术栈约束

### 3.1 核心技术栈版本锁定
```json
{
  "必须使用版本": {
    "next": "15.2.4",
    "react": "^19",
    "react-dom": "^19",
    "typescript": "^5"
  },
  "推荐使用版本": {
    "@radix-ui/*": "最新稳定版",
    "tailwindcss": "^3.4.17",
    "lucide-react": "^0.454.0"
  }
}
```

### 3.2 第三方依赖选择准则
**必须遵循 [MUST]**:
- 优先选择官方推荐的库
- 选择维护活跃、社区成熟的库
- 避免引入功能重复的依赖

**推荐做法 [SHOULD]**:
- 新增依赖前进行技术评估
- 记录依赖选择理由
- 定期更新依赖版本

### 3.3 技术选型决策流程
1. **需求分析**: 明确技术需求和约束
2. **方案调研**: 调研3个以上候选方案
3. **技术评估**: 评估性能、安全性、维护成本
4. **团队讨论**: 技术团队评审和决策
5. **文档记录**: 记录选型理由和决策过程

## 4. 代码规范约束

### 4.1 TypeScript规范
**必须遵循 [MUST]**:
```typescript
// 启用严格模式
"strict": true,
"noImplicitAny": true,
"strictNullChecks": true

// 接口定义
interface UserInfo {
  id: string;
  name: string;
  email?: string; // 可选属性使用?
}

// 函数类型定义
type HandleSubmit = (data: FormData) => Promise<void>;

// 组件Props类型
interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary';
  size?: 'sm' | 'md' | 'lg';
}
```

**推荐做法 [SHOULD]**:
- 优先使用interface而非type
- 为所有函数参数和返回值定义类型
- 使用泛型提高代码复用性

### 4.2 React组件规范
**必须遵循 [MUST]**:
```typescript
// 函数式组件 + TypeScript
interface ComponentProps {
  title: string;
  onSubmit: (data: any) => void;
}

export function MyComponent({ title, onSubmit }: ComponentProps) {
  const [loading, setLoading] = useState(false);
  
  return (
    <div className="p-4">
      <h1>{title}</h1>
    </div>
  );
}

// 默认导出组件
export default MyComponent;
```

**推荐做法 [SHOULD]**:
- 使用React.memo优化性能
- 自定义Hooks提取逻辑
- 使用useCallback和useMemo优化

### 4.3 代码风格规范
**必须遵循 [MUST]**:
- 使用2空格缩进
- 行尾不留空格
- 文件末尾保留一个空行
- 字符串使用双引号
- 对象和数组末尾保留逗号

**ESLint配置**:
```json
{
  "extends": ["next/core-web-vitals"],
  "rules": {
    "indent": ["error", 2],
    "quotes": ["error", "double"],
    "semi": ["error", "always"]
  }
}
```

## 5. 命名约束规范

### 5.1 变量和函数命名
**必须遵循 [MUST]**:
```typescript
// 变量: camelCase
const userName = "张三";
const isLoading = false;
const userList = [];

// 函数: camelCase + 动词开头
function getUserInfo() {}
function handleSubmit() {}
function validateForm() {}

// 常量: UPPER_SNAKE_CASE
const API_BASE_URL = "https://api.example.com";
const MAX_RETRY_COUNT = 3;

// 布尔值: is/has/can/should开头
const isVisible = true;
const hasPermission = false;
const canEdit = true;
const shouldUpdate = false;
```

### 5.2 组件和类命名
**必须遵循 [MUST]**:
```typescript
// 组件: PascalCase
export function DataTable() {}
export function UserProfile() {}
export function AuditRuleGenerator() {}

// 类: PascalCase
class ApiClient {}
class DataProcessor {}

// 接口: PascalCase + I前缀(可选)
interface UserInfo {}
interface IApiResponse {} // 可选前缀
```

### 5.3 文件和目录命名
**必须遵循 [MUST]**:
```
// 页面文件: kebab-case
audit-knowledge-management/
data-source-management/

// 组件文件: PascalCase
DataTable.tsx
UserProfile.tsx
AuditRuleGenerator.tsx

// 工具文件: kebab-case
api-client.ts
data-processor.ts
form-validator.ts

// 类型文件: kebab-case
user-types.ts
api-types.ts
```

### 5.4 CSS类名和样式
**必须遵循 [MUST]**:
```css
/* Tailwind CSS类名组合 */
.btn-primary {
  @apply bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600;
}

/* 自定义CSS类名: kebab-case */
.data-table-container {}
.audit-rule-form {}
.user-profile-card {}
```

## 6. 组件和模块约束

### 6.1 组件设计原则
**必须遵循 [MUST]**:
1. **单一职责**: 每个组件只负责一个功能
2. **Props接口**: 明确定义Props类型
3. **可复用性**: 避免硬编码，支持配置
4. **无副作用**: 纯函数组件，避免全局状态修改

**组件结构模板**:
```typescript
interface ComponentProps {
  // Props定义
}

export function Component({ ...props }: ComponentProps) {
  // Hooks
  // 事件处理函数
  // 渲染逻辑
  
  return (
    // JSX
  );
}

export default Component;
```

### 6.2 状态管理规范
**必须遵循 [MUST]**:
```typescript
// 本地状态: useState
const [data, setData] = useState<DataType[]>([]);
const [loading, setLoading] = useState(false);
const [error, setError] = useState<string | null>(null);

// 副作用: useEffect
useEffect(() => {
  // 副作用逻辑
  return () => {
    // 清理函数
  };
}, [dependencies]);

// 自定义Hooks
function useApiData(url: string) {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(true);
  
  // 逻辑实现
  
  return { data, loading, error };
}
```

### 6.3 组件间通信约定
**必须遵循 [MUST]**:
- **父子通信**: Props传递数据，回调函数传递事件
- **兄弟通信**: 通过共同父组件中转
- **跨层通信**: 使用React Context
- **全局状态**: 避免使用，优先本地状态

## 7. 工具类使用规范

### 7.1 通用工具类使用准则
**必须遵循 [MUST]**:
```typescript
// lib/utils.ts - 核心工具函数
import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";

// 样式合并工具
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

// 日期格式化
export function formatDate(date: Date): string {
  return date.toLocaleDateString('zh-CN');
}

// 数字格式化
export function formatNumber(num: number): string {
  return num.toLocaleString('zh-CN');
}
```

### 7.2 自定义工具类开发规范
**必须遵循 [MUST]**:
1. **纯函数**: 无副作用，相同输入产生相同输出
2. **类型安全**: 完整的TypeScript类型定义
3. **错误处理**: 合理的错误处理和边界情况
4. **文档注释**: JSDoc注释说明用途和参数

**工具函数模板**:
```typescript
/**
 * 工具函数描述
 * @param param1 参数1描述
 * @param param2 参数2描述
 * @returns 返回值描述
 */
export function utilityFunction(param1: string, param2: number): boolean {
  // 参数验证
  if (!param1 || param2 < 0) {
    throw new Error('Invalid parameters');
  }
  
  // 业务逻辑
  return true;
}
```

### 7.3 第三方工具库集成规范
**推荐做法 [SHOULD]**:
- 创建适配器层封装第三方库
- 统一错误处理和日志记录
- 提供类型定义和文档说明

## 8. 样式和UI约束

### 8.1 CSS/SCSS组织结构
**必须遵循 [MUST]**:
```css
/* app/globals.css - 全局样式 */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* 全局CSS变量 */
:root {
  --background: 0 0% 100%;
  --foreground: 222.2 84% 4.9%;
  --primary: 221.2 83.2% 53.3%;
  --primary-foreground: 210 40% 98%;
}

/* 组件样式层 */
@layer components {
  .btn-primary {
    @apply bg-primary text-primary-foreground hover:bg-primary/90;
  }
}

/* 工具样式层 */
@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}
```

### 8.2 样式命名规范
**必须遵循 [MUST]**:
- **Tailwind优先**: 优先使用Tailwind CSS类名
- **自定义类名**: 使用kebab-case，如`data-table-header`
- **BEM方法论**: 复杂组件使用BEM命名
- **语义化命名**: 体现功能而非样式

**示例**:
```typescript
// 推荐: 使用Tailwind类名
<div className="flex items-center justify-between p-4 bg-white rounded-lg shadow">

// 自定义类名
<div className="audit-rule-card">
  <div className="audit-rule-card__header">
    <div className="audit-rule-card__title">
```

### 8.3 UI组件库使用规范
**必须遵循 [MUST]**:
```typescript
// 使用shadcn/ui组件
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";

// 组件使用示例
<Card>
  <CardHeader>
    <CardTitle>标题</CardTitle>
  </CardHeader>
  <CardContent>
    <Input placeholder="请输入..." />
    <Button variant="default" size="sm">
      提交
    </Button>
  </CardContent>
</Card>
```

**推荐做法 [SHOULD]**:
- 优先使用shadcn/ui组件
- 自定义组件继承shadcn/ui样式系统
- 保持设计系统一致性

### 8.4 响应式设计约束
**必须遵循 [MUST]**:
```typescript
// 响应式断点
const breakpoints = {
  sm: '640px',   // 手机
  md: '768px',   // 平板
  lg: '1024px',  // 笔记本
  xl: '1280px',  // 桌面
  '2xl': '1536px' // 大屏
};

// 响应式类名使用
<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
  <div className="p-4 sm:p-6 lg:p-8">
```

## 9. 性能约束

### 9.1 代码性能基准要求
**必须达到 [MUST]**:
- **首屏加载时间**: < 3秒
- **页面切换时间**: < 500ms
- **API响应处理**: < 200ms
- **组件渲染时间**: < 100ms

### 9.2 资源加载和优化规范
**必须遵循 [MUST]**:
```typescript
// 代码分割 - 动态导入
const LazyComponent = lazy(() => import('./LazyComponent'));

// 图片优化
import Image from 'next/image';

<Image
  src="/image.jpg"
  alt="描述"
  width={500}
  height={300}
  priority={false} // 非关键图片
  placeholder="blur"
/>

// 预加载关键资源
<link rel="preload" href="/critical.css" as="style" />
<link rel="prefetch" href="/next-page.js" as="script" />
```

### 9.3 内存管理和垃圾回收约束
**必须遵循 [MUST]**:
```typescript
// useEffect清理函数
useEffect(() => {
  const timer = setInterval(() => {
    // 定时器逻辑
  }, 1000);

  return () => {
    clearInterval(timer); // 清理定时器
  };
}, []);

// 事件监听器清理
useEffect(() => {
  const handleResize = () => {
    // 处理逻辑
  };

  window.addEventListener('resize', handleResize);

  return () => {
    window.removeEventListener('resize', handleResize);
  };
}, []);
```

### 9.4 渲染性能监控要求
**推荐做法 [SHOULD]**:
```typescript
// React.memo优化
const MemoizedComponent = React.memo(function Component({ data }) {
  return <div>{data}</div>;
});

// useMemo优化计算
const expensiveValue = useMemo(() => {
  return heavyCalculation(data);
}, [data]);

// useCallback优化函数
const handleClick = useCallback((id: string) => {
  // 处理逻辑
}, [dependency]);
```

## 10. 安全约束

### 10.1 前端安全编码规范
**必须遵循 [MUST]**:
```typescript
// XSS防护 - 避免dangerouslySetInnerHTML
// 错误做法
<div dangerouslySetInnerHTML={{ __html: userInput }} />

// 正确做法
<div>{userInput}</div>

// 输入验证
import { z } from 'zod';

const schema = z.object({
  email: z.string().email('无效的邮箱格式'),
  password: z.string().min(8, '密码至少8位'),
});

// URL验证
function isValidUrl(url: string): boolean {
  try {
    const urlObj = new URL(url);
    return ['http:', 'https:'].includes(urlObj.protocol);
  } catch {
    return false;
  }
}
```

### 10.2 数据验证和防护规则
**必须遵循 [MUST]**:
```typescript
// 表单验证
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';

const formSchema = z.object({
  username: z.string()
    .min(2, '用户名至少2个字符')
    .max(20, '用户名最多20个字符')
    .regex(/^[a-zA-Z0-9_]+$/, '只能包含字母、数字和下划线'),
});

const form = useForm({
  resolver: zodResolver(formSchema),
});
```

### 10.3 敏感信息处理约束
**必须遵循 [MUST]**:
- 不在前端存储敏感信息（密码、密钥等）
- 使用HTTPS传输所有数据
- 实现CSP（内容安全策略）
- 定期更新依赖包，修复安全漏洞

### 10.4 第三方依赖安全审核
**推荐做法 [SHOULD]**:
```bash
# 定期安全审核
pnpm audit
pnpm audit --fix

# 检查过期依赖
pnpm outdated

# 更新依赖
pnpm update
```

## 11. 测试约束

### 11.1 单元测试覆盖率要求
**必须达到 [MUST]**:
- **整体覆盖率**: > 80%
- **核心业务逻辑**: > 90%
- **工具函数**: > 95%
- **组件测试**: > 70%

### 11.2 测试代码组织规范
**必须遵循 [MUST]**:
```
tests/
├── unit/                    # 单元测试
│   ├── components/          # 组件测试
│   │   └── Button.test.tsx
│   ├── utils/              # 工具函数测试
│   │   └── formatDate.test.ts
│   └── hooks/              # Hooks测试
│       └── useApiData.test.ts
├── integration/            # 集成测试
├── e2e/                    # 端到端测试
└── __mocks__/              # Mock文件
```

### 11.3 测试编写规范
**必须遵循 [MUST]**:
```typescript
// 组件测试示例
import { render, screen, fireEvent } from '@testing-library/react';
import { Button } from './Button';

describe('Button Component', () => {
  it('should render with correct text', () => {
    render(<Button>Click me</Button>);
    expect(screen.getByText('Click me')).toBeInTheDocument();
  });

  it('should handle click events', () => {
    const handleClick = jest.fn();
    render(<Button onClick={handleClick}>Click me</Button>);

    fireEvent.click(screen.getByText('Click me'));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });
});

// 工具函数测试示例
import { formatDate } from './formatDate';

describe('formatDate', () => {
  it('should format date correctly', () => {
    const date = new Date('2024-01-01');
    expect(formatDate(date)).toBe('2024/1/1');
  });

  it('should handle invalid date', () => {
    expect(() => formatDate(null)).toThrow('Invalid date');
  });
});
```

## 12. 构建和部署约束

### 12.1 构建流程和配置规范
**必须遵循 [MUST]**:
```javascript
// next.config.mjs
/** @type {import('next').NextConfig} */
const nextConfig = {
  // 静态导出配置
  output: 'export',
  distDir: 'dist',
  assetPrefix: '/',
  basePath: '',

  // 图片优化
  images: {
    unoptimized: true,
  },

  // 构建优化
  eslint: {
    ignoreDuringBuilds: false, // 生产环境必须通过ESLint
  },
  typescript: {
    ignoreBuildErrors: false, // 生产环境必须通过TypeScript检查
  },

  // Webpack配置
  webpack: (config) => {
    config.module.rules.push({
      test: /\.(png|jpg|gif|svg|ico)$/,
      type: 'asset/resource',
    });
    return config;
  },
};

export default nextConfig;
```

### 12.2 环境配置管理
**必须遵循 [MUST]**:
```bash
# 环境变量文件
.env.local          # 本地开发环境
.env.development    # 开发环境
.env.staging        # 测试环境
.env.production     # 生产环境

# 环境变量命名规范
NEXT_PUBLIC_API_URL=https://api.example.com
NEXT_PUBLIC_APP_NAME=电力数据大脑
NEXT_PUBLIC_VERSION=1.0.0
```

**环境变量使用**:
```typescript
// 环境变量类型定义
interface EnvConfig {
  API_URL: string;
  APP_NAME: string;
  VERSION: string;
}

// 环境变量验证
const env: EnvConfig = {
  API_URL: process.env.NEXT_PUBLIC_API_URL || '',
  APP_NAME: process.env.NEXT_PUBLIC_APP_NAME || '默认应用名',
  VERSION: process.env.NEXT_PUBLIC_VERSION || '1.0.0',
};

// 运行时验证
if (!env.API_URL) {
  throw new Error('NEXT_PUBLIC_API_URL is required');
}
```

### 12.3 部署策略和版本控制
**必须遵循 [MUST]**:
```bash
# 构建脚本
{
  "scripts": {
    "dev": "next dev",
    "build": "next build",
    "start": "next start",
    "lint": "next lint",
    "type-check": "tsc --noEmit",
    "test": "jest",
    "test:coverage": "jest --coverage",
    "build:analyze": "ANALYZE=true next build"
  }
}

# 部署前检查清单
pnpm run type-check  # TypeScript检查
pnpm run lint        # ESLint检查
pnpm run test        # 单元测试
pnpm run build       # 构建检查
```

### 12.4 持续集成要求
**推荐做法 [SHOULD]**:
```yaml
# .github/workflows/ci.yml
name: CI
on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      - run: pnpm install
      - run: pnpm run type-check
      - run: pnpm run lint
      - run: pnpm run test
      - run: pnpm run build
```

## 13. 代码审查约束

### 13.1 代码审查流程和标准
**必须遵循 [MUST]**:
1. **提交前自检**: 运行所有检查脚本
2. **创建PR**: 提供清晰的描述和变更说明
3. **自动化检查**: CI/CD流水线自动检查
4. **人工审查**: 至少一人审查代码
5. **修复问题**: 解决所有审查意见
6. **合并代码**: 审查通过后合并

### 13.2 审查检查清单
**必须检查 [MUST]**:
- [ ] 代码符合命名规范
- [ ] TypeScript类型定义完整
- [ ] 组件设计合理，职责单一
- [ ] 性能优化措施到位
- [ ] 错误处理完善
- [ ] 安全问题排查
- [ ] 测试覆盖充分
- [ ] 文档注释完整

### 13.3 问题分级和处理规范
**问题分级**:
- **阻塞性问题**: 必须修复才能合并
- **重要问题**: 建议修复，可延后处理
- **建议性问题**: 可选修复，提升代码质量

### 13.4 审查工具和自动化
**推荐工具 [SHOULD]**:
```json
// .eslintrc.json
{
  "extends": ["next/core-web-vitals"],
  "rules": {
    "prefer-const": "error",
    "no-unused-vars": "error",
    "@typescript-eslint/no-explicit-any": "warn"
  }
}

// prettier.config.js
module.exports = {
  semi: true,
  trailingComma: 'es5',
  singleQuote: false,
  printWidth: 80,
  tabWidth: 2,
};
```

## 14. 文档和维护约束

### 14.1 技术文档编写规范
**必须遵循 [MUST]**:
```typescript
/**
 * 用户信息管理组件
 *
 * @description 提供用户信息的展示、编辑和删除功能
 * <AUTHOR>
 * @date 2024-01-01
 * @version 1.0.0
 *
 * @example
 * ```tsx
 * <UserProfile
 *   user={userInfo}
 *   onUpdate={handleUpdate}
 *   onDelete={handleDelete}
 * />
 * ```
 */
interface UserProfileProps {
  /** 用户信息对象 */
  user: UserInfo;
  /** 更新用户信息的回调函数 */
  onUpdate: (user: UserInfo) => void;
  /** 删除用户的回调函数 */
  onDelete: (userId: string) => void;
}

export function UserProfile({ user, onUpdate, onDelete }: UserProfileProps) {
  // 组件实现
}
```

### 14.2 变更日志维护要求
**必须遵循 [MUST]**:
```markdown
# CHANGELOG.md

## [1.2.0] - 2024-01-15

### Added
- 新增用户权限管理功能
- 添加数据导出功能

### Changed
- 优化页面加载性能
- 更新UI组件库到最新版本

### Fixed
- 修复表单验证问题
- 解决内存泄漏问题

### Deprecated
- 废弃旧版API接口

### Removed
- 移除无用的依赖包

### Security
- 修复XSS安全漏洞
```

### 14.3 版本管理和发布流程
**必须遵循 [MUST]**:
```bash
# 语义化版本控制
# 主版本号.次版本号.修订号
# 1.0.0 -> 1.0.1 (修复bug)
# 1.0.1 -> 1.1.0 (新增功能)
# 1.1.0 -> 2.0.0 (破坏性变更)

# Git标签管理
git tag -a v1.0.0 -m "Release version 1.0.0"
git push origin v1.0.0

# 发布检查清单
- [ ] 代码审查通过
- [ ] 所有测试通过
- [ ] 文档更新完成
- [ ] 变更日志更新
- [ ] 版本号更新
- [ ] 构建成功
- [ ] 部署验证
```

### 14.4 知识管理和传承
**推荐做法 [SHOULD]**:
- 定期技术分享和培训
- 维护技术决策记录(ADR)
- 建立代码审查知识库
- 新人入职培训文档

## 15. 规范执行和监控

### 15.1 规范执行检查清单
**日常开发检查 [MUST]**:
- [ ] 代码提交前运行所有检查脚本
- [ ] 遵循命名规范和代码风格
- [ ] 完成单元测试编写
- [ ] 添加必要的文档注释
- [ ] 进行安全性检查

**代码审查检查 [MUST]**:
- [ ] 架构设计合理性
- [ ] 性能优化措施
- [ ] 错误处理完整性
- [ ] 测试覆盖率达标
- [ ] 文档完整性

### 15.2 规范违反处理机制
**处理流程**:
1. **发现问题**: 通过工具或人工发现规范违反
2. **问题记录**: 记录问题详情和影响范围
3. **责任确认**: 确认责任人和修复时间
4. **问题修复**: 按照规范要求修复问题
5. **验证确认**: 验证修复效果和规范符合性

### 15.3 规范持续改进
**改进机制**:
- 定期评估规范执行效果
- 收集团队反馈和建议
- 根据项目发展调整规范
- 更新工具和自动化检查

---

## 附录

### A. 快速参考

#### A.1 常用命令
```bash
# 开发环境
pnpm dev                    # 启动开发服务器
pnpm build                  # 构建生产版本
pnpm lint                   # 代码检查
pnpm test                   # 运行测试

# 依赖管理
pnpm add package-name       # 添加依赖
pnpm remove package-name    # 移除依赖
pnpm update                 # 更新依赖
```

#### A.2 常用代码模板
```typescript
// 组件模板
interface ComponentProps {
  // Props定义
}

export function Component({ ...props }: ComponentProps) {
  // 实现
  return <div></div>;
}

// Hook模板
function useCustomHook() {
  const [state, setState] = useState();

  return { state, setState };
}
```

### B. 工具配置

#### B.1 VSCode配置
```json
// .vscode/settings.json
{
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  },
  "typescript.preferences.importModuleSpecifier": "relative"
}
```

#### B.2 Git配置
```bash
# .gitignore
node_modules/
.next/
dist/
.env.local
*.log
```

---

*本规范文档将根据项目发展和团队反馈持续更新和完善。如有疑问或建议，请联系技术团队。*
```
