"use client"

import { useState, use<PERSON>emo } from "react"
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { 
  AlertTriangle, 
  CheckCircle, 
  Eye, 
  Edit, 
  Plus,
  FileText,
  TrendingUp,
  AlertCircle,
  ArrowRight,
  ExternalLink,
  MessageSquare,
  BookOpen,
  Target,
  Zap
} from "lucide-react"

// 类型定义
interface NewlyIdentifiedElement {
  id: string
  name: string
  type: 'Entity' | 'Attribute' | 'Event' | 'Relationship'
  description: string
  sourcePolicy: string
  extractionDate: string
  confidence: number
  suggestedActions: string[]
  originalText: string
}

interface UncoveredElement {
  id: string
  name: string
  type: 'Entity' | 'Attribute' | 'Event' | 'Relationship'
  description: string
  sourcePolicy: string
  currentCoverage: string
  riskLevel: 'High' | 'Medium' | 'Low'
  suggestedThemes: string[]
  originalText: string
}

interface PolicyInconsistency {
  id: string
  elementName: string
  elementType: 'Entity' | 'Attribute' | 'Event' | 'Relationship'
  currentDefinition: string
  newDefinition: string
  sourcePolicy: string
  changeType: 'Definition' | 'Threshold' | 'Criteria' | 'Scope'
  impactLevel: 'High' | 'Medium' | 'Low'
  affectedThemes: string[]
  suggestedAction: 'Adopt' | 'Review' | 'Reject'
  originalText: string
}

// 模拟数据
const mockNewlyIdentifiedElements: NewlyIdentifiedElement[] = [
  {
    id: "NIE001",
    name: "分布式能源客户",
    type: "Entity",
    description: "使用分布式能源系统的客户，包括太阳能、风能等可再生能源用户",
    sourcePolicy: "新能源政策文件2024-01",
    extractionDate: "2024-01-20",
    confidence: 0.95,
    suggestedActions: ["创建新的稽查主题", "关联现有客户主题", "设置专项监控规则"],
    originalText: "分布式能源客户是指使用分布式能源系统的客户，包括太阳能、风能等可再生能源用户。"
  },
  {
    id: "NIE002",
    name: "智能电表异常事件",
    type: "Event",
    description: "智能电表数据异常或通信故障时触发的事件",
    sourcePolicy: "智能电网建设政策2024-02",
    extractionDate: "2024-01-21",
    confidence: 0.88,
    suggestedActions: ["创建设备监控主题", "设置告警阈值", "关联客户服务流程"],
    originalText: "智能电表异常事件是指智能电表数据异常或通信故障时触发的事件。"
  }
]

const mockUncoveredElements: UncoveredElement[] = [
  {
    id: "UE001",
    name: "虚拟电厂参与度",
    type: "Attribute",
    description: "客户参与虚拟电厂项目的程度和贡献度",
    sourcePolicy: "虚拟电厂试点政策2024-04",
    currentCoverage: "无相关稽查主题覆盖",
    riskLevel: "High",
    suggestedThemes: ["虚拟电厂稽查", "新能源参与稽查", "电网互动稽查"],
    originalText: "虚拟电厂参与度是指客户参与虚拟电厂项目的程度和贡献度。"
  }
]

const mockPolicyInconsistencies: PolicyInconsistency[] = [
  {
    id: "PI001",
    elementName: "高耗能客户阈值",
    elementType: "Attribute",
    currentDefinition: "年用电量超过1000万千瓦时的客户",
    newDefinition: "年用电量超过800万千瓦时的客户",
    sourcePolicy: "节能降耗政策2024-07",
    changeType: "Threshold",
    impactLevel: "High",
    affectedThemes: ["高耗能客户稽查", "节能稽查", "负荷管理稽查"],
    suggestedAction: "Adopt",
    originalText: "高耗能客户是指年用电量超过800万千瓦时的客户。"
  }
]

export function AuditThemeIntelligenceAnalysis() {
  const [activeTab, setActiveTab] = useState("newly-identified")
  const [selectedElement, setSelectedElement] = useState<NewlyIdentifiedElement | UncoveredElement | PolicyInconsistency | null>(null)
  const [isDetailDialogOpen, setIsDetailDialogOpen] = useState(false)
  const [isActionDialogOpen, setIsActionDialogOpen] = useState(false)
  const [actionType, setActionType] = useState<string>("")
  const [actionNote, setActionNote] = useState("")

  const handleViewDetail = (element: NewlyIdentifiedElement | UncoveredElement | PolicyInconsistency) => {
    setSelectedElement(element)
    setIsDetailDialogOpen(true)
  }

  const handleExecuteAction = (element: NewlyIdentifiedElement | UncoveredElement | PolicyInconsistency, action: string) => {
    setSelectedElement(element)
    setActionType(action)
    setIsActionDialogOpen(true)
  }

  const getRiskLevelBadgeVariant = (level: string) => {
    switch (level) {
      case 'High': return 'destructive'
      case 'Medium': return 'secondary'
      case 'Low': return 'default'
      default: return 'default'
    }
  }

  const getImpactLevelBadgeVariant = (level: string) => {
    switch (level) {
      case 'High': return 'destructive'
      case 'Medium': return 'secondary'
      case 'Low': return 'default'
      default: return 'default'
    }
  }

  const getSuggestedActionBadgeVariant = (action: string) => {
    switch (action) {
      case 'Adopt': return 'default'
      case 'Review': return 'secondary'
      case 'Reject': return 'destructive'
      default: return 'default'
    }
  }

  const getConfidenceBadgeVariant = (confidence: number) => {
    if (confidence >= 0.9) return 'default'
    if (confidence >= 0.8) return 'secondary'
    return 'destructive'
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">稽查主题要素智能分析</h1>
          <p className="text-gray-600 mt-2">智能预测与风险洞察 - 识别新要素、分析覆盖缺口、检测政策不一致性</p>
        </div>
        <div className="flex items-center gap-2">
          <Badge variant="outline" className="flex items-center gap-1">
            <Zap className="w-4 h-4" />
            智能分析
          </Badge>
          <Badge variant="outline" className="flex items-center gap-1">
            <Target className="w-4 h-4" />
            实时更新
          </Badge>
        </div>
      </div>

      {/* 统计概览卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">新识别要素</p>
                <p className="text-2xl font-bold text-blue-600">{mockNewlyIdentifiedElements.length}</p>
              </div>
              <div className="p-2 bg-blue-100 rounded-lg">
                <Plus className="w-6 h-6 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">未覆盖要素</p>
                <p className="text-2xl font-bold text-orange-600">{mockUncoveredElements.length}</p>
              </div>
              <div className="p-2 bg-orange-100 rounded-lg">
                <AlertTriangle className="w-6 h-6 text-orange-600" />
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">政策不一致</p>
                <p className="text-2xl font-bold text-red-600">{mockPolicyInconsistencies.length}</p>
              </div>
              <div className="p-2 bg-red-100 rounded-lg">
                <AlertCircle className="w-6 h-6 text-red-600" />
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">平均置信度</p>
                <p className="text-2xl font-bold text-green-600">91.7%</p>
              </div>
              <div className="p-2 bg-green-100 rounded-lg">
                <TrendingUp className="w-6 h-6 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 主要内容区域 */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="newly-identified" className="flex items-center gap-2">
            <Plus className="w-4 h-4" />
            新识别要素
            <Badge variant="secondary" className="ml-1">{mockNewlyIdentifiedElements.length}</Badge>
          </TabsTrigger>
          <TabsTrigger value="uncovered" className="flex items-center gap-2">
            <AlertTriangle className="w-4 h-4" />
            未覆盖要素
            <Badge variant="secondary" className="ml-1">{mockUncoveredElements.length}</Badge>
          </TabsTrigger>
          <TabsTrigger value="inconsistencies" className="flex items-center gap-2">
            <AlertCircle className="w-4 h-4" />
            政策不一致
            <Badge variant="secondary" className="ml-1">{mockPolicyInconsistencies.length}</Badge>
          </TabsTrigger>
        </TabsList>

        {/* 新识别要素标签页 */}
        <TabsContent value="newly-identified" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Plus className="w-5 h-5" />
                新识别稽查要素
              </CardTitle>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>要素名称</TableHead>
                    <TableHead>类型</TableHead>
                    <TableHead>来源政策</TableHead>
                    <TableHead>置信度</TableHead>
                    <TableHead>建议操作</TableHead>
                    <TableHead>操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {mockNewlyIdentifiedElements.map((element) => (
                    <TableRow key={element.id}>
                      <TableCell>
                        <div>
                          <div className="font-medium">{element.name}</div>
                          <div className="text-sm text-gray-500">{element.description}</div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">{element.type}</Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1">
                          <FileText className="w-4 h-4 text-gray-400" />
                          {element.sourcePolicy}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant={getConfidenceBadgeVariant(element.confidence)}>
                          {(element.confidence * 100).toFixed(0)}%
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1">
                          {element.suggestedActions.slice(0, 2).map((action, index) => (
                            <Badge key={index} variant="secondary" className="text-xs">
                              {action}
                            </Badge>
                          ))}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleViewDetail(element)}
                                >
                                  <Eye className="w-4 h-4" />
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent>查看详情</TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleExecuteAction(element, "创建稽查主题")}
                                >
                                  <Plus className="w-4 h-4" />
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent>创建稽查主题</TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        {/* 未覆盖要素标签页 */}
        <TabsContent value="uncovered" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <AlertTriangle className="w-5 h-5" />
                未覆盖稽查要素
              </CardTitle>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>要素名称</TableHead>
                    <TableHead>类型</TableHead>
                    <TableHead>当前覆盖情况</TableHead>
                    <TableHead>风险等级</TableHead>
                    <TableHead>建议主题</TableHead>
                    <TableHead>操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {mockUncoveredElements.map((element) => (
                    <TableRow key={element.id}>
                      <TableCell>
                        <div>
                          <div className="font-medium">{element.name}</div>
                          <div className="text-sm text-gray-500">{element.description}</div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">{element.type}</Badge>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm text-gray-600">{element.currentCoverage}</div>
                      </TableCell>
                      <TableCell>
                        <Badge variant={getRiskLevelBadgeVariant(element.riskLevel)}>
                          {element.riskLevel}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1">
                          {element.suggestedThemes.slice(0, 2).map((theme, index) => (
                            <Badge key={index} variant="secondary" className="text-xs">
                              {theme}
                            </Badge>
                          ))}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleViewDetail(element)}
                                >
                                  <Eye className="w-4 h-4" />
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent>查看详情</TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleExecuteAction(element, "创建稽查主题")}
                                >
                                  <Plus className="w-4 h-4" />
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent>创建稽查主题</TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        {/* 政策不一致标签页 */}
        <TabsContent value="inconsistencies" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <AlertCircle className="w-5 h-5" />
                政策不一致性分析
              </CardTitle>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>要素名称</TableHead>
                    <TableHead>变更类型</TableHead>
                    <TableHead>影响等级</TableHead>
                    <TableHead>建议操作</TableHead>
                    <TableHead>受影响主题</TableHead>
                    <TableHead>操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {mockPolicyInconsistencies.map((inconsistency) => (
                    <TableRow key={inconsistency.id}>
                      <TableCell>
                        <div>
                          <div className="font-medium">{inconsistency.elementName}</div>
                          <div className="text-sm text-gray-500">{inconsistency.elementType}</div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">{inconsistency.changeType}</Badge>
                      </TableCell>
                      <TableCell>
                        <Badge variant={getImpactLevelBadgeVariant(inconsistency.impactLevel)}>
                          {inconsistency.impactLevel}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Badge variant={getSuggestedActionBadgeVariant(inconsistency.suggestedAction)}>
                          {inconsistency.suggestedAction}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1">
                          {inconsistency.affectedThemes.slice(0, 2).map((theme, index) => (
                            <Badge key={index} variant="secondary" className="text-xs">
                              {theme}
                            </Badge>
                          ))}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleViewDetail(inconsistency)}
                                >
                                  <Eye className="w-4 h-4" />
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent>查看详情</TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleExecuteAction(inconsistency, "更新定义")}
                                >
                                  <Edit className="w-4 h-4" />
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent>更新定义</TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* 详情对话框 */}
      <Dialog open={isDetailDialogOpen} onOpenChange={setIsDetailDialogOpen}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>要素详情</DialogTitle>
            <DialogDescription>
              查看稽查要素的详细信息和分析结果
            </DialogDescription>
          </DialogHeader>
          {selectedElement && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium">要素名称</Label>
                  <p className="text-sm text-gray-600 mt-1">
                    {'name' in selectedElement ? selectedElement.name : selectedElement.elementName}
                  </p>
                </div>
                <div>
                  <Label className="text-sm font-medium">要素类型</Label>
                  <p className="text-sm text-gray-600 mt-1">
                    {'type' in selectedElement ? selectedElement.type : selectedElement.elementType}
                  </p>
                </div>
                <div>
                  <Label className="text-sm font-medium">来源政策</Label>
                  <p className="text-sm text-gray-600 mt-1">
                    {selectedElement.sourcePolicy}
                  </p>
                </div>
                <div>
                  <Label className="text-sm font-medium">提取日期</Label>
                  <p className="text-sm text-gray-600 mt-1">
                    {'extractionDate' in selectedElement ? selectedElement.extractionDate : 'N/A'}
                  </p>
                </div>
              </div>
              
              <div>
                <Label className="text-sm font-medium">描述</Label>
                <p className="text-sm text-gray-600 mt-1">
                  {'description' in selectedElement ? selectedElement.description : 
                   'currentDefinition' in selectedElement ? selectedElement.currentDefinition : ''}
                </p>
              </div>

              {'currentDefinition' in selectedElement && 'newDefinition' in selectedElement && (
                <div className="space-y-2">
                  <Label className="text-sm font-medium">定义对比</Label>
                  <div className="grid grid-cols-2 gap-4 p-4 bg-gray-50 rounded-lg">
                    <div>
                      <p className="text-xs font-medium text-gray-500 mb-1">当前定义</p>
                      <p className="text-sm">{selectedElement.currentDefinition}</p>
                    </div>
                    <div>
                      <p className="text-xs font-medium text-gray-500 mb-1">新政策定义</p>
                      <p className="text-sm">{selectedElement.newDefinition}</p>
                    </div>
                  </div>
                </div>
              )}

              <div>
                <Label className="text-sm font-medium">原始文本</Label>
                <div className="mt-1 p-3 bg-gray-50 rounded-lg">
                  <p className="text-sm text-gray-600">{selectedElement.originalText}</p>
                </div>
              </div>

              {'suggestedActions' in selectedElement && (
                <div>
                  <Label className="text-sm font-medium">建议操作</Label>
                  <div className="flex flex-wrap gap-2 mt-1">
                    {selectedElement.suggestedActions.map((action, index) => (
                      <Badge key={index} variant="secondary">{action}</Badge>
                    ))}
                  </div>
                </div>
              )}

              {'suggestedThemes' in selectedElement && (
                <div>
                  <Label className="text-sm font-medium">建议主题</Label>
                  <div className="flex flex-wrap gap-2 mt-1">
                    {selectedElement.suggestedThemes.map((theme, index) => (
                      <Badge key={index} variant="secondary">{theme}</Badge>
                    ))}
                  </div>
                </div>
              )}

              {'affectedThemes' in selectedElement && (
                <div>
                  <Label className="text-sm font-medium">受影响主题</Label>
                  <div className="flex flex-wrap gap-2 mt-1">
                    {selectedElement.affectedThemes.map((theme, index) => (
                      <Badge key={index} variant="outline">{theme}</Badge>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDetailDialogOpen(false)}>
              关闭
            </Button>
            <Button onClick={() => {
              setIsDetailDialogOpen(false)
              if (selectedElement) {
                handleExecuteAction(selectedElement, "执行操作")
              }
            }}>
              执行操作
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 操作对话框 */}
      <Dialog open={isActionDialogOpen} onOpenChange={setIsActionDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>执行操作</DialogTitle>
            <DialogDescription>
              确认执行 {actionType} 操作
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="action-note">操作备注</Label>
              <Textarea
                id="action-note"
                placeholder="请输入操作备注..."
                value={actionNote}
                onChange={(e) => setActionNote(e.target.value)}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsActionDialogOpen(false)}>
              取消
            </Button>
            <Button onClick={() => {
              console.log('执行操作:', actionType, actionNote)
              setIsActionDialogOpen(false)
              setActionNote("")
            }}>
              确认执行
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
} 