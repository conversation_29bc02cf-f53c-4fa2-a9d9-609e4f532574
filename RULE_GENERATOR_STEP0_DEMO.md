# 稽查主题智能化设计 - 规则设计要素引导功能

## 功能概述

在稽查主题智能化设计功能中，新增了**步骤0：规则设计要素引导**，这是一个预先自然语言对话框的意图明确步骤，旨在帮助业务人员更好地设置稽查目标。

## 功能特点

### 1. 自然语言对话界面
- 提供智能对话助手，帮助用户明确稽查需求
- 支持自然语言输入，如"我要查充电桩异常"
- 智能识别用户意图并提供相应建议

### 2. 业务语义选项引导
提供四个主要业务语义类别：

#### 基本信息
- 用电户
- 发电户  
- 台区
- 电能表计

#### 稽查对象
- 营销业务用户
- 工商业用户
- 居民用户
- 大工业用户
- 临时用电用户

#### 专业分类
- 计量采集
- 客户服务
- 电费计算
- 电能质量

#### 风险点
- 充电桩电量异常
- 工商业高耗能异常
- 功率因数异常
- 光伏客户档案异常
- 居民电量异常

### 3. 智能交互功能
- **对话式确认**：用户可以通过自然语言描述需求
- **选项引导**：系统根据对话内容推荐相关选项
- **实时预览**：动态生成稽查目标描述
- **灵活切换**：用户可以随时从对话模式切换到结构化选择

### 4. 信息传递机制
- 用户选择的业务语义选项会自动传递到下一步
- 自然语言描述会作为结构化编排的输入
- 支持中途直接进入结构化编排步骤

## 使用流程

### 步骤0：规则设计要素引导

1. **启动对话**
   - 系统显示欢迎消息："您好！我是您的稽查规则设计助手。请告诉我您想要查什么？"

2. **自然语言输入**
   - 用户可以输入如："我要查充电桩电量异常"
   - 系统会智能识别并回复相应建议

3. **业务语义选择**
   - 左侧提供四个业务语义类别
   - 用户可以勾选相关类别
   - 每个类别下可以选择具体选项

4. **实时预览**
   - 右侧实时显示生成的稽查目标描述
   - 格式：我要稽查[类别]：[选项1、选项2...]

5. **确认进入下一步**
   - 点击"确认并进入结构化编排"按钮
   - 所有选择的信息会传递到步骤1

## 技术实现

### 组件结构
```
Step0IntentGuide.tsx
├── 业务语义选项面板
│   ├── 基本信息选择
│   ├── 稽查对象选择
│   ├── 专业分类选择
│   └── 风险点选择
├── 自然语言对话界面
│   ├── 消息历史显示
│   ├── 用户输入框
│   └── 发送按钮
└── 稽查目标描述预览
    └── 可编辑文本框
```

### 状态管理
- `businessIntents`: 业务意图状态数组
- `chatMessages`: 对话消息历史
- `naturalLanguageInput`: 自然语言输入
- `generatedDescription`: 生成的描述

### 智能回复逻辑
系统根据用户输入的关键词提供智能回复：
- "充电"、"充电桩" → 推荐充电桩相关选项
- "工商业"、"高耗能" → 推荐工商业高耗能相关选项
- "功率因数" → 推荐功率因数相关选项

## 用户体验优化

### 1. 响应式设计
- 支持桌面端和移动端
- 左侧选项面板和右侧对话界面自适应布局

### 2. 交互反馈
- 选项选择状态清晰显示
- 对话消息有时间戳
- 按钮状态变化提供视觉反馈

### 3. 操作便利性
- 支持键盘回车发送消息
- 一键重置对话
- 可随时编辑生成的描述

## 后续步骤

完成步骤0后，用户将进入：
1. **步骤1：主题定义结构化编排** - 基于引导信息进行详细配置
2. **步骤2：语义化分析** - 生成语义化表达
3. **步骤3：脚本生成与确认** - 生成可执行脚本

## 总结

新增的规则设计要素引导功能大大提升了用户体验，通过自然语言对话和结构化选项的结合，帮助业务人员更直观、更高效地设置稽查目标，为后续的结构化编排提供了清晰的输入基础。 