"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Progress } from "@/components/ui/progress"
import { 
  Database, 
  Table, 
  FileText, 
  Users, 
  RefreshCw,
  Search,
  Filter,
  AlertCircle
} from "lucide-react"

// 模拟数据
const mockData = {
  totalAssets: 1250,
  totalTables: 856,
  totalFiles: 394,
  totalUsers: 45,
  growthRate: 12.5,
  issues: 23,
  categories: [
    { name: "客户数据", count: 320, color: "bg-blue-500" },
    { name: "交易数据", count: 280, color: "bg-green-500" },
    { name: "产品数据", count: 195, color: "bg-purple-500" },
    { name: "系统数据", count: 165, color: "bg-orange-500" },
    { name: "其他", count: 290, color: "bg-gray-500" }
  ],
  recentUpdates: [
    { table: "customer_info", update: "字段新增", time: "2小时前" },
    { table: "transaction_log", update: "索引优化", time: "4小时前" },
    { table: "product_catalog", update: "数据同步", time: "6小时前" }
  ]
}

export default function MetadataOverview() {
  const [activeTab, setActiveTab] = useState("overview")

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">元数据概览</h1>
          <p className="text-muted-foreground">
            数据资产总览、统计分析和分类管理
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            刷新数据
          </Button>
          <Button size="sm">
            <Search className="h-4 w-4 mr-2" />
            高级搜索
          </Button>
        </div>
      </div>

      {/* 核心指标卡片 */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">数据资产总数</CardTitle>
            <Database className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{mockData.totalAssets.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              <span className="text-green-600">+{mockData.growthRate}%</span> 较上月
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">数据表数量</CardTitle>
            <Table className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{mockData.totalTables.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              <span className="text-green-600">+8.2%</span> 较上月
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">文件数量</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{mockData.totalFiles.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              <span className="text-green-600">+15.3%</span> 较上月
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">活跃用户</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{mockData.totalUsers}</div>
            <p className="text-xs text-muted-foreground">
              <span className="text-red-600">-2.1%</span> 较上月
            </p>
          </CardContent>
        </Card>
      </div>

      {/* 主要内容区域 */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">数据概览</TabsTrigger>
          <TabsTrigger value="categories">分类管理</TabsTrigger>
          <TabsTrigger value="updates">最近更新</TabsTrigger>
          <TabsTrigger value="issues">问题监控</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            {/* 数据分类饼图 */}
            <Card>
              <CardHeader>
                <CardTitle>数据分类分布</CardTitle>
                <CardDescription>按业务分类的数据资产分布情况</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {mockData.categories.map((category, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <div className={`w-3 h-3 rounded-full ${category.color}`}></div>
                        <span className="text-sm font-medium">{category.name}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <span className="text-sm text-muted-foreground">
                          {category.count.toLocaleString()}
                        </span>
                        <Badge variant="secondary">
                          {((category.count / mockData.totalAssets) * 100).toFixed(1)}%
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* 数据质量指标 */}
            <Card>
              <CardHeader>
                <CardTitle>数据质量指标</CardTitle>
                <CardDescription>关键数据质量监控指标</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>数据完整性</span>
                    <span>98.5%</span>
                  </div>
                  <Progress value={98.5} className="h-2" />
                </div>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>数据准确性</span>
                    <span>96.2%</span>
                  </div>
                  <Progress value={96.2} className="h-2" />
                </div>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>数据一致性</span>
                    <span>97.8%</span>
                  </div>
                  <Progress value={97.8} className="h-2" />
                </div>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>数据及时性</span>
                    <span>99.1%</span>
                  </div>
                  <Progress value={99.1} className="h-2" />
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="categories" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>数据分类管理</CardTitle>
              <CardDescription>管理和配置数据资产分类</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                {mockData.categories.map((category, index) => (
                  <Card key={index} className="cursor-pointer hover:shadow-md transition-shadow">
                    <CardContent className="p-4">
                      <div className="flex items-center space-x-3">
                        <div className={`w-4 h-4 rounded-full ${category.color}`}></div>
                        <div className="flex-1">
                          <h3 className="font-medium">{category.name}</h3>
                          <p className="text-sm text-muted-foreground">
                            {category.count.toLocaleString()} 个资产
                          </p>
                        </div>
                        <Button variant="ghost" size="sm">
                          <Filter className="h-4 w-4" />
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="updates" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>最近更新</CardTitle>
              <CardDescription>数据资产的最近变更记录</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {mockData.recentUpdates.map((update, index) => (
                  <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <div>
                        <p className="font-medium">{update.table}</p>
                        <p className="text-sm text-muted-foreground">{update.update}</p>
                      </div>
                    </div>
                    <span className="text-sm text-muted-foreground">{update.time}</span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="issues" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>问题监控</CardTitle>
              <CardDescription>数据质量和元数据问题监控</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-center space-x-4 p-4 bg-red-50 border border-red-200 rounded-lg">
                <AlertCircle className="h-5 w-5 text-red-500" />
                <div>
                  <p className="font-medium text-red-800">发现 {mockData.issues} 个问题</p>
                  <p className="text-sm text-red-600">需要关注的数据质量问题</p>
                </div>
                <Button variant="outline" size="sm" className="ml-auto">
                  查看详情
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
