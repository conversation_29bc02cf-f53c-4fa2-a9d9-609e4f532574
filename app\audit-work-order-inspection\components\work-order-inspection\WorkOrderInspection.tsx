"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import InspectionDetail from "./InspectionDetail";

interface WorkOrder {
  id: string;
  title: string;
  type: string;
  status: string;
  submittedAt: string;
  score: number;
  complianceStatus: "compliant" | "non_compliant" | "pending";
}

const mockWorkOrders: WorkOrder[] = [
  {
    id: "WO-2024-001",
    title: "系统异常访问审计",
    type: "系统安全",
    status: "已完成",
    submittedAt: "2024-03-20",
    score: 95,
    complianceStatus: "compliant",
  },
  {
    id: "WO-2024-002",
    title: "数据泄露风险排查",
    type: "数据安全",
    status: "处理中",
    submittedAt: "2024-03-19",
    score: 75,
    complianceStatus: "non_compliant",
  },
];

export default function WorkOrderInspection() {
  const [workOrders, setWorkOrders] = useState<WorkOrder[]>(mockWorkOrders);
  const [selectedWorkOrder, setSelectedWorkOrder] = useState<WorkOrder | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const handleInspect = (workOrder: WorkOrder) => {
    setSelectedWorkOrder(workOrder);
    setIsDialogOpen(true);
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold">工单质检列表</h2>
      </div>

      <Card>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>工单编号</TableHead>
              <TableHead>标题</TableHead>
              <TableHead>类型</TableHead>
              <TableHead>状态</TableHead>
              <TableHead>提交时间</TableHead>
              <TableHead>质检得分</TableHead>
              <TableHead>合规状态</TableHead>
              <TableHead>操作</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {workOrders.map((workOrder) => (
              <TableRow key={workOrder.id}>
                <TableCell>{workOrder.id}</TableCell>
                <TableCell>{workOrder.title}</TableCell>
                <TableCell>{workOrder.type}</TableCell>
                <TableCell>{workOrder.status}</TableCell>
                <TableCell>{workOrder.submittedAt}</TableCell>
                <TableCell>{workOrder.score}</TableCell>
                <TableCell>
                  <Badge
                    variant={
                      workOrder.complianceStatus === "compliant"
                        ? "outline"
                        : workOrder.complianceStatus === "non_compliant"
                        ? "destructive"
                        : "secondary"
                    }
                  >
                    {workOrder.complianceStatus === "compliant" && "合规"}
                    {workOrder.complianceStatus === "non_compliant" && "不合规"}
                    {workOrder.complianceStatus === "pending" && "待检查"}
                  </Badge>
                </TableCell>
                <TableCell>
                  <Button variant="outline" size="sm" onClick={() => handleInspect(workOrder)}>
                    查看详情
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </Card>

      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>工单质检详情</DialogTitle>
          </DialogHeader>
          {selectedWorkOrder && <InspectionDetail workOrder={selectedWorkOrder} />}
        </DialogContent>
      </Dialog>
    </div>
  );
} 