"use client"

import { useState, useMemo } from "react"
import { <PERSON>, CardHeader, CardTitle, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Checkbox } from "@/components/ui/checkbox"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { 
  Search, 
  Filter, 
  Eye, 
  Edit, 
  CheckCircle, 
  Alert<PERSON>riangle, 
  Trash2, 
  Plus,
  Calendar,
  FileText,
  Tag,
  BookOpen,
  Download
} from "lucide-react"
import { 
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination"
import dynamic from "next/dynamic"

// 稽查要素类型定义
interface AuditElement {
  id: string
  name: string
  type: 'Entity' | 'Attribute' | 'Event' | 'Relationship'
  businessDescription: string
  source: string
  extractionDate: string
  reviewStatus: 'Pending Review' | 'Reviewed' | 'To Be Revised'
  tags: string[]
  originalText?: string
}

// 模拟数据
const mockAuditElements: AuditElement[] = [
  {
    id: "AE001",
    name: "工商业客户",
    type: "Entity",
    businessDescription: "指从事工业、商业活动的电力客户，包括制造业、服务业等各类企业客户",
    source: "政策文件A",
    extractionDate: "2024-01-15",
    reviewStatus: "Reviewed",
    tags: ["客户类型", "工商业"],
    originalText: "工商业客户是指从事工业、商业活动的电力客户，包括制造业、服务业等各类企业客户。"
  },
  {
    id: "AE002",
    name: "月度用电量",
    type: "Attribute",
    businessDescription: "客户在一个自然月内的总用电量，单位为千瓦时",
    source: "政策文件B",
    extractionDate: "2024-01-16",
    reviewStatus: "Pending Review",
    tags: ["用电量", "月度"],
    originalText: "月度用电量是指客户在一个自然月内的总用电量，单位为千瓦时。"
  },
  {
    id: "AE003",
    name: "用电量异常事件",
    type: "Event",
    businessDescription: "当客户用电量超过设定阈值或出现异常波动时触发的事件",
    source: "政策文件C",
    extractionDate: "2024-01-17",
    reviewStatus: "To Be Revised",
    tags: ["异常", "事件"],
    originalText: "用电量异常事件是指当客户用电量超过设定阈值或出现异常波动时触发的事件。"
  },
  {
    id: "AE004",
    name: "客户用电关系",
    type: "Relationship",
    businessDescription: "客户与用电量之间的关联关系，用于分析客户用电行为模式",
    source: "政策文件A",
    extractionDate: "2024-01-18",
    reviewStatus: "Reviewed",
    tags: ["关系", "用电行为"],
    originalText: "客户用电关系描述了客户与用电量之间的关联关系，用于分析客户用电行为模式。"
  },
  {
    id: "AE005",
    name: "高耗能客户",
    type: "Entity",
    businessDescription: "用电量较大、能耗较高的客户群体，通常需要重点监控",
    source: "政策文件D",
    extractionDate: "2024-01-19",
    reviewStatus: "Pending Review",
    tags: ["客户类型", "高耗能"],
    originalText: "高耗能客户是指用电量较大、能耗较高的客户群体，通常需要重点监控。"
  }
]

export function AuditKnowledgeManagement() {
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedType, setSelectedType] = useState<string>("all")
  const [selectedStatus, setSelectedStatus] = useState<string>("all")
  const [selectedSource, setSelectedSource] = useState<string>("all")
  const [selectedElements, setSelectedElements] = useState<string[]>([])
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [editingElement, setEditingElement] = useState<AuditElement | null>(null)
  const [currentPage, setCurrentPage] = useState(1)
  const itemsPerPage = 10
  const [activeTab, setActiveTab] = useState<'list' | 'graph'>("list")
  // 动态加载图谱组件，避免SSR问题
  const AuditElementGraph = useMemo(() => dynamic(() => import("./graph"), { ssr: false }), [])

  // 过滤后的数据
  const filteredElements = useMemo(() => {
    return mockAuditElements.filter(element => {
      const matchesSearch = element.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           element.businessDescription.toLowerCase().includes(searchTerm.toLowerCase())
      const matchesType = selectedType === "all" || element.type === selectedType
      const matchesStatus = selectedStatus === "all" || element.reviewStatus === selectedStatus
      const matchesSource = selectedSource === "all" || element.source === selectedSource
      
      return matchesSearch && matchesType && matchesStatus && matchesSource
    })
  }, [searchTerm, selectedType, selectedStatus, selectedSource])

  // 分页数据
  const paginatedElements = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage
    const endIndex = startIndex + itemsPerPage
    return filteredElements.slice(startIndex, endIndex)
  }, [filteredElements, currentPage])

  // 总页数
  const totalPages = Math.ceil(filteredElements.length / itemsPerPage)

  // 当过滤条件改变时重置到第一页
  const resetToFirstPage = () => {
    setCurrentPage(1)
  }

  // 处理搜索和过滤变化
  const handleSearchChange = (value: string) => {
    setSearchTerm(value)
    resetToFirstPage()
  }

  const handleTypeChange = (value: string) => {
    setSelectedType(value)
    resetToFirstPage()
  }

  const handleStatusChange = (value: string) => {
    setSelectedStatus(value)
    resetToFirstPage()
  }

  const handleSourceChange = (value: string) => {
    setSelectedSource(value)
    resetToFirstPage()
  }

  // 获取唯一的数据源列表
  const sources = useMemo(() => {
    const uniqueSources = [...new Set(mockAuditElements.map(element => element.source))]
    return uniqueSources
  }, [])

  // 处理选择变化
  const handleSelectElement = (elementId: string, checked: boolean) => {
    if (checked) {
      setSelectedElements(prev => [...prev, elementId])
    } else {
      setSelectedElements(prev => prev.filter(id => id !== elementId))
    }
  }

  // 处理全选
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedElements(filteredElements.map(element => element.id))
    } else {
      setSelectedElements([])
    }
  }

  // 批量操作
  const handleBulkAction = (action: string) => {
    console.log(`执行批量操作: ${action}`, selectedElements)
    // 这里可以添加实际的批量操作逻辑
    setSelectedElements([])
  }

  // 单个元素操作
  const handleElementAction = (action: string, element: AuditElement) => {
    switch (action) {
      case 'view':
      case 'edit':
        setEditingElement(element)
        setIsEditDialogOpen(true)
        break
      case 'approve':
        console.log('批准元素:', element.id)
        break
      case 'revise':
        console.log('标记为需要修订:', element.id)
        break
      case 'delete':
        console.log('删除元素:', element.id)
        break
    }
  }

  // 获取状态徽章颜色
  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'Reviewed':
        return 'default'
      case 'Pending Review':
        return 'secondary'
      case 'To Be Revised':
        return 'destructive'
      default:
        return 'outline'
    }
  }

  // 获取类型徽章颜色
  const getTypeBadgeVariant = (type: string) => {
    switch (type) {
      case 'Entity':
        return 'default'
      case 'Attribute':
        return 'secondary'
      case 'Event':
        return 'outline'
      case 'Relationship':
        return 'destructive'
      default:
        return 'outline'
    }
  }

  return (
    <div className="flex-1 p-6 space-y-6">
      {/* Tab切换栏 */}
      <div className="flex gap-4 border-b mb-4">
        <button
          className={`px-4 py-2 font-semibold border-b-2 transition-colors ${activeTab === 'list' ? 'border-blue-600 text-blue-600' : 'border-transparent text-gray-500 hover:text-blue-600'}`}
          onClick={() => setActiveTab('list')}
        >
          稽查要素列表
        </button>
        <button
          className={`px-4 py-2 font-semibold border-b-2 transition-colors ${activeTab === 'graph' ? 'border-blue-600 text-blue-600' : 'border-transparent text-gray-500 hover:text-blue-600'}`}
          onClick={() => setActiveTab('graph')}
        >
          知识图谱
        </button>
      </div>
      {/* Tab内容 */}
      {activeTab === 'list' ? (
        <>
          {/* 页面标题 */}
          <div className="space-y-2">
            <h1 className="text-3xl font-bold tracking-tight text-gray-900">稽查知识管理</h1>
            <p className="text-gray-600">集中管理稽查要素知识库，支持查看、编辑和优化提取的稽查要素</p>
          </div>

          {/* 统计信息卡片 */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">总要素数</p>
                    <p className="text-2xl font-bold text-gray-900">{mockAuditElements.length}</p>
                  </div>
                  <div className="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center">
                    <BookOpen className="h-4 w-4 text-blue-600" />
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">待审核</p>
                    <p className="text-2xl font-bold text-yellow-600">
                      {mockAuditElements.filter(e => e.reviewStatus === 'Pending Review').length}
                    </p>
                  </div>
                  <div className="h-8 w-8 rounded-full bg-yellow-100 flex items-center justify-center">
                    <AlertTriangle className="h-4 w-4 text-yellow-600" />
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">已审核</p>
                    <p className="text-2xl font-bold text-green-600">
                      {mockAuditElements.filter(e => e.reviewStatus === 'Reviewed').length}
                    </p>
                  </div>
                  <div className="h-8 w-8 rounded-full bg-green-100 flex items-center justify-center">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">需要修订</p>
                    <p className="text-2xl font-bold text-red-600">
                      {mockAuditElements.filter(e => e.reviewStatus === 'To Be Revised').length}
                    </p>
                  </div>
                  <div className="h-8 w-8 rounded-full bg-red-100 flex items-center justify-center">
                    <Edit className="h-4 w-4 text-red-600" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* 搜索和过滤区域 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Search className="h-5 w-5" />
                搜索与过滤
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* 搜索框 */}
              <div className="flex gap-4">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <Input
                      placeholder="搜索要素名称或描述..."
                      value={searchTerm}
                      onChange={(e) => handleSearchChange(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
                <Button variant="outline" className="flex items-center gap-2">
                  <Filter className="h-4 w-4" />
                  高级过滤
                </Button>
              </div>

              {/* 过滤选项 */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <Label htmlFor="type-filter">要素类型</Label>
                  <Select value={selectedType} onValueChange={handleTypeChange}>
                    <SelectTrigger>
                      <SelectValue placeholder="选择要素类型" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">全部类型</SelectItem>
                      <SelectItem value="Entity">实体 (Entity)</SelectItem>
                      <SelectItem value="Attribute">属性 (Attribute)</SelectItem>
                      <SelectItem value="Event">事件 (Event)</SelectItem>
                      <SelectItem value="Relationship">关系 (Relationship)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="status-filter">审核状态</Label>
                  <Select value={selectedStatus} onValueChange={handleStatusChange}>
                    <SelectTrigger>
                      <SelectValue placeholder="选择审核状态" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">全部状态</SelectItem>
                      <SelectItem value="Pending Review">待审核</SelectItem>
                      <SelectItem value="Reviewed">已审核</SelectItem>
                      <SelectItem value="To Be Revised">需要修订</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="source-filter">数据源</Label>
                  <Select value={selectedSource} onValueChange={handleSourceChange}>
                    <SelectTrigger>
                      <SelectValue placeholder="选择数据源" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">全部数据源</SelectItem>
                      {sources.map(source => (
                        <SelectItem key={source} value={source}>{source}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 批量操作工具栏 */}
          {selectedElements.length > 0 && (
            <Card>
              <CardContent className="pt-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">
                    已选择 {selectedElements.length} 个要素
                  </span>
                  <div className="flex gap-2">
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => handleBulkAction('approve')}
                      className="flex items-center gap-2"
                    >
                      <CheckCircle className="h-4 w-4" />
                      批量批准
                    </Button>
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => handleBulkAction('revise')}
                      className="flex items-center gap-2"
                    >
                      <AlertTriangle className="h-4 w-4" />
                      批量标记修订
                    </Button>
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => handleBulkAction('delete')}
                      className="flex items-center gap-2"
                    >
                      <Trash2 className="h-4 w-4" />
                      批量删除
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* 要素列表表格 */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center gap-2">
                  <BookOpen className="h-5 w-5" />
                  稽查要素列表
                </CardTitle>
                <div className="flex items-center gap-2">
                  <Button variant="outline" className="flex items-center gap-2">
                    <Download className="h-4 w-4" />
                    导出数据
                  </Button>
                  <Button className="flex items-center gap-2">
                    <Plus className="h-4 w-4" />
                    新增要素
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-12">
                      <Checkbox
                        checked={selectedElements.length === filteredElements.length && filteredElements.length > 0}
                        onCheckedChange={handleSelectAll}
                      />
                    </TableHead>
                    <TableHead>ID</TableHead>
                    <TableHead>要素名称</TableHead>
                    <TableHead>类型</TableHead>
                    <TableHead>业务描述</TableHead>
                    <TableHead>数据源</TableHead>
                    <TableHead>提取日期</TableHead>
                    <TableHead>审核状态</TableHead>
                    <TableHead>标签</TableHead>
                    <TableHead className="w-32">操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {paginatedElements.map((element) => (
                    <TableRow key={element.id}>
                      <TableCell>
                        <Checkbox
                          checked={selectedElements.includes(element.id)}
                          onCheckedChange={(checked) => handleSelectElement(element.id, checked as boolean)}
                        />
                      </TableCell>
                      <TableCell className="font-mono text-sm">{element.id}</TableCell>
                      <TableCell className="font-medium">{element.name}</TableCell>
                      <TableCell>
                        <Badge variant={getTypeBadgeVariant(element.type)}>
                          {element.type}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <div className="max-w-xs truncate cursor-help">
                                {element.businessDescription}
                              </div>
                            </TooltipTrigger>
                            <TooltipContent className="max-w-md">
                              <p>{element.businessDescription}</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </TableCell>
                      <TableCell>{element.source}</TableCell>
                      <TableCell>{element.extractionDate}</TableCell>
                      <TableCell>
                        <Badge variant={getStatusBadgeVariant(element.reviewStatus)}>
                          {element.reviewStatus === 'Pending Review' && '待审核'}
                          {element.reviewStatus === 'Reviewed' && '已审核'}
                          {element.reviewStatus === 'To Be Revised' && '需要修订'}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex flex-wrap gap-1">
                          {element.tags.slice(0, 2).map((tag, index) => (
                            <Badge key={index} variant="outline" className="text-xs">
                              {tag}
                            </Badge>
                          ))}
                          {element.tags.length > 2 && (
                            <Badge variant="outline" className="text-xs">
                              +{element.tags.length - 2}
                            </Badge>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleElementAction('view', element)}
                            className="h-8 w-8 p-0"
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleElementAction('edit', element)}
                            className="h-8 w-8 p-0"
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleElementAction('approve', element)}
                            className="h-8 w-8 p-0"
                          >
                            <CheckCircle className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleElementAction('revise', element)}
                            className="h-8 w-8 p-0"
                          >
                            <AlertTriangle className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleElementAction('delete', element)}
                            className="h-8 w-8 p-0 text-red-500 hover:text-red-700"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
              
              {/* 分页组件 */}
              {totalPages > 1 && (
                <div className="mt-4 flex items-center justify-between">
                  <div className="text-sm text-gray-600">
                    显示第 {(currentPage - 1) * itemsPerPage + 1} 到 {Math.min(currentPage * itemsPerPage, filteredElements.length)} 条，
                    共 {filteredElements.length} 条记录
                  </div>
                  <Pagination>
                    <PaginationContent>
                      <PaginationItem>
                        <PaginationPrevious 
                          onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                          className={currentPage === 1 ? "pointer-events-none opacity-50" : "cursor-pointer"}
                        />
                      </PaginationItem>
                      
                      {/* 页码 */}
                      {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => {
                        // 显示当前页附近的页码
                        if (
                          page === 1 ||
                          page === totalPages ||
                          (page >= currentPage - 1 && page <= currentPage + 1)
                        ) {
                          return (
                            <PaginationItem key={page}>
                              <PaginationLink
                                onClick={() => setCurrentPage(page)}
                                isActive={currentPage === page}
                                className="cursor-pointer"
                              >
                                {page}
                              </PaginationLink>
                            </PaginationItem>
                          )
                        } else if (
                          page === currentPage - 2 ||
                          page === currentPage + 2
                        ) {
                          return (
                            <PaginationItem key={page}>
                              <PaginationEllipsis />
                            </PaginationItem>
                          )
                        }
                        return null
                      })}
                      
                      <PaginationItem>
                        <PaginationNext 
                          onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                          className={currentPage === totalPages ? "pointer-events-none opacity-50" : "cursor-pointer"}
                        />
                      </PaginationItem>
                    </PaginationContent>
                  </Pagination>
                </div>
              )}
            </CardContent>
          </Card>

          {/* 要素详情/编辑对话框 */}
          <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
            <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>要素详情</DialogTitle>
                <DialogDescription>
                  查看和编辑稽查要素的详细信息
                </DialogDescription>
              </DialogHeader>
              {editingElement && (
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="element-id">要素ID</Label>
                      <Input id="element-id" value={editingElement.id} readOnly />
                    </div>
                    <div>
                      <Label htmlFor="element-name">要素名称</Label>
                      <Input id="element-name" value={editingElement.name} />
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="element-type">要素类型</Label>
                      <Select value={editingElement.type}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Entity">实体 (Entity)</SelectItem>
                          <SelectItem value="Attribute">属性 (Attribute)</SelectItem>
                          <SelectItem value="Event">事件 (Event)</SelectItem>
                          <SelectItem value="Relationship">关系 (Relationship)</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Label htmlFor="element-status">审核状态</Label>
                      <Select value={editingElement.reviewStatus}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Pending Review">待审核</SelectItem>
                          <SelectItem value="Reviewed">已审核</SelectItem>
                          <SelectItem value="To Be Revised">需要修订</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  
                  <div>
                    <Label htmlFor="element-description">业务描述</Label>
                    <Textarea 
                      id="element-description" 
                      value={editingElement.businessDescription}
                      rows={3}
                    />
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="element-source">数据源</Label>
                      <Input id="element-source" value={editingElement.source} />
                    </div>
                    <div>
                      <Label htmlFor="element-date">提取日期</Label>
                      <Input id="element-date" value={editingElement.extractionDate} />
                    </div>
                  </div>
                  
                  <div>
                    <Label htmlFor="element-tags">标签</Label>
                    <Input 
                      id="element-tags" 
                      value={editingElement.tags.join(', ')}
                      placeholder="用逗号分隔多个标签"
                    />
                  </div>
                  
                  {editingElement.originalText && (
                    <div>
                      <Label htmlFor="element-original">原始文本片段</Label>
                      <div className="p-3 bg-gray-50 rounded-md text-sm">
                        <div className="flex items-center gap-2 mb-2">
                          <FileText className="h-4 w-4 text-gray-500" />
                          <span className="text-gray-600">来源文档片段</span>
                        </div>
                        <p className="text-gray-700">{editingElement.originalText}</p>
                      </div>
                    </div>
                  )}
                </div>
              )}
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
                  取消
                </Button>
                <Button onClick={() => setIsEditDialogOpen(false)}>
                  保存更改
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </>
      ) : (
        <AuditElementGraph />
      )}
    </div>
  )
} 