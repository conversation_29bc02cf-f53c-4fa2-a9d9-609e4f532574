"use client";

import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import StandardManagement from "./components/standard-management/StandardManagement";
import WorkOrderInspection from "./components/work-order-inspection/WorkOrderInspection";
import SpotCheck from "./components/spot-check/SpotCheck";
import InspectionResults from "./components/inspection-results/InspectionResults";
import InspectionAnalytics from "./components/inspection-analytics/InspectionAnalytics";

export default function AuditWorkOrderInspection() {
  return (
    <div className="container mx-auto p-6">
      <h1 className="text-2xl font-bold mb-6">稽查工单质检</h1>
      
      <Tabs defaultValue="standards" className="w-full">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="standards">质检基准管理</TabsTrigger>
          <TabsTrigger value="inspection">工单合规性质检</TabsTrigger>
          <TabsTrigger value="spotcheck">工单抽检</TabsTrigger>
          <TabsTrigger value="results">质检结果</TabsTrigger>
          <TabsTrigger value="analytics">统计分析</TabsTrigger>
        </TabsList>

        <TabsContent value="standards">
          <StandardManagement />
        </TabsContent>

        <TabsContent value="inspection">
          <WorkOrderInspection />
        </TabsContent>

        <TabsContent value="spotcheck">
          <SpotCheck />
        </TabsContent>

        <TabsContent value="results">
          <InspectionResults />
        </TabsContent>

        <TabsContent value="analytics">
          <InspectionAnalytics />
        </TabsContent>
      </Tabs>
    </div>
  );
} 