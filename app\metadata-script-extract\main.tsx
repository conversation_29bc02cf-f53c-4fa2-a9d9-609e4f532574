"use client";

import React, { useState } from "react";
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ger, TabsContent } from "@/components/ui/tabs";
import { Table, TableHeader, TableRow, TableHead, TableBody, TableCell } from "@/components/ui/table";
import { Upload, FileText, Eye, CheckCircle, Edit2 } from "lucide-react";

export default function MetadataScriptExtractMain() {
  const [activeTab, setActiveTab] = useState("upload");
  const [sqlContent, setSqlContent] = useState("");
  const [extracted, setExtracted] = useState<any>(null);
  const [step, setStep] = useState(0); // 0: 输入, 1: 预览, 2: 编辑, 3: 完成

  // 模拟解析SQL脚本，实际应调用后端或本地解析
  const mockExtract = (sql: string) => {
    // 这里只做简单mock，实际应用正则/AST等
    return {
      tables: [
        { name: "user_info", columns: [
          { name: "user_id", type: "BIGINT", pk: true },
          { name: "username", type: "VARCHAR(50)", pk: false },
        ] },
        { name: "power_consumption", columns: [
          { name: "meter_id", type: "STRING", pk: true },
          { name: "user_id", type: "BIGINT", pk: false },
          { name: "consumption_value", type: "DECIMAL(10,2)", pk: false },
        ] },
      ],
      views: [],
      procedures: [],
      indexes: [
        { table: "user_info", name: "PRIMARY", columns: ["user_id"] },
        { table: "user_info", name: "idx_username", columns: ["username"] },
      ],
      relations: [
        { from: "power_consumption.user_id", to: "user_info.user_id", type: "foreign" },
      ],
    };
  };

  const handleExtract = () => {
    setExtracted(mockExtract(sqlContent));
    setStep(1);
  };

  const handleConfirm = () => {
    // 实际应提交到后端并触发元数据刷新
    setStep(3);
  };

  return (
    <div className="flex-1 space-y-6 p-6">
      {/* <div className="space-y-2">
        <h1 className="text-3xl font-bold tracking-tight text-gray-900 flex items-center gap-2">
          <FileText className="h-7 w-7 text-blue-500" /> SQL脚本元数据提取
        </h1>
        <p className="text-gray-600">支持上传或粘贴SQL脚本，智能解析并提取元数据信息，确认后自动入库并更新关系图。</p>
      </div> */}
      <Card>
        <CardHeader>
          <CardTitle>脚本输入</CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab} className="mb-4">
            <TabsList>
              <TabsTrigger value="upload"><Upload className="w-4 h-4 mr-1" />上传文件</TabsTrigger>
              <TabsTrigger value="paste"><Edit2 className="w-4 h-4 mr-1" />粘贴内容</TabsTrigger>
            </TabsList>
            <TabsContent value="upload">
              <Input type="file" accept=".sql" onChange={e => {
                const file = e.target.files?.[0];
                if (file) {
                  const reader = new FileReader();
                  reader.onload = (ev) => setSqlContent(ev.target?.result as string || "");
                  reader.readAsText(file);
                }
              }} />
            </TabsContent>
            <TabsContent value="paste">
              <Textarea rows={8} value={sqlContent} onChange={e => setSqlContent(e.target.value)} placeholder="粘贴SQL脚本内容..." />
            </TabsContent>
          </Tabs>
          <Button className="mt-2" onClick={handleExtract} disabled={!sqlContent}>智能解析并预览</Button>
        </CardContent>
      </Card>

      {step >= 1 && (
        <Card className="mt-6">
          <CardHeader>
            <CardTitle><Eye className="w-5 h-5 mr-2 inline" />元数据预览与确认</CardTitle>
          </CardHeader>
          <CardContent>
            {extracted ? (
              <>
                <div className="mb-4">
                  <h3 className="font-semibold text-lg mb-2">表结构</h3>
                  {extracted.tables.map((table: any) => (
                    <div key={table.name} className="mb-2">
                      <div className="font-medium text-blue-700">{table.name}</div>
                      <Table className="mb-2">
                        <TableHeader>
                          <TableRow>
                            <TableHead>字段名</TableHead>
                            <TableHead>类型</TableHead>
                            <TableHead>主键</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {table.columns.map((col: any) => (
                            <TableRow key={col.name}>
                              <TableCell>{col.name}</TableCell>
                              <TableCell>{col.type}</TableCell>
                              <TableCell>{col.pk ? "是" : ""}</TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </div>
                  ))}
                </div>
                {/* 视图、存储过程、索引、关系等可扩展 */}
                <div className="flex gap-2 mt-4">
                  <Button variant="outline" onClick={() => setStep(0)}>返回修改</Button>
                  <Button onClick={handleConfirm} className="bg-green-600 text-white">确认导入</Button>
                </div>
              </>
            ) : <div className="text-gray-500">未解析到元数据</div>}
          </CardContent>
        </Card>
      )}

      {step === 3 && (
        <Card className="mt-6">
          <CardHeader>
            <CardTitle><CheckCircle className="w-5 h-5 mr-2 inline text-green-600" />导入成功</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-green-700">元数据已成功导入，元数据总览和关系图已自动更新。</div>
            <Button className="mt-4" onClick={() => setStep(0)}>继续导入其他脚本</Button>
          </CardContent>
        </Card>
      )}
    </div>
  );
} 