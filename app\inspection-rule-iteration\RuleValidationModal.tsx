'use client'

import React, { useState, useEffect } from 'react'
import { DraftRule } from '../../utils/draftRule'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { <PERSON><PERSON>, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import {
  <PERSON>,
  Pause,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Clock,
  Database,
  Target,
  BarChart3,
  Settings,
  RefreshCw,
  Download,
  Eye,
  Filter,
  Search,
} from 'lucide-react'

interface RuleValidationModalProps {
  rule: DraftRule | null
  isOpen: boolean
  onClose: () => void
}

interface ValidationResult {
  id: string
  sampleData: any
  isHit: boolean
  confidence: number
  reason: string
  timestamp: string
}

interface ValidationStats {
  totalSamples: number
  hitCount: number
  missCount: number
  accuracy: number
  precision: number
  recall: number
  f1Score: number
}

const RuleValidationModal: React.FC<RuleValidationModalProps> = ({
  rule,
  isOpen,
  onClose,
}) => {
  const [validationMode, setValidationMode] = useState<'manual' | 'auto'>('auto')
  const [isValidating, setIsValidating] = useState(false)
  const [validationProgress, setValidationProgress] = useState(0)
  const [validationResults, setValidationResults] = useState<ValidationResult[]>([])
  const [validationStats, setValidationStats] = useState<ValidationStats>({
    totalSamples: 0,
    hitCount: 0,
    missCount: 0,
    accuracy: 0,
    precision: 0,
    recall: 0,
    f1Score: 0,
  })
  const [selectedTab, setSelectedTab] = useState('overview')
  const [filterStatus, setFilterStatus] = useState('all')
  const [searchTerm, setSearchTerm] = useState('')

  // 模拟样本数据
  const sampleData = [
    {
      id: 'S001',
      customer_id: 'C001',
      monthly_consumption: 12000,
      customer_type: 'industrial',
      region: '华东',
      payment_history: 'good',
    },
    {
      id: 'S002',
      customer_id: 'C002',
      monthly_consumption: 8000,
      customer_type: 'commercial',
      region: '华南',
      payment_history: 'good',
    },
    {
      id: 'S003',
      customer_id: 'C003',
      monthly_consumption: 15000,
      customer_type: 'industrial',
      region: '华北',
      payment_history: 'poor',
    },
    {
      id: 'S004',
      customer_id: 'C004',
      monthly_consumption: 5000,
      customer_type: 'residential',
      region: '华东',
      payment_history: 'good',
    },
    {
      id: 'S005',
      customer_id: 'C005',
      monthly_consumption: 18000,
      customer_type: 'industrial',
      region: '西南',
      payment_history: 'good',
    },
  ]

  // 模拟验证逻辑
  const validateSample = (sample: any): ValidationResult => {
    // 基于规则的高耗能客户识别逻辑
    const isHit = 
      sample.customer_type === 'industrial' && 
      sample.monthly_consumption > 10000

    const confidence = isHit ? 0.95 : 0.85
    const reason = isHit 
      ? '符合高耗能客户条件：工商业客户且月用电量超过10000千瓦时'
      : '不符合高耗能客户条件'

    return {
      id: sample.id,
      sampleData: sample,
      isHit,
      confidence,
      reason,
      timestamp: new Date().toISOString(),
    }
  }

  // 开始验证
  const startValidation = async () => {
    setIsValidating(true)
    setValidationProgress(0)
    setValidationResults([])

    if (validationMode === 'auto') {
      // 自动化验证
      for (let i = 0; i < sampleData.length; i++) {
        await new Promise(resolve => setTimeout(resolve, 500)) // 模拟验证延迟
        const result = validateSample(sampleData[i])
        setValidationResults(prev => [...prev, result])
        setValidationProgress(((i + 1) / sampleData.length) * 100)
      }
    } else {
      // 手动验证 - 只验证第一个样本
      const result = validateSample(sampleData[0])
      setValidationResults([result])
      setValidationProgress(100)
    }

    setIsValidating(false)
    calculateStats()
  }

  // 计算验证统计
  const calculateStats = () => {
    const results = validationResults
    const totalSamples = results.length
    const hitCount = results.filter(r => r.isHit).length
    const missCount = totalSamples - hitCount
    const accuracy = totalSamples > 0 ? (hitCount / totalSamples) * 100 : 0
    const precision = hitCount > 0 ? (hitCount / (hitCount + 2)) * 100 : 0 // 模拟假阳性
    const recall = hitCount > 0 ? (hitCount / (hitCount + 1)) * 100 : 0 // 模拟假阴性
    const f1Score = precision + recall > 0 ? (2 * precision * recall) / (precision + recall) : 0

    setValidationStats({
      totalSamples,
      hitCount,
      missCount,
      accuracy,
      precision,
      recall,
      f1Score,
    })
  }

  // 过滤结果
  const filteredResults = validationResults.filter(result => {
    const matchesStatus = 
      filterStatus === 'all' || 
      (filterStatus === 'hit' && result.isHit) ||
      (filterStatus === 'miss' && !result.isHit)
    
    const matchesSearch = 
      searchTerm === '' ||
      result.sampleData.customer_id.toLowerCase().includes(searchTerm.toLowerCase()) ||
      result.reason.toLowerCase().includes(searchTerm.toLowerCase())

    return matchesStatus && matchesSearch
  })

  // 导出验证结果
  const exportResults = () => {
    const content = `规则验证结果报告
规则ID: ${rule?.id}
规则描述: ${rule?.generatedDescription}
验证时间: ${new Date().toLocaleString()}
验证模式: ${validationMode === 'auto' ? '自动化验证' : '手动验证'}

验证统计:
- 总样本数: ${validationStats.totalSamples}
- 命中数: ${validationStats.hitCount}
- 未命中数: ${validationStats.missCount}
- 准确率: ${validationStats.accuracy.toFixed(2)}%
- 精确率: ${validationStats.precision.toFixed(2)}%
- 召回率: ${validationStats.recall.toFixed(2)}%
- F1分数: ${validationStats.f1Score.toFixed(2)}%

详细结果:
${validationResults.map(r => 
  `样本ID: ${r.id}
  命中: ${r.isHit ? '是' : '否'}
  置信度: ${(r.confidence * 100).toFixed(1)}%
  原因: ${r.reason}
  `
).join('\n')}`

    const blob = new Blob([content], { type: 'text/plain;charset=utf-8' })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `规则验证结果_${rule?.id}_${new Date().toISOString().split('T')[0]}.txt`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
  }

  useEffect(() => {
    if (validationResults.length > 0) {
      calculateStats()
    }
  }, [validationResults])

  if (!isOpen || !rule) return null

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Target className="h-5 w-5" />
            命中范围样本性验证: {rule.generatedDescription}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* 验证控制面板 */}
          <Card className="bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200">
            <CardHeader className="pb-4">
              <CardTitle className="text-blue-700 text-lg flex items-center gap-2">
                <Settings className="h-5 w-5" />
                验证控制面板
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label className="text-blue-700 font-medium">验证模式</Label>
                  <Select value={validationMode} onValueChange={(value: 'manual' | 'auto') => setValidationMode(value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="auto">自动化验证</SelectItem>
                      <SelectItem value="manual">手动验证</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="space-y-2">
                  <Label className="text-blue-700 font-medium">样本数量</Label>
                  <div className="text-sm text-blue-600">
                    {validationMode === 'auto' ? `${sampleData.length} 个样本` : '1 个样本'}
                  </div>
                </div>

                <div className="flex items-end">
                  <Button 
                    onClick={startValidation} 
                    disabled={isValidating}
                    className="w-full"
                  >
                    {isValidating ? (
                      <>
                        <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                        验证中...
                      </>
                    ) : (
                      <>
                        <Play className="mr-2 h-4 w-4" />
                        开始验证
                      </>
                    )}
                  </Button>
                </div>
              </div>

              {isValidating && (
                <div className="mt-4 space-y-2">
                  <div className="flex justify-between text-sm text-blue-700">
                    <span>验证进度</span>
                    <span>{validationProgress.toFixed(1)}%</span>
                  </div>
                  <Progress value={validationProgress} className="h-2" />
                </div>
              )}
            </CardContent>
          </Card>

          {/* 验证结果 */}
          {validationResults.length > 0 && (
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center gap-2">
                    <BarChart3 className="h-5 w-5" />
                    验证结果
                  </CardTitle>
                  <div className="flex items-center gap-2">
                    <Button variant="outline" size="sm" onClick={exportResults}>
                      <Download className="mr-2 h-4 w-4" />
                      导出结果
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <Tabs value={selectedTab} onValueChange={setSelectedTab}>
                  <TabsList className="grid w-full grid-cols-3">
                    <TabsTrigger value="overview">概览统计</TabsTrigger>
                    <TabsTrigger value="details">详细结果</TabsTrigger>
                    <TabsTrigger value="analysis">分析建议</TabsTrigger>
                  </TabsList>

                  <TabsContent value="overview" className="mt-6">
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                      <Card className="bg-green-50 border-green-200">
                        <CardContent className="pt-4">
                          <div className="flex items-center gap-2">
                            <CheckCircle className="h-5 w-5 text-green-600" />
                            <div>
                              <div className="text-2xl font-bold text-green-700">
                                {validationStats.hitCount}
                              </div>
                              <div className="text-sm text-green-600">命中样本</div>
                            </div>
                          </div>
                        </CardContent>
                      </Card>

                      <Card className="bg-red-50 border-red-200">
                        <CardContent className="pt-4">
                          <div className="flex items-center gap-2">
                            <XCircle className="h-5 w-5 text-red-600" />
                            <div>
                              <div className="text-2xl font-bold text-red-700">
                                {validationStats.missCount}
                              </div>
                              <div className="text-sm text-red-600">未命中样本</div>
                            </div>
                          </div>
                        </CardContent>
                      </Card>

                      <Card className="bg-blue-50 border-blue-200">
                        <CardContent className="pt-4">
                          <div className="flex items-center gap-2">
                            <Target className="h-5 w-5 text-blue-600" />
                            <div>
                              <div className="text-2xl font-bold text-blue-700">
                                {validationStats.accuracy.toFixed(1)}%
                              </div>
                              <div className="text-sm text-blue-600">准确率</div>
                            </div>
                          </div>
                        </CardContent>
                      </Card>

                      <Card className="bg-purple-50 border-purple-200">
                        <CardContent className="pt-4">
                          <div className="flex items-center gap-2">
                            <BarChart3 className="h-5 w-5 text-purple-600" />
                            <div>
                              <div className="text-2xl font-bold text-purple-700">
                                {validationStats.f1Score.toFixed(2)}
                              </div>
                              <div className="text-sm text-purple-600">F1分数</div>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    </div>

                    <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-4">
                      <Card>
                        <CardHeader>
                          <CardTitle className="text-sm">精确率与召回率</CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="space-y-3">
                            <div>
                              <div className="flex justify-between text-sm mb-1">
                                <span>精确率</span>
                                <span>{validationStats.precision.toFixed(1)}%</span>
                              </div>
                              <Progress value={validationStats.precision} className="h-2" />
                            </div>
                            <div>
                              <div className="flex justify-between text-sm mb-1">
                                <span>召回率</span>
                                <span>{validationStats.recall.toFixed(1)}%</span>
                              </div>
                              <Progress value={validationStats.recall} className="h-2" />
                            </div>
                          </div>
                        </CardContent>
                      </Card>

                      <Card>
                        <CardHeader>
                          <CardTitle className="text-sm">验证建议</CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="space-y-2">
                            {validationStats.accuracy >= 80 ? (
                              <div className="flex items-center gap-2 text-green-600">
                                <CheckCircle className="h-4 w-4" />
                                <span className="text-sm">规则验证通过，准确率良好</span>
                              </div>
                            ) : (
                              <div className="flex items-center gap-2 text-yellow-600">
                                <AlertTriangle className="h-4 w-4" />
                                <span className="text-sm">建议调整规则逻辑以提高准确率</span>
                              </div>
                            )}
                            
                            {validationStats.hitCount === 0 && (
                              <div className="flex items-center gap-2 text-red-600">
                                <XCircle className="h-4 w-4" />
                                <span className="text-sm">未发现命中样本，请检查规则条件</span>
                              </div>
                            )}
                          </div>
                        </CardContent>
                      </Card>
                    </div>
                  </TabsContent>

                  <TabsContent value="details" className="mt-6">
                    <div className="space-y-4">
                      <div className="flex items-center gap-4">
                        <div className="relative flex-1">
                          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                          <Input
                            placeholder="搜索样本ID或原因..."
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                            className="pl-10"
                          />
                        </div>
                        <Select value={filterStatus} onValueChange={setFilterStatus}>
                          <SelectTrigger className="w-48">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="all">全部结果</SelectItem>
                            <SelectItem value="hit">命中样本</SelectItem>
                            <SelectItem value="miss">未命中样本</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="rounded-md border">
                        <Table>
                          <TableHeader>
                            <TableRow>
                              <TableHead>样本ID</TableHead>
                              <TableHead>客户信息</TableHead>
                              <TableHead>验证结果</TableHead>
                              <TableHead>置信度</TableHead>
                              <TableHead>原因分析</TableHead>
                              <TableHead>操作</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {filteredResults.map((result) => (
                              <TableRow key={result.id}>
                                <TableCell className="font-medium">
                                  {result.id}
                                </TableCell>
                                <TableCell>
                                  <div className="text-sm space-y-1">
                                    <div>客户ID: {result.sampleData.customer_id}</div>
                                    <div>类型: {result.sampleData.customer_type}</div>
                                    <div>用电量: {result.sampleData.monthly_consumption}</div>
                                  </div>
                                </TableCell>
                                <TableCell>
                                  <Badge 
                                    className={result.isHit 
                                      ? 'bg-green-100 text-green-800 border-green-300' 
                                      : 'bg-red-100 text-red-800 border-red-300'
                                    }
                                  >
                                    {result.isHit ? '命中' : '未命中'}
                                  </Badge>
                                </TableCell>
                                <TableCell>
                                  <div className="flex items-center gap-2">
                                    <div className="w-16 bg-gray-200 rounded-full h-2">
                                      <div 
                                        className="bg-blue-600 h-2 rounded-full" 
                                        style={{ width: `${result.confidence * 100}%` }}
                                      ></div>
                                    </div>
                                    <span className="text-sm">
                                      {(result.confidence * 100).toFixed(0)}%
                                    </span>
                                  </div>
                                </TableCell>
                                <TableCell className="max-w-xs">
                                  <div className="text-sm text-gray-600">
                                    {result.reason}
                                  </div>
                                </TableCell>
                                <TableCell>
                                  <Button variant="outline" size="sm">
                                    <Eye className="h-3 w-3 mr-1" />
                                    详情
                                  </Button>
                                </TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </div>
                    </div>
                  </TabsContent>

                  <TabsContent value="analysis" className="mt-6">
                    <div className="space-y-6">
                      <Card className="bg-yellow-50 border-yellow-200">
                        <CardHeader>
                          <CardTitle className="text-yellow-700 flex items-center gap-2">
                            <AlertTriangle className="h-5 w-5" />
                            验证分析
                          </CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="space-y-4">
                            <div>
                              <h4 className="font-medium text-yellow-800 mb-2">规则有效性评估</h4>
                              <div className="text-sm text-yellow-700 space-y-1">
                                <p>• 当前规则在测试样本上的准确率为 {validationStats.accuracy.toFixed(1)}%</p>
                                <p>• 命中 {validationStats.hitCount} 个样本，未命中 {validationStats.missCount} 个样本</p>
                                <p>• 规则逻辑基本符合预期，但建议进一步优化</p>
                              </div>
                            </div>

                            <div>
                              <h4 className="font-medium text-yellow-800 mb-2">优化建议</h4>
                              <div className="text-sm text-yellow-700 space-y-2">
                                <div className="flex items-start gap-2">
                                  <div className="w-2 h-2 bg-yellow-500 rounded-full mt-2 flex-shrink-0"></div>
                                  <p>考虑调整用电量阈值，当前10000千瓦时可能过于严格</p>
                                </div>
                                <div className="flex items-start gap-2">
                                  <div className="w-2 h-2 bg-yellow-500 rounded-full mt-2 flex-shrink-0"></div>
                                  <p>可以增加地区维度，不同地区的用电标准可能不同</p>
                                </div>
                                <div className="flex items-start gap-2">
                                  <div className="w-2 h-2 bg-yellow-500 rounded-full mt-2 flex-shrink-0"></div>
                                  <p>建议增加历史用电趋势分析，提高规则准确性</p>
                                </div>
                              </div>
                            </div>
                          </div>
                        </CardContent>
                      </Card>

                      <Card>
                        <CardHeader>
                          <CardTitle className="flex items-center gap-2">
                            <Target className="h-5 w-5" />
                            下一步行动
                          </CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <Button 
                              variant="outline" 
                              className="h-auto p-4 flex flex-col items-start gap-2"
                              onClick={() => window.location.href = `/rule-generator?ruleId=${rule.id}`}
                            >
                              <RefreshCw className="h-5 w-5" />
                              <div className="text-left">
                                <div className="font-medium">迭代优化规则</div>
                                <div className="text-sm text-gray-500">基于验证结果调整规则逻辑</div>
                              </div>
                            </Button>
                            
                            <Button 
                              variant="outline" 
                              className="h-auto p-4 flex flex-col items-start gap-2"
                              disabled
                            >
                              <Database className="h-5 w-5" />
                              <div className="text-left">
                                <div className="font-medium">扩大验证范围</div>
                                <div className="text-sm text-gray-500">使用更多样本进行验证</div>
                              </div>
                            </Button>
                          </div>
                        </CardContent>
                      </Card>
                    </div>
                  </TabsContent>
                </Tabs>
              </CardContent>
            </Card>
          )}
        </div>
      </DialogContent>
    </Dialog>
  )
}

export default RuleValidationModal 