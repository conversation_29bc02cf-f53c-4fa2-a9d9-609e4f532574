import React, { useState, useEffect, useRef } from "react"
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card"
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from "@/components/ui/select"
import { Label } from "@/components/ui/label"
import { Button } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Textarea } from "@/components/ui/textarea"
import { Plus, Trash2, CheckCircle, Circle, Sparkles } from "lucide-react"
import LogicSelector from "@/components/ui/logic-selector"

export type BaseCondition = {
  label: string;
  type: 'basic' | 'derived';
  field: string;
  operator?: string;
  value?: string;
  unit?: string;
};

export interface RuleTemplate {
  id: string
  name: string
  description: string
  category: string
  structure: {
    scenario: string
    targetUsers: string[]
    baseConditions: string[]
    branchConditions: any[]
  }
  objectTypes: string[]
  professionalCategories: string[]
  riskPoints: string[]
}

// 选项与字段定义（可根据需要传props或本地mock）
const objectTypeOptions = ["用电户", "发电户", "台区", "电能表计"]
const professionalCategoryOptions = ["计量采集", "客户服务"]
const riskPointOptions = ["充电桩电量异常", "工商业高耗能异常", "功率因数异常", "光伏客户档案异常", "居民电量异常"]
const fieldOptions = {
  targetUsers: ["营销业务用户", "工商业用户", "居民用户", "大工业用户", "临时用电用户"],
  baseConditions: [
    { label: "用户名称包含'充电'字段", type: 'derived' as 'derived', field: "用户名称", operator: "包含", value: "充电", unit: "" },
    { label: "用电地址包含'充电'字段", type: 'derived' as 'derived', field: "用电地址", operator: "包含", value: "充电", unit: "" },
    { label: "电价执行居民合表电价", type: 'derived' as 'derived', field: "电价", operator: "等于", value: "居民合表电价", unit: "" },
    { label: "客户类型为'工商业'", type: 'derived' as 'derived', field: "客户类型", operator: "等于", value: "工商业", unit: "" },
    { label: "电压等级为'10kV'或'35kV'", type: 'derived' as 'derived', field: "电压等级", operator: "等于", value: "10kV/35kV", unit: "" },
    { label: "用电性质为'生产用电'", type: 'derived' as 'derived', field: "用电性质", operator: "等于", value: "生产用电", unit: "" },
    { label: "装机容量≥100kW", type: 'derived' as 'derived', field: "装机容量", operator: "大于", value: "100", unit: "kW" },
    { label: "装机容量<100kW", type: 'derived' as 'derived', field: "装机容量", operator: "小于", value: "100", unit: "kW" },
    { label: "行业类型", type: 'basic' as 'basic', field: "行业类型" },
    { label: "抄表周期", type: 'basic' as 'basic', field: "抄表周期" },
    { label: "用户名称", type: 'basic' as 'basic', field: "用户名称" },
    { label: "用电地址", type: 'basic' as 'basic', field: "用电地址" },
    { label: "电价", type: 'basic' as 'basic', field: "电价" },
    { label: "客户类型", type: 'basic' as 'basic', field: "客户类型" },
    { label: "电压等级", type: 'basic' as 'basic', field: "电压等级" },
    { label: "用电性质", type: 'basic' as 'basic', field: "用电性质" },
    { label: "装机容量", type: 'basic' as 'basic', field: "装机容量" },
  ],
  operators: ["包含", "等于", "大于", "小于", "介于", "大于等于", "小于等于", "不等于", "不包含"],
  units: ["千瓦时", "千瓦", "功率因数", "百分比", "元"],
}

// 预定义规则模板
const ruleTemplates: RuleTemplate[] = [
  {
    id: "ev_charging_anomaly",
    name: "电动汽车充电桩电量异常",
    description: "针对充电桩用户的电量异常监控",
    category: "用电异常",
    structure: {
      scenario: "电动汽车充电桩电量异常",
      targetUsers: ["营销业务用户"],
      baseConditions: ["用户名称包含'充电'字段", "用电地址包含'充电'字段", "电价执行居民合表电价"],
      branchConditions: [
        // {
        //   type: 'group',
        //   logic: 'AND',
        //   conditions: [
        //     {
        //       type: 'group',
        //       logic: 'AND',
        //       conditions: [
        //         {
        //           type: 'condition',
        //           field: '抄表周期',
        //           label: "抄表周期为'单月'",
        //           operator: '等于',
        //           value: '单月',
        //           unit: ''
        //         },
        //         {
        //           type: 'condition',
        //           field: '月度发行电量',
        //           label: "月度发行电量（抄见电量）≥5000千瓦时",
        //           operator: '大于',
        //           value: '5000',
        //           unit: '千瓦时'
        //         }
        //       ]
        //     },
        //     {
        //       type: 'group',
        //       logic: 'AND',
        //       conditions: [
        //         {
        //           type: 'condition',
        //           field: '抄表周期',
        //           label: "抄表周期为'双月'",
        //           operator: '等于',
        //           value: '双月',
        //           unit: ''
        //         },
        //         {
        //           type: 'condition',
        //           field: '月度发行电量',
        //           label: "月度发行电量（抄见电量）≥10000千瓦时",
        //           operator: '大于',
        //           value: '10000',
        //           unit: '千瓦时'
        //         }
        //       ]
        //     }
        //   ]
        // }
        {
          type: 'group',
          logic: 'AND',
          conditions: [
            {
              type: 'condition',
              field: '抄表周期',
              label: "抄表周期为'单月'",
              operator: '等于',
              value: '单月',
              unit: ''
            },
            {
              type: 'condition',
              field: '月度发行电量',
              label: "月度发行电量（抄见电量）",
              operator: '大于等于',
              value: '5000',
              unit: '千瓦时'
            }
          ]
        },
        {
          type: 'group',
          logic: 'AND',
          conditions: [
            {
              type: 'condition',
              field: '抄表周期',
              label: "抄表周期为'双月'",
              operator: '等于',
              value: '双月',
              unit: ''
            },
            {
              type: 'condition',
              field: '月度发行电量',
              label: "月度发行电量（抄见电量）",
              operator: '大于等于',
              value: '10000',
              unit: '千瓦时'
            }
          ]
        }
      ]
    },
    objectTypes: ["用电户", "电能表计"],
    professionalCategories: ["计量采集"],
    riskPoints: ["充电桩电量异常"],
  },
  {
    id: "high_consumption_commercial",
    name: "工商业高耗能异常",
    description: "工商业用户异常高耗能监控",
    category: "用电异常",
    structure: {
      scenario: "工商业高耗能异常",
      targetUsers: ["工商业用户"],
      baseConditions: ["客户类型为'工商业'", "电压等级为'10kV'或'35kV'"],
      branchConditions: [
        {
          condition: "行业类型为'制造业'",
          threshold: "50000",
          unit: "千瓦时",
        },
        {
          condition: "行业类型为'服务业'",
          threshold: "20000",
          unit: "千瓦时",
        },
      ],
    },
    objectTypes: ["用电户"],
    professionalCategories: ["客户服务"],
    riskPoints: ["工商业高耗能异常"],
  },
  {
    id: "power_factor_anomaly",
    name: "功率因数不达标",
    description: "功率因数低于标准值的监控",
    category: "电能质量",
    structure: {
      scenario: "功率因数不达标",
      targetUsers: ["工商业用户", "大工业用户"],
      baseConditions: ["电压等级为'10kV'以上", "用电性质为'生产用电'"],
      branchConditions: [
        {
          condition: "装机容量≥100kW",
          threshold: "0.9",
          unit: "功率因数",
        },
        {
          condition: "装机容量<100kW",
          threshold: "0.85",
          unit: "功率因数",
        },
      ],
    },
    objectTypes: ["用电户"],
    professionalCategories: ["计量采集"],
    riskPoints: ["功率因数异常"],
  },
]

export interface Step1TemplateSelectProps {
  selectedTemplate: RuleTemplate | null
  setSelectedTemplate: (t: RuleTemplate | null) => void
  customRule: any
  setCustomRule: (r: any) => void
  generatedDescription: string
  setGeneratedDescription: (d: string) => void
  nextStep: () => void
  aiDetectConflict: () => void
  conflictResult: any[]
}

// 分支条件递归组件
function ConditionGroup({ group, onChange, onDelete, fieldOptions, path = [] }: any) {
  // 修改当前组的某个子项
  const updateChild = (idx: number, newItem: any) => {
    if (newItem.field === '抄表周期') newItem.unit = ''
    const newConds = (group.conditions ?? []).map((c: any, i: number) => (i === idx ? newItem : c))
    onChange({ ...group, conditions: newConds }, path)
  }
  // 新增：直接替换某个条件对象
  const updateConditionObject = (idx: number, newObj: any) => {
    if (newObj.field === '抄表周期') newObj.unit = ''
    const newConds = (group.conditions ?? []).map((c: any, i: number) => (i === idx ? newObj : c))
    onChange({ ...group, conditions: newConds }, path)
  }
  // 删除子项
  const removeChild = (idx: number) => {
    const newConds = (group.conditions ?? []).filter((_: any, i: number) => i !== idx)
    onChange({ ...group, conditions: newConds }, path)
  }
  // 添加普通条件
  const addCondition = () => {
    onChange({ ...group, conditions: [...(group.conditions ?? []), { type: "condition", field: "", operator: "", value: "", unit: "" }] }, path)
  }
  // 添加子组
  const addGroup = () => {
    onChange({ ...group, conditions: [...(group.conditions ?? []), { type: 'group', logic: 'AND', conditions: [] }] }, path)
  }
  // 切换逻辑
  const setLogic = (logic: "AND" | "OR" | "NOT") => {
    onChange({ ...group, logic }, path)
  }
  // 新增：常用派生/复合原子快捷添加
  const derivedAtoms = fieldOptions.baseConditions.filter((c: any) => c.type === 'derived')
  return (
    <div className="p-3 border rounded space-y-2 bg-gray-50">
      <div className="flex items-center gap-2 mb-2">
        <label className="text-xs mr-1">逻辑</label>
        <LogicSelector value={group.logic} onChange={setLogic} />
        {onDelete && (
          <button type="button" className="h-6 w-6 p-0" onClick={onDelete}>×</button>
        )}
      </div>
      <div className="flex flex-col gap-2">
        {(group.conditions ?? []).map((item: any, idx: number) =>
          item.type === "condition" ? (
            <div key={idx} className="flex items-center gap-2">
              <select
                value={item.field || ''}
                onChange={e => {
                  const field = e.target.value
                  const opt = fieldOptions.baseConditions.find((c: any) => c.field === field)
                  if (opt) {
                    updateConditionObject(idx, {
                      ...item,
                      field: opt.field,
                      label: opt.label,
                      operator: opt.operator || '',
                      value: item.value || '',
                      unit: item.unit || ''
                    })
                  } else {
                    updateConditionObject(idx, {
                      ...item,
                      field,
                      label: field,
                      operator: '',
                      value: '',
                      unit: ''
                    })
                  }
                }}
                className="w-40 h-7 text-xs"
              >
                <option value="">选择条件字段</option>
                {fieldOptions.baseConditions.filter((opt: any) => opt.field && !/['"']/.test(opt.field)).map((opt: any, idx: number) => (
                  <option key={opt.field + '-' + idx} value={opt.field}>{opt.field}</option>
                ))}
                {/* 若当前值不在候选项中也能显示 */}
                {item.field && !fieldOptions.baseConditions.some((c: any) => c.field === item.field) && (
                  <option key={item.field + '-custom'} value={item.field}>{item.field}</option>
                )}
              </select>
              <select
                value={item.operator || ''}
                onChange={e => updateChild(idx, { ...item, operator: e.target.value })}
                className="w-20 h-7 text-xs"
              >
                <option value="">操作符</option>
                {fieldOptions.operators.map((opt: any, idx: number) => (
                  <option key={opt + '-' + idx} value={opt}>{opt}</option>
                ))}
                {/* 若当前值不在候选项中也能显示 */}
                {item.operator && !fieldOptions.operators.includes(item.operator) && (
                  <option key={item.operator + '-custom'} value={item.operator}>{item.operator}</option>
                )}
              </select>
              <input
                className="w-24 h-7 text-xs"
                placeholder="阈值"
                value={item.value || ''}
                onChange={e => updateChild(idx, { ...item, value: e.target.value })}
              />
              <select
                value={item.unit || ''}
                onChange={e => updateChild(idx, { ...item, unit: e.target.value })}
                className="w-20 h-7 text-xs"
                style={{ display: (item.field === '抄表周期') ? 'none' : undefined }}
              >
                <option value="">单位</option>
                {fieldOptions.units.map((opt: any, idx: number) => (
                  <option key={opt + '-' + idx} value={opt}>{opt}</option>
                ))}
                {/* 若当前值不在候选项中也能显示 */}
                {item.unit && !fieldOptions.units.includes(item.unit) && (
                  <option key={item.unit + '-custom'} value={item.unit}>{item.unit}</option>
                )}
              </select>
              <button type="button" className="h-6 w-6 p-0" onClick={() => removeChild(idx)}>×</button>
            </div>
          ) : (
            <div key={idx} className="ml-4">
              <ConditionGroup
                group={item}
                onChange={(newGroup: any, childPath: any) => {
                  const newConds = (group.conditions ?? []).map((c: any, i: number) =>
                    i === idx ? newGroup : c
                  )
                  onChange({ ...group, conditions: newConds }, path)
                }}
                onDelete={() => removeChild(idx)}
                fieldOptions={fieldOptions}
                path={[...path, idx]}
              />
            </div>
          )
        )}
        <div className="flex gap-2 mt-2">
          <button type="button" className="w-fit text-xs" onClick={addCondition}>+ 添加条件</button>
          <button type="button" className="w-fit text-xs" onClick={addGroup}>+ 添加子组</button>
        </div>
      </div>
    </div>
  )
}

// 递归转换分支条件，兼容嵌套结构和老格式
function convertBranchConditions(branchConditions: any[]): any[] {
  return branchConditions.map((b: any) => {
    if (b.type === 'group') {
      return {
        type: 'group',
        logic: b.logic,
        conditions: convertBranchConditions(b.conditions)
      }
    }
    // 兼容老数据
    if (typeof b.condition === 'string') {
      // 1. 字段为'值'
      const reg1 = b.condition.match(/^(.+)为['\"](.+)['\"]$/);
      if (reg1) {
        return {
          type: 'condition',
          field: reg1[1].trim(),
          label: b.condition,
          operator: '等于',
          value: reg1[2].trim() || b.threshold || '',
          unit: b.unit || ''
        };
      }
      // 2. 字段包含'值'
      const reg2 = b.condition.match(/^(.+)包含['\"](.+)['\"]$/);
      if (reg2) {
        return {
          type: 'condition',
          field: reg2[1].trim(),
          label: b.condition,
          operator: '包含',
          value: reg2[2].trim() || b.threshold || '',
          unit: b.unit || ''
        };
      }
      // 3. 字段>=值 或 字段≥值
      const reg3 = b.condition.match(/^(.+)[≥>=](.+)$/);
      if (reg3) {
        return {
          type: 'condition',
          field: reg3[1].trim(),
          label: b.condition,
          operator: '大于',
          value: reg3[2].trim() || b.threshold || '',
          unit: b.unit || ''
        };
      }
      // 4. 字段<值
      const reg4 = b.condition.match(/^(.+)<(.+)$/);
      if (reg4) {
        return {
          type: 'condition',
          field: reg4[1].trim(),
          label: b.condition,
          operator: '小于',
          value: reg4[2].trim() || b.threshold || '',
          unit: b.unit || ''
        };
      }
      // 5. 兜底
      return {
        type: 'condition',
        field: b.condition,
        label: b.condition,
        operator: '',
        value: b.threshold,
        unit: b.unit
      };
    }
    // 已经是新结构
    return b;
  });
}

const targetObjectDetailOptions: string[] = []
const periodOptions = ["每月", "每周", "每日"]

// 逻辑对象主体选项
const logicObjectSubjects = ["月度发行电量", "用电申请容量"]

const Step1TemplateSelect: React.FC<Step1TemplateSelectProps> = (props) => {
  // 本地筛选状态
  const [objectTypeFilter, setObjectTypeFilter] = useState<string>("all")
  const [professionalCategoryFilter, setProfessionalCategoryFilter] = useState<string>("all")
  const [riskPointFilter, setRiskPointFilter] = useState<string>("all")

  // 基础条件多选
  const baseConditions: BaseCondition[] = props.customRule.baseConditions || []
  const setBaseConditions = (conds: BaseCondition[]) => {
    props.setCustomRule({ ...props.customRule, baseConditions: conds })
  }
  // 筛查对象多选
  const targetUsers: string[] = props.customRule.targetUsers || []
  const setTargetUsers = (users: string[]) => {
    props.setCustomRule({ ...props.customRule, targetUsers: users })
  }
  // 分支条件（高级条件）递归结构
  const branchConditions = props.customRule.branchConditions || []
  const setBranchConditions = (conds: any[]) => {
    props.setCustomRule({ ...props.customRule, branchConditions: conds })
  }

  // 新增：目标对象细分与周期
  const targetObjectDetails: string[] = props.customRule.targetObjectDetails || []
  const setTargetObjectDetails = (arr: string[]) => {
    props.setCustomRule({ ...props.customRule, targetObjectDetails: arr })
  }
  const period: string = props.customRule.period || ""
  const setPeriod = (val: string) => {
    props.setCustomRule({ ...props.customRule, period: val })
  }

  // 是否用户手动编辑过描述
  const userEdited = useRef(false)

  // 规则描述自动生成逻辑
  const autoGenerateDescription = () => {
    const rule = props.customRule
    let description = rule.scenario ? `${rule.scenario}，` : ''
    if (rule.period) {
      description += `${rule.period}筛查，`
    }
    if (rule.targetUsers && rule.targetUsers.length > 0) {
      description += `对`
    }
    if (rule.targetObjectDetails && rule.targetObjectDetails.length > 0) {
      description += `（${rule.targetObjectDetails.join("、")}）`
    }
    if (
      (rule.targetUsers && rule.targetUsers.length > 0 && rule.targetUsers.length != 1) ||
      (rule.targetObjectDetails && rule.targetObjectDetails.length > 0)
    ) {
      description += '，'
    }
    // 基础条件区按baseConditionAtoms顺序和逻辑生成
    if (baseConditionAtoms && baseConditionAtoms.length > 0 && baseConditionAtoms.some(item => item.atom)) {
      description += baseConditionAtoms
        .filter(item => item.atom)
        .map((item, idx) => {
          const logic = idx > 0 ? (item.logic || '') : ''
          return `${logic ? logic : ''}${item.atom}`
        })
        .join('')
      description += `的`
    }
    description += `${rule.targetUsers.join('、')}，`
    if (rule.branchConditions && rule.branchConditions.length > 0) {
      description += `开展符合以下情况的稽查监控：\n`
      rule.branchConditions.forEach((group: any, idx: number) => {
        if (group.type === 'group') {
          description += `${idx + 1}. ` + renderBranchGroupDesc(group) + '\n'
        }
      })
    }
    return description
  }
  // 递归渲染分支条件组，第一层外不加括号
  const renderBranchGroupDesc = (group: any, isTop = true): string => {
    if (!group.conditions || group.conditions.length === 0) return ''
    let str = ''
    group.conditions.forEach((item: any, idx: number) => {
      if (item.type === 'condition') {
        str += `${item.label || item.field}${item.operator ? item.operator : ''}${item.value ? item.value : ''}${item.unit ? item.unit : ''}`
      } else if (item.type === 'group') {
        str += renderBranchGroupDesc(item, false)
      }
      if (idx < group.conditions.length - 1) {
        let logic = group.logic
        if (logic === 'AND') logic = '且'
        else if (logic === 'OR') logic = '或'
        else if (logic === 'NOT') logic = '非'
        str += ` ${logic} `
      }
    })
    if (!isTop) str = `(${str})`
    return str
  }

  // 自动生成描述副作用
  useEffect(() => {
    if (!userEdited.current) {
      const desc = autoGenerateDescription()
      props.setGeneratedDescription(desc)
    }
    // eslint-disable-next-line
  }, [props.customRule])

  // 用户手动编辑描述时，标记已编辑
  const handleDescChange = (e: any) => {
    userEdited.current = true
    props.setGeneratedDescription(e.target.value)
  }

  // 模板筛选
  const filteredTemplates = ruleTemplates.filter(t => {
    return (
      (objectTypeFilter === "all" || t.objectTypes.includes(objectTypeFilter)) &&
      (professionalCategoryFilter === "all" || t.professionalCategories.includes(professionalCategoryFilter)) &&
      (riskPointFilter === "all" || t.riskPoints.includes(riskPointFilter))
    )
  })

  // 在Step1TemplateSelect组件内，添加分页相关状态
  const [currentPage, setCurrentPage] = useState(1)
  const pageSize = 5
  const totalPages = Math.ceil(filteredTemplates.length / pageSize)
  const pagedTemplates = filteredTemplates.slice((currentPage - 1) * pageSize, currentPage * pageSize)

  // 模板点选后自动填充参数
  const handleTemplateSelect = (template: RuleTemplate) => {
    userEdited.current = false
    props.setSelectedTemplate(template)
    // 填充参数
    const baseConds: BaseCondition[] = (template.structure.baseConditions as any[]).map((c: any) => {
      
      if (typeof c === 'string') {
        // 用 label 匹配
        const found = fieldOptions.baseConditions.find(opt => opt.label === c)
        console.log("c:", c, found);
        if (found && (found.type === 'basic' || found.type === 'derived')) {
          return { ...found, type: found.type as 'basic' | 'derived' } as BaseCondition
        }
        // fallback: 兜底类型
        return { label: c, type: 'basic' as 'basic', field: c } as BaseCondition
      }
      return { ...c, type: (c.type === 'basic' || c.type === 'derived') ? c.type : 'basic' } as BaseCondition
    })
    console.log("baseConds", baseConds)
    
    props.setCustomRule({
      scenario: template.structure.scenario,
      targetUsers: [...template.structure.targetUsers],
      baseConditions: baseConds,
      branchConditions: convertBranchConditions(template.structure.branchConditions)
    })
    // 同步 baseConditionAtoms
    setBaseConditionAtoms(
      baseConds.map((c, i) => ({ atom: c.label, logic: i === 0 ? undefined : '且' }))
    )
  }

  // 添加分支条件组
  const addBranchGroup = () => {
    setBranchConditions([
      ...branchConditions,
      { type: 'group', logic: 'AND', conditions: [] }
    ])
  }

  // 润色规则描述（本地模拟，可替换为API调用）
  const polishDescription = async (desc: string): Promise<string> => {
    // TODO: 替换为后端API调用
    // 简单本地模拟：逗号->顿号，优化语序
    let polished = desc
      .replace(/，/g, '，')
      .replace(/或/g, '或')
      .replace(/且/g, '且')
      .replace(/:/g, '：')
      .replace(/'/g, '"' ).replace(/'/g, '"' )
    // 可根据需要进一步优化
    return polished
  }

  // 基础条件区展示所有派生语义逻辑原子（type为'derived'），不再用label/field包含判断
  const relatedDerivedConditions = fieldOptions.baseConditions.filter(c => c.type === 'derived')

  // 基础条件原子和逻辑关系
  const logicOperators = ["且", "或", "非"]
  const atomOptions = fieldOptions.baseConditions.filter(c => c.type === 'derived').map(c => c.label)
  const [baseConditionAtoms, setBaseConditionAtoms] = useState<{atom: string, logic?: string}[]>(
    baseConditions.length > 0
      ? baseConditions.map((c, i) => ({ atom: c.label, logic: i === 0 ? undefined : '且' }))
      : [{ atom: '', logic: undefined }]
  )
  // 同步到props.customRule.baseConditions
  useEffect(() => {
    setBaseConditions(
      baseConditionAtoms.filter(item => item.atom).map(item => {
        console.log("item,", item, baseConditionAtoms)
        const found = fieldOptions.baseConditions.find(c => c.label === item.atom)
        return found || { label: item.atom, type: 'derived', field: item.atom }
      })
    )
    // eslint-disable-next-line
  }, [baseConditionAtoms])

  return (
    <div className="space-y-6">
      <div className="flex gap-6">
        {/* 左侧：模板筛选与列表 */}
        <Card className="w-1/4 min-w-[320px]">
          <CardHeader>
            <CardTitle className="text-sm">主题定义结构化编排</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            {/* 筛选区 */}
            <div className="mb-2 space-y-2">
              <div className="font-semibold text-xs text-gray-700 mb-1">
                查询稽查主题模板
              </div>
              <div className="flex items-center gap-2">
                <Label className="w-20 text-xs text-gray-600">稽查对象</Label>
                <Select
                  value={objectTypeFilter}
                  onValueChange={setObjectTypeFilter}
                >
                  <SelectTrigger className="flex-1 h-8 text-xs">
                    <SelectValue placeholder="按稽查对象筛选" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部稽查对象</SelectItem>
                    {objectTypeOptions.map((opt) => (
                      <SelectItem key={opt} value={opt}>
                        {opt}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="flex items-center gap-2">
                <Label className="w-20 text-xs text-gray-600">专业分类</Label>
                <Select
                  value={professionalCategoryFilter}
                  onValueChange={setProfessionalCategoryFilter}
                >
                  <SelectTrigger className="flex-1 h-8 text-xs">
                    <SelectValue placeholder="按专业分类筛选" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部专业分类</SelectItem>
                    {professionalCategoryOptions.map((opt) => (
                      <SelectItem key={opt} value={opt}>
                        {opt}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="flex items-center gap-2">
                <Label className="w-20 text-xs text-gray-600">风险点</Label>
                <Select
                  value={riskPointFilter}
                  onValueChange={setRiskPointFilter}
                >
                  <SelectTrigger className="flex-1 h-8 text-xs">
                    <SelectValue placeholder="按风险点筛选" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部风险点</SelectItem>
                    {riskPointOptions.map((opt) => (
                      <SelectItem key={opt} value={opt}>
                        {opt}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
            <Separator className="my-2" />
            {/* 模板列表区 */}
            <div>
              <div className="font-semibold text-xs text-gray-700 mb-2">
                稽查主题模板
              </div>
              <div style={{ maxHeight: 320, overflowY: 'auto' }}>
                {pagedTemplates.length === 0 && (
                  <div className="text-xs text-gray-400 text-center py-6">
                    无符合条件的模板
                  </div>
                )}
                {pagedTemplates.map((template) => (
                  <div
                    key={template.id}
                    className={`p-3 rounded border cursor-pointer transition-all mb-2 ${
                      props.selectedTemplate?.id === template.id
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => handleTemplateSelect(template)}
                  >
                    <div className="flex items-center gap-2 mb-2">
                      {props.selectedTemplate?.id === template.id ? (
                        <CheckCircle className="h-4 w-4 text-blue-500" />
                      ) : (
                        <Circle className="h-4 w-4 text-gray-400" />
                      )}
                      <span className="font-medium text-sm">
                        {template.name}
                      </span>
                    </div>
                    <p className="text-xs text-gray-600">
                      {template.description}
                    </p>
                    <Badge variant="secondary" className="text-xs mt-2">
                      {template.category}
                    </Badge>
                  </div>
                ))}
              </div>
              {/* 分页按钮 */}
              {totalPages > 1 && (
                <div className="flex justify-center items-center gap-2 mt-2">
                  <Button
                    size="sm"
                    variant="outline"
                    disabled={currentPage === 1}
                    onClick={() => setCurrentPage(currentPage - 1)}
                  >
                    上一页
                  </Button>
                  <span className="text-xs">
                    {currentPage} / {totalPages}
                  </span>
                  <Button
                    size="sm"
                    variant="outline"
                    disabled={currentPage === totalPages}
                    onClick={() => setCurrentPage(currentPage + 1)}
                  >
                    下一页
                  </Button>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
        {/* 右侧：参数点选、条件、描述 */}
        <div className="flex-1 space-y-4">
          <Card className="bg-blue-50 border-blue-100 rounded-xl transition-colors duration-300 shadow-sm hover:shadow-lg">
            <CardHeader>
              <CardTitle className="text-blue-500 text-sm">基本信息</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex flex-row items-start gap-8">
                {/* 左侧：目标对象 */}
                <div className="flex-1 min-w-[180px]">
                  <div className="mb-2 font-semibold text-xs text-blue-500">
                    目标对象
                  </div>
                  <div className="flex flex-wrap gap-2 mb-2">
                    {fieldOptions.targetUsers.map((user) => (
                      <div key={user} className="flex items-center space-x-2">
                        <Checkbox
                          id={user}
                          checked={targetUsers.includes(user)}
                          onCheckedChange={(checked) => {
                            if (checked) {
                              setTargetUsers([...targetUsers, user])
                            } else {
                              setTargetUsers(
                                targetUsers.filter((u) => u !== user)
                              )
                            }
                          }}
                          className="accent-blue-400 transition-colors duration-300"
                        />
                        <Label
                          htmlFor={user}
                          className="text-sm text-blue-700 transition-colors duration-300"
                        >
                          {user}
                        </Label>
                      </div>
                    ))}
                  </div>
                  <div className="grid grid-cols-3 gap-2 mb-2">
                    {targetObjectDetailOptions.map((detail) => (
                      <div key={detail} className="flex items-center space-x-2">
                        <Checkbox
                          id={detail}
                          checked={targetObjectDetails.includes(detail)}
                          onCheckedChange={(checked) => {
                            if (checked) {
                              setTargetObjectDetails([
                                ...targetObjectDetails,
                                detail,
                              ])
                            } else {
                              setTargetObjectDetails(
                                targetObjectDetails.filter((d) => d !== detail)
                              )
                            }
                          }}
                          className="accent-blue-400 transition-colors duration-300"
                        />
                        <Label
                          htmlFor={detail}
                          className="text-xs text-blue-700 transition-colors duration-300"
                        >
                          {detail}
                        </Label>
                      </div>
                    ))}
                  </div>
                </div>
                {/* 右侧：筛查周期 */}
                <div className="flex flex-col items-start min-w-[180px]">
                  <div className="mb-2 font-semibold text-xs text-blue-500">
                    筛查周期
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {periodOptions.map((opt) => (
                      <Button
                        key={opt}
                        size="sm"
                        variant={period === opt ? 'default' : 'outline'}
                        className={`text-xs px-3 py-1 rounded-full border-blue-200 transition-colors duration-300 ${
                          period === opt
                            ? 'bg-blue-400/80 text-white hover:bg-blue-400'
                            : 'bg-blue-100 text-blue-700 hover:bg-blue-200'
                        }`}
                        onClick={() => setPeriod(opt)}
                      >
                        {opt}
                      </Button>
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card className="bg-blue-50 border-blue-100 rounded-xl transition-colors duration-300 shadow-sm hover:shadow-lg">
            <CardHeader className="pb-3">
              {/* （直接筛查对象） */}
              <CardTitle className="text-blue-500 text-sm">基础条件</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center gap-2 flex-wrap">
                {baseConditionAtoms.map((item, idx) => (
                  <React.Fragment key={idx}>
                    {idx > 0 && (
                      <Select
                        value={item.logic}
                        onValueChange={(val) => {
                          const newArr = [...baseConditionAtoms]
                          newArr[idx].logic = val
                          setBaseConditionAtoms(newArr)
                        }}
                      >
                        <SelectTrigger className="w-16 h-8 text-xs bg-blue-100 border-blue-200 text-blue-700 rounded-lg transition-colors duration-300">
                          <SelectValue placeholder="逻辑" />
                        </SelectTrigger>
                        <SelectContent>
                          {logicOperators.map((op) => (
                            <SelectItem key={op} value={op}>
                              {op}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    )}
                    <Select
                      value={item.atom}
                      onValueChange={(val) => {
                        const newArr = [...baseConditionAtoms]
                        newArr[idx].atom = val
                        setBaseConditionAtoms(newArr)
                      }}
                    >
                      <SelectTrigger className="w-56 h-8 text-xs bg-blue-100 border-blue-200 text-blue-700 rounded-lg transition-colors duration-300">
                        <SelectValue placeholder="请选择基础条件" />
                      </SelectTrigger>
                      <SelectContent>
                        {atomOptions.map((opt: string, idx: number) => (
                          <SelectItem key={opt + '-' + idx} value={opt}>
                            {opt}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    {/* 删除按钮，至少保留一个 */}
                    {baseConditionAtoms.length > 1 && (
                      <Button
                        size="icon"
                        variant="ghost"
                        className="text-blue-700 rounded-full transition-colors duration-300 hover:bg-blue-100"
                        onClick={() =>
                          setBaseConditionAtoms(
                            baseConditionAtoms.filter((_, i) => i !== idx)
                          )
                        }
                      >
                        ×
                      </Button>
                    )}
                  </React.Fragment>
                ))}
                {/* 添加按钮 */}
                <Button
                  size="sm"
                  variant="outline"
                  className="bg-blue-100 text-blue-700 border-blue-200 rounded-full transition-colors duration-300 hover:bg-blue-200"
                  onClick={() =>
                    setBaseConditionAtoms([
                      ...baseConditionAtoms,
                      {
                        atom: '',
                        logic:
                          baseConditionAtoms.length === 0 ? undefined : '且',
                      },
                    ])
                  }
                >
                  +
                </Button>
              </div>
            </CardContent>
          </Card>
          {/* 分支条件（高级条件）递归组合区 */}
          <Card className="bg-blue-50 border-blue-100 rounded-xl transition-colors duration-300 shadow-sm hover:shadow-lg">
            <CardHeader className="pb-3">
              <CardTitle className="text-blue-500 text-sm flex items-center justify-between">
                {/* （高级组合） */}
                分支条件
                <div className="flex items-center gap-2">
                  {/* 派生/复合原子下拉搜索添加 */}
                  <Select
                    onValueChange={(label) => {
                      let atom = fieldOptions.baseConditions.find(
                        (c: any) => c.type === 'derived' && c.label === label
                      )
                      if (!atom) {
                        // 逻辑对象主体
                        atom = {
                          label: label as string,
                          type: 'basic',
                          field: label as string,
                        }
                      }
                      if (atom) {
                        setBranchConditions([
                          ...branchConditions,
                          {
                            type: 'group',
                            logic: 'AND',
                            conditions: [{ ...atom, type: 'condition' }],
                          },
                        ])
                      }
                    }}
                  >
                    <SelectTrigger className="w-48 h-8 text-xs bg-blue-100 border-blue-200 text-blue-700 rounded-lg transition-colors duration-300">
                      <SelectValue placeholder="添加常用派生/复合原子或逻辑对象主体" />
                    </SelectTrigger>
                    <SelectContent>
                      {fieldOptions.baseConditions
                        .filter((c: any) => c.type === 'derived')
                        .map((atom, idx) => (
                          <SelectItem
                            key={atom.label + '-' + idx}
                            value={atom.label}
                          >
                            {atom.label}
                          </SelectItem>
                        ))}
                      {logicObjectSubjects.map((subject, idx) => (
                        <SelectItem key={subject + '-' + idx} value={subject}>
                          {subject}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <Button
                    size="sm"
                    className="bg-blue-100 text-blue-700 border-blue-200 rounded-full transition-colors duration-300 hover:bg-blue-200"
                    onClick={addBranchGroup}
                  >
                    <Plus className="h-3 w-3 mr-1" />
                    添加分支组
                  </Button>
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {branchConditions.map((group: any, idx: number) => (
                  <div key={idx} className="space-y-2">
                    <div className="font-bold text-xs mb-1 text-blue-500">
                      {idx + 1}.
                    </div>
                    <div className="bg-blue-100 p-2 rounded-lg transition-colors duration-300">
                      <ConditionGroup
                        group={group}
                        onChange={(newGroup: any) => {
                          const newGroups = branchConditions.map(
                            (g: any, i: number) => (i === idx ? newGroup : g)
                          )
                          setBranchConditions(newGroups)
                        }}
                        onDelete={() =>
                          setBranchConditions(
                            branchConditions.filter(
                              (_: any, i: number) => i !== idx
                            )
                          )
                        }
                        fieldOptions={fieldOptions}
                      />
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
          <Card className="bg-blue-50 border-blue-100 rounded-xl transition-colors duration-300 shadow-sm hover:shadow-lg">
            <CardHeader className="pb-3">
              <CardTitle className="text-blue-500 text-sm flex items-center gap-2">
                编排结果：稽查主题描述
                <div className="flex items-center gap-2 ml-2">
                  <Button
                    size="sm"
                    variant="outline"
                    className="flex items-center gap-1 bg-blue-100 text-blue-700 border-blue-200 rounded-full transition-colors duration-300 hover:bg-blue-200"
                    onClick={async () => {
                      const polished = await polishDescription(props.generatedDescription)
                      props.setGeneratedDescription(polished)
                    }}
                  >
                    <Sparkles className="h-4 w-4" /> AI 一键润色
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    className="flex items-center gap-1 bg-blue-100 text-blue-700 border-blue-200 rounded-full transition-colors duration-300 hover:bg-blue-200"
                    onClick={props.aiDetectConflict}
                  >
                    <Sparkles className="h-4 w-4 text-blue-500" /> AI 一键检测
                  </Button>
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <Textarea
                className="bg-blue-100 p-4 rounded-lg border min-h-24 border-blue-200 text-blue-900 transition-colors duration-300 focus:ring-2 focus:ring-blue-200 disabled:text-blue-900 disabled:opacity-100"
                value={props.generatedDescription}
                onChange={handleDescChange}
                disabled
              />
            </CardContent>
          </Card>
          {/* 检测结果展示 */}
          {props.conflictResult && props.conflictResult.length > 0 && (
            <div className="mt-2">
              {props.conflictResult.map((conf, idx) => (
                <div key={idx} className="bg-red-50 border border-red-200 text-red-700 rounded px-3 py-2 mb-2 text-sm">
                  <b>检测到冲突：</b>{conf.message}
                </div>
              ))}
            </div>
          )}
          <div className="flex gap-4 pt-4 border-t">
            <Button
              className="flex-1 bg-blue-400/80 text-white hover:bg-blue-400 rounded-full transition-colors duration-300 shadow-sm hover:shadow-lg"
              onClick={() => {
                console.log('点击下一步')
                props.nextStep()
                console.log('nextStep已调用')
              }}
            >
              下一步：语义化分析
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Step1TemplateSelect