# 问题命中管理 - 线索溯源追踪功能

## 功能概述

在问题命中管理功能中新增了**线索溯源流程图展示**功能，用户可以点击任一问题线索，在右侧弹出从规则命中到线索生成的完整流程图，详细展示主题规则中的具体逻辑步骤和问题命中点。

## 主要特性

### 1. 线索列表展示
- 每个问题下显示相关的线索列表
- 显示线索标题、描述、置信度等关键信息
- 展示线索关联的规则名称、主题名称、数据源等

### 2. 线索溯源流程图
点击任一线索后，弹出详细的溯源流程图，包含：

#### 问题概览
- 问题标题、严重程度、状态、创建时间等基本信息

#### 规则命中信息
- 规则名称、主题名称、数据源、命中时间
- 命中条件和结果

#### 逻辑步骤详情
- 详细展示规则中的每个逻辑步骤
- 显示步骤名称、类型、参数、结果
- 标识哪些步骤被命中

#### 线索生成流程
- 规则触发 → 逻辑评估 → 数据验证 → 线索生成
- 每个步骤的详细信息和时间戳

## 技术实现

### 类型定义 (`types/issue.ts`)
```typescript
export interface Issue {
  id: number
  title: string
  description: string
  status: 'active' | 'resolved' | 'pending'
  severity: 'low' | 'medium' | 'high' | 'critical'
  clues: IssueClue[]
}

export interface IssueClue {
  id: string
  title: string
  description: string
  ruleHit: RuleHit
  evidence: string[]
  confidence: number
}

export interface RuleHit {
  id: string
  ruleName: string
  themeName: string
  logicSteps: LogicStep[]
  // ... 其他字段
}
```

### 核心组件

#### 1. IssueHits 组件 (`app/issue-hits/issue-hits.tsx`)
- 展示问题信息和线索列表
- 处理线索点击事件
- 生成溯源流程数据

#### 2. TraceFlowModal 组件 (`components/trace-flow-modal.tsx`)
- 线索溯源流程图弹窗
- 分步骤展示完整的溯源流程
- 支持滚动查看详细信息

## 使用流程

1. **查看问题列表**
   - 进入问题命中管理页面
   - 查看所有问题及其状态

2. **查看线索**
   - 点击问题卡片展开线索列表
   - 查看每个线索的基本信息

3. **溯源追踪**
   - 点击任一线索
   - 右侧弹出溯源流程图
   - 查看从规则命中到线索生成的完整流程

4. **分析逻辑步骤**
   - 查看规则中的具体逻辑步骤
   - 了解哪些步骤被命中
   - 分析问题产生的根本原因

## 示例数据

### 问题示例
```typescript
{
  id: 1,
  title: "数据异常报警",
  description: "发现用户表存在数据缺失，请及时处理。",
  status: "active",
  severity: "high",
  clues: [
    {
      id: "clue-1-1",
      title: "用户ID缺失检测",
      description: "检测到用户表中存在ID字段为空的记录",
      confidence: 95,
      ruleHit: {
        ruleName: "用户数据完整性检查",
        themeName: "数据质量监控",
        logicSteps: [
          {
            stepName: "空值检查",
            logicType: "validation",
            isHit: true
          }
        ]
      }
    }
  ]
}
```

## 界面截图说明

### 主界面
- 问题卡片展示问题基本信息和线索数量
- 线索列表显示置信度、规则名称等关键信息

### 溯源流程图
- 弹窗形式展示，支持滚动
- 分区域展示问题概览、规则信息、逻辑步骤、生成流程
- 使用不同颜色和图标区分不同类型的步骤

## 扩展性

该功能设计具有良好的扩展性：

1. **数据源扩展**：可以轻松添加新的数据源类型
2. **规则类型扩展**：支持更多类型的规则和逻辑步骤
3. **可视化扩展**：可以添加图表、流程图等可视化元素
4. **交互扩展**：可以添加更多交互功能，如步骤详情展开、参数编辑等

## 后续优化方向

1. **性能优化**：对于大量数据的加载和渲染优化
2. **交互优化**：添加更多交互功能，如步骤高亮、参数修改等
3. **可视化增强**：添加流程图、时序图等可视化展示
4. **导出功能**：支持导出溯源报告
5. **历史追踪**：支持查看历史版本的溯源信息 