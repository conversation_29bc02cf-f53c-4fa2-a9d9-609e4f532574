"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";

interface SpotCheckItem {
  id: string;
  workOrderId: string;
  title: string;
  type: string;
  riskScore: number;
  status: "pending" | "completed";
  reviewResult?: "pass" | "fail";
  reviewedAt?: string;
  reviewer?: string;
}

interface Props {
  spotCheck: SpotCheckItem;
  onSubmit: (result: { reviewResult: "pass" | "fail"; comments: string }) => void;
}

export default function SpotCheckForm({ spotCheck, onSubmit }: Props) {
  const [reviewResult, setReviewResult] = useState<"pass" | "fail">("pass");
  const [comments, setComments] = useState("");

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit({ reviewResult, comments });
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <Card className="p-4">
        <h3 className="text-lg font-semibold mb-4">工单信息</h3>
        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label>工单编号</Label>
            <p className="mt-1">{spotCheck.workOrderId}</p>
          </div>
          <div>
            <Label>标题</Label>
            <p className="mt-1">{spotCheck.title}</p>
          </div>
          <div>
            <Label>类型</Label>
            <p className="mt-1">{spotCheck.type}</p>
          </div>
          <div>
            <Label>风险分数</Label>
            <p className="mt-1">
              <Badge variant={spotCheck.riskScore >= 90 ? "destructive" : "outline"}>
                {spotCheck.riskScore}
              </Badge>
            </p>
          </div>
        </div>
      </Card>

      <Card className="p-4">
        <h3 className="text-lg font-semibold mb-4">审核结果</h3>
        <div className="space-y-4">
          <div>
            <Label>审核结论</Label>
            <RadioGroup
              value={reviewResult}
              onValueChange={(value: "pass" | "fail") => setReviewResult(value)}
              className="mt-2"
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="pass" id="pass" />
                <Label htmlFor="pass">通过</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="fail" id="fail" />
                <Label htmlFor="fail">不通过</Label>
              </div>
            </RadioGroup>
          </div>

          <div>
            <Label>审核意见</Label>
            <Textarea
              value={comments}
              onChange={(e) => setComments(e.target.value)}
              placeholder="请输入审核意见..."
              className="mt-2"
              rows={4}
            />
          </div>
        </div>
      </Card>

      <div className="flex justify-end space-x-2">
        <Button type="submit" disabled={!comments}>
          提交审核
        </Button>
      </div>
    </form>
  );
} 