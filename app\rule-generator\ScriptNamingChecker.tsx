import React, { useState } from "react"
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { 
  CheckCircle, 
  AlertTriangle, 
  XCircle, 
  Wand2, 
  FileText,
  Code,
  Settings,
  Lightbulb,
  Star
} from "lucide-react"

// 命名规范检查结果接口
interface NamingCheckResult {
  score: number
  issues: NamingIssue[]
  suggestions: NamingSuggestion[]
  correctedScript?: string
}

// 命名问题接口
interface NamingIssue {
  type: 'error' | 'warning' | 'info'
  category: 'table' | 'column' | 'alias' | 'function' | 'comment'
  message: string
  line?: number
  currentValue?: string
  suggestedValue?: string
}

// 命名建议接口
interface NamingSuggestion {
  category: string
  description: string
  impact: 'high' | 'medium' | 'low'
  autoFixable: boolean
}

// 脚本命名规范检查组件
interface ScriptNamingCheckerProps {
  sqlScript: string
  scenario: string
  onScriptUpdate?: (newScript: string) => void
}

const ScriptNamingChecker: React.FC<ScriptNamingCheckerProps> = ({
  sqlScript,
  scenario,
  onScriptUpdate
}) => {
  const [checkResult, setCheckResult] = useState<NamingCheckResult | null>(null)
  const [isChecking, setIsChecking] = useState(false)
  const [showDetails, setShowDetails] = useState(false)

  // 自动检查：组件一渲染就执行
  React.useEffect(() => {
    if (!checkResult && !isChecking && sqlScript) {
      setIsChecking(true)
      setTimeout(() => {
        const result = checkNamingStandards(sqlScript)
        const correctedScript = autoFixScript(sqlScript, result.issues)
        result.correctedScript = correctedScript
        setCheckResult(result)
        setIsChecking(false)
      }, 1000)
    }
  }, [checkResult, isChecking, sqlScript])

  // 检查脚本命名规范
  const checkNamingStandards = (script: string): NamingCheckResult => {
    const issues: NamingIssue[] = []
    const suggestions: NamingSuggestion[] = []
    let score = 100

    const lines = script.split('\n')
    
    lines.forEach((line, lineIndex) => {
      const trimmedLine = line.trim()
      
      // 检查注释规范
      if (trimmedLine.startsWith('--')) {
        if (!/^--\s+.+$/.test(trimmedLine)) {
          issues.push({
            type: 'warning',
            category: 'comment',
            message: '注释格式不规范，建议使用 "-- 描述" 格式',
            line: lineIndex + 1,
            currentValue: trimmedLine,
            suggestedValue: `-- ${trimmedLine.replace(/^--\s*/, '')}`
          })
          score -= 5
        }
      }
      
      // 检查表名规范
      const tableMatches = trimmedLine.match(/FROM\s+([a-zA-Z_][a-zA-Z0-9_]*)/gi)
      if (tableMatches) {
        tableMatches.forEach(match => {
          const tableName = match.replace(/FROM\s+/i, '')
          if (!tableName.startsWith('dm_')) {
            issues.push({
              type: 'error',
              category: 'table',
              message: `表名应使用数据巡查前缀 "dm_"，当前表名: ${tableName}`,
              line: lineIndex + 1,
              currentValue: tableName,
              suggestedValue: `dm_${tableName}`
            })
            score -= 15
          }
        })
      }
      
      // 检查字段名规范
      const columnMatches = trimmedLine.match(/SELECT\s+(.+?)\s+FROM/gi)
      if (columnMatches) {
        columnMatches.forEach(match => {
          const columns = match.replace(/SELECT\s+|FROM/gi, '').split(',').map(c => c.trim())
          columns.forEach(column => {
            const columnName = column.split(' ')[0] // 去掉别名
            if (!/^[a-z_]+$/.test(columnName)) {
              issues.push({
                type: 'warning',
                category: 'column',
                message: `字段名应使用小写字母和下划线，当前字段: ${columnName}`,
                line: lineIndex + 1,
                currentValue: columnName,
                suggestedValue: columnName.toLowerCase().replace(/[^a-z0-9]/g, '_')
              })
              score -= 3
            }
          })
        })
      }
      
      // 检查别名规范
      const aliasMatches = trimmedLine.match(/\s+AS\s+([a-zA-Z_][a-zA-Z0-9_]*)/gi)
      if (aliasMatches) {
        aliasMatches.forEach(match => {
          const alias = match.replace(/\s+AS\s+/i, '')
          if (!/^[a-z_]+$/.test(alias)) {
            issues.push({
              type: 'warning',
              category: 'alias',
              message: `别名应使用小写字母和下划线，当前别名: ${alias}`,
              line: lineIndex + 1,
              currentValue: alias,
              suggestedValue: alias.toLowerCase().replace(/[^a-z0-9]/g, '_')
            })
            score -= 2
          }
        })
      }
      
      // 检查函数名规范
      const functionMatches = trimmedLine.match(/\b([A-Z_]+)\s*\(/g)
      if (functionMatches) {
        functionMatches.forEach(match => {
          const funcName = match.replace(/\s*\($/, '')
          if (!/^[A-Z_]+$/.test(funcName)) {
            issues.push({
              type: 'info',
              category: 'function',
              message: `函数名应使用大写字母和下划线，当前函数: ${funcName}`,
              line: lineIndex + 1,
              currentValue: funcName,
              suggestedValue: funcName.toUpperCase().replace(/[^A-Z0-9]/g, '_')
            })
            score -= 1
          }
        })
      }
    })

    // 检查是否缺少注释
    const hasComment = lines.some(line => line.trim().startsWith('--'))
    if (!hasComment) {
      issues.push({
        type: 'error',
        category: 'comment',
        message: '缺少脚本注释，建议添加描述性注释',
        line: 1,
        suggestedValue: `-- 数据巡查：${scenario}`
      })
      score -= 20
    }

    // 生成建议
    if (issues.length > 0) {
      suggestions.push({
        category: '命名规范',
        description: '建议遵循数据巡查脚本的命名规范，提高代码可读性和维护性',
        impact: 'high',
        autoFixable: true
      })
    }

    if (score < 60) {
      suggestions.push({
        category: '代码质量',
        description: '脚本命名规范得分较低，建议进行修正',
        impact: 'high',
        autoFixable: true
      })
    }

    return {
      score: Math.max(0, score),
      issues,
      suggestions
    }
  }

  // 自动修正脚本
  const autoFixScript = (script: string, issues: NamingIssue[]): string => {
    let correctedScript = script
    const lines = correctedScript.split('\n')
    
    // 按行号倒序处理，避免行号变化影响后续修正
    const sortedIssues = [...issues].sort((a, b) => (b.line || 0) - (a.line || 0))
    
    sortedIssues.forEach(issue => {
      if (issue.line && issue.currentValue && issue.suggestedValue) {
        const lineIndex = issue.line - 1
        if (lines[lineIndex]) {
          lines[lineIndex] = lines[lineIndex].replace(issue.currentValue, issue.suggestedValue)
        }
      }
    })
    
    // 如果没有注释，在开头添加
    if (!lines.some(line => line.trim().startsWith('--'))) {
      lines.unshift(`-- 数据巡查：${scenario}`)
    }
    
    return lines.join('\n')
  }

  // 应用修正
  const applyCorrection = () => {
    if (checkResult?.correctedScript && onScriptUpdate) {
      onScriptUpdate(checkResult.correctedScript)
    }
  }

  // 获取评分颜色
  const getScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-600'
    if (score >= 70) return 'text-yellow-600'
    return 'text-red-600'
  }

  // 获取评分等级
  const getScoreLevel = (score: number) => {
    if (score >= 90) return { level: '优秀', icon: <Star className="h-4 w-4" />, color: 'text-green-600' }
    if (score >= 70) return { level: '良好', icon: <CheckCircle className="h-4 w-4" />, color: 'text-yellow-600' }
    if (score >= 50) return { level: '一般', icon: <AlertTriangle className="h-4 w-4" />, color: 'text-orange-600' }
    return { level: '较差', icon: <XCircle className="h-4 w-4" />, color: 'text-red-600' }
  }

  if (!sqlScript) {
    return (
      <Card>
        <CardContent className="text-center text-orange-600 py-8">
          请先生成脚本，再进行命名规范检查
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="bg-gradient-to-br from-orange-50 to-orange-100 border-orange-100 rounded-xl transition-colors duration-300 shadow-sm hover:shadow-lg">
      <CardHeader className="pb-3">
        <CardTitle className="text-orange-500 text-sm flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Code className="h-4 w-4" />
            脚本命名规范检查
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {isChecking && (
          <div className="text-center py-8">
            <FileText className="h-12 w-12 text-orange-300 mx-auto mb-4 animate-pulse" />
            <p className="text-orange-600 text-sm">正在检查脚本命名规范，请稍候…</p>
          </div>
        )}
        {!isChecking && checkResult && (
          <>
            {/* 评分展示 */}
            <div className="bg-white rounded-lg p-4 border border-orange-200">
              <div className="flex items-center justify-between mb-3">
                <h4 className="font-medium text-gray-900">规范性评分</h4>
                <Badge variant="outline" className="border-orange-200 text-orange-700">
                  {checkResult.score}/100
                </Badge>
              </div>
              <div className="flex items-center gap-3 mb-3">
                <div className={`text-2xl font-bold ${getScoreColor(checkResult.score)}`}>
                  {checkResult.score}
                </div>
                <div className="flex items-center gap-2">
                  {getScoreLevel(checkResult.score).icon}
                  <span className={`font-medium ${getScoreLevel(checkResult.score).color}`}>
                    {getScoreLevel(checkResult.score).level}
                  </span>
                </div>
              </div>
              <Progress value={checkResult.score} className="h-2" />
            </div>

            {/* 问题统计 */}
            {checkResult.issues.length > 0 && (
              <div className="bg-white rounded-lg p-4 border border-orange-200">
                <h4 className="font-medium text-gray-900 mb-3">发现的问题</h4>
                <div className="space-y-2">
                  {checkResult.issues.slice(0, showDetails ? undefined : 3).map((issue, index) => (
                    <div key={index} className="flex items-start gap-3 p-2 bg-gray-50 rounded">
                      <div className="flex-shrink-0 mt-1">
                        {issue.type === 'error' && <XCircle className="h-4 w-4 text-red-500" />}
                        {issue.type === 'warning' && <AlertTriangle className="h-4 w-4 text-yellow-500" />}
                        {issue.type === 'info' && <Lightbulb className="h-4 w-4 text-blue-500" />}
                      </div>
                      <div className="flex-1">
                        <div className="text-sm font-medium text-gray-900">{issue.message}</div>
                        {issue.line && (
                          <div className="text-xs text-gray-500 mt-1">
                            第 {issue.line} 行
                            {issue.currentValue && (
                              <span className="ml-2">
                                当前: <code className="bg-red-100 text-red-700 px-1 rounded">{issue.currentValue}</code>
                              </span>
                            )}
                            {issue.suggestedValue && (
                              <span className="ml-2">
                                建议: <code className="bg-green-100 text-green-700 px-1 rounded">{issue.suggestedValue}</code>
                              </span>
                            )}
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                  {checkResult.issues.length > 3 && !showDetails && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setShowDetails(true)}
                      className="text-orange-600 hover:text-orange-700"
                    >
                      显示全部 {checkResult.issues.length} 个问题
                    </Button>
                  )}
                </div>
              </div>
            )}

            {/* 修正建议 */}
            {checkResult.suggestions.length > 0 && (
              <div className="bg-white rounded-lg p-4 border border-orange-200">
                <h4 className="font-medium text-gray-900 mb-3">修正建议</h4>
                <div className="space-y-2">
                  {checkResult.suggestions.map((suggestion, index) => (
                    <div key={index} className="flex items-start gap-3 p-2 bg-blue-50 rounded">
                      <Wand2 className="h-4 w-4 text-blue-500 mt-1 flex-shrink-0" />
                      <div className="flex-1">
                        <div className="text-sm font-medium text-gray-900">{suggestion.description}</div>
                        <div className="text-xs text-gray-500 mt-1">
                          影响程度: 
                          <Badge 
                            variant="outline" 
                            className={`ml-1 ${
                              suggestion.impact === 'high' ? 'border-red-200 text-red-700' :
                              suggestion.impact === 'medium' ? 'border-yellow-200 text-yellow-700' :
                              'border-green-200 text-green-700'
                            }`}
                          >
                            {suggestion.impact === 'high' ? '高' : suggestion.impact === 'medium' ? '中' : '低'}
                          </Badge>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* 一键修正按钮 */}
            {checkResult.correctedScript && checkResult.score < 100 && (
              <Alert className="border-orange-200 bg-orange-50">
                <Wand2 className="h-4 w-4 text-orange-600" />
                <AlertDescription className="text-orange-800">
                  <div className="flex items-center justify-between">
                    <span>发现 {checkResult.issues.length} 个命名规范问题，可以一键修正</span>
                    <Button
                      size="sm"
                      onClick={applyCorrection}
                      className="bg-orange-600 text-white hover:bg-orange-700"
                    >
                      <Wand2 className="h-3 w-3 mr-1" />
                      一键修正
                    </Button>
                  </div>
                </AlertDescription>
              </Alert>
            )}

            {/* 命名规范说明 */}
            <div className="bg-white rounded-lg p-4 border border-orange-200">
              <h4 className="font-medium text-gray-900 mb-3 flex items-center gap-2">
                <Settings className="h-4 w-4" />
                数据巡查脚本命名规范
              </h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                  <h5 className="font-medium text-gray-700 mb-2">表名规范</h5>
                  <ul className="space-y-1 text-gray-600">
                    <li>• 使用 <code className="bg-gray-100 px-1 rounded">dm_</code> 前缀</li>
                    <li>• 示例: <code className="bg-gray-100 px-1 rounded">dm_customer_audit</code></li>
                  </ul>
                </div>
                <div>
                  <h5 className="font-medium text-gray-700 mb-2">字段名规范</h5>
                  <ul className="space-y-1 text-gray-600">
                    <li>• 使用小写字母和下划线</li>
                    <li>• 示例: <code className="bg-gray-100 px-1 rounded">customer_id</code></li>
                  </ul>
                </div>
                <div>
                  <h5 className="font-medium text-gray-700 mb-2">函数名规范</h5>
                  <ul className="space-y-1 text-gray-600">
                    <li>• 使用大写字母和下划线</li>
                    <li>• 示例: <code className="bg-gray-100 px-1 rounded">COUNT</code></li>
                  </ul>
                </div>
                <div>
                  <h5 className="font-medium text-gray-700 mb-2">注释规范</h5>
                  <ul className="space-y-1 text-gray-600">
                    <li>• 使用 <code className="bg-gray-100 px-1 rounded">-- 描述</code> 格式</li>
                    <li>• 示例: <code className="bg-gray-100 px-1 rounded">-- 数据巡查：充电桩电量异常</code></li>
                  </ul>
                </div>
              </div>
            </div>
          </>
        )}
      </CardContent>
    </Card>
  )
}

export default ScriptNamingChecker 