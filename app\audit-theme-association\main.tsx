"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Checkbox } from "@/components/ui/checkbox"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Progress } from "@/components/ui/progress"
import { Separator } from "@/components/ui/separator"
import { 
  Search, 
  Plus, 
  RefreshCw, 
  Link, 
  Unlink, 
  AlertTriangle, 
  CheckCircle, 
  Clock,
  FileText,
  Target,
  Settings,
  ArrowRight,
  Filter
} from "lucide-react"

// 稽查主题接口
interface AuditTheme {
  id: string
  name: string
  description: string
  category: string
  priority: 'high' | 'medium' | 'low'
  status: 'active' | 'inactive' | 'draft'
  createdAt: string
  updatedAt: string
  associatedElementsCount: number
}

// 稽查要素接口
interface AuditElement {
  id: string
  name: string
  type: 'risk_point' | 'policy_basis' | 'control_measure' | 'compliance_requirement'
  description: string
  source: string
  status: 'active' | 'inactive' | 'pending'
  associatedThemesCount: number
  lastUpdated: string
}

// 关联关系接口
interface Association {
  id: string
  themeId: string
  elementId: string
  themeName: string
  elementName: string
  elementType: string
  associationDate: string
  strength: 'strong' | 'medium' | 'weak'
  notes?: string
}

// 更新建议接口
interface UpdateSuggestion {
  type: 'new_element' | 'updated_element' | 'outdated_element' | 'removed_element'
  element: AuditElement
  reason: string
  confidence: number
  action: 'add' | 'update' | 'remove' | 'review'
}

export function AuditThemeAssociationMain() {
  const [themes, setThemes] = useState<AuditTheme[]>([])
  const [elements, setElements] = useState<AuditElement[]>([])
  const [associations, setAssociations] = useState<Association[]>([])
  const [selectedTheme, setSelectedTheme] = useState<AuditTheme | null>(null)
  const [selectedElements, setSelectedElements] = useState<string[]>([])
  const [searchTerm, setSearchTerm] = useState("")
  const [filterType, setFilterType] = useState<string>("all")
  const [isUpdateDialogOpen, setIsUpdateDialogOpen] = useState(false)
  const [isAssociationDialogOpen, setIsAssociationDialogOpen] = useState(false)
  const [updateSuggestions, setUpdateSuggestions] = useState<UpdateSuggestion[]>([])
  const [isUpdating, setIsUpdating] = useState(false)
  const [updateProgress, setUpdateProgress] = useState(0)

  // 模拟数据
  useEffect(() => {
    // 模拟稽查主题数据
    const mockThemes: AuditTheme[] = [
      {
        id: "1",
        name: "电价政策合规性稽查",
        description: "检查电价政策执行情况，确保符合国家规定",
        category: "价格管理",
        priority: "high",
        status: "active",
        createdAt: "2024-01-15",
        updatedAt: "2024-01-20",
        associatedElementsCount: 8
      },
      {
        id: "2",
        name: "用电量异常稽查",
        description: "识别用电量异常波动，发现潜在违规行为",
        category: "用电管理",
        priority: "medium",
        status: "active",
        createdAt: "2024-01-10",
        updatedAt: "2024-01-18",
        associatedElementsCount: 12
      },
      {
        id: "3",
        name: "客户信息真实性稽查",
        description: "验证客户信息的真实性和完整性",
        category: "客户管理",
        priority: "high",
        status: "active",
        createdAt: "2024-01-12",
        updatedAt: "2024-01-19",
        associatedElementsCount: 6
      }
    ]

    // 模拟稽查要素数据
    const mockElements: AuditElement[] = [
      {
        id: "1",
        name: "电价政策文件",
        type: "policy_basis",
        description: "国家发改委发布的电价政策文件",
        source: "发改委官网",
        status: "active",
        associatedThemesCount: 3,
        lastUpdated: "2024-01-20"
      },
      {
        id: "2",
        name: "用电量异常阈值",
        type: "risk_point",
        description: "用电量异常判断的阈值标准",
        source: "历史数据分析",
        status: "active",
        associatedThemesCount: 2,
        lastUpdated: "2024-01-18"
      },
      {
        id: "3",
        name: "客户身份验证要求",
        type: "compliance_requirement",
        description: "客户身份信息验证的合规要求",
        source: "监管规定",
        status: "active",
        associatedThemesCount: 4,
        lastUpdated: "2024-01-19"
      }
    ]

    // 模拟关联关系数据
    const mockAssociations: Association[] = [
      {
        id: "1",
        themeId: "1",
        elementId: "1",
        themeName: "电价政策合规性稽查",
        elementName: "电价政策文件",
        elementType: "policy_basis",
        associationDate: "2024-01-15",
        strength: "strong"
      },
      {
        id: "2",
        themeId: "2",
        elementId: "2",
        themeName: "用电量异常稽查",
        elementName: "用电量异常阈值",
        elementType: "risk_point",
        associationDate: "2024-01-10",
        strength: "strong"
      }
    ]

    setThemes(mockThemes)
    setElements(mockElements)
    setAssociations(mockAssociations)
  }, [])

  // 获取主题的关联要素
  const getThemeElements = (themeId: string) => {
    return associations
      .filter(assoc => assoc.themeId === themeId)
      .map(assoc => {
        const element = elements.find(el => el.id === assoc.elementId)
        return { ...assoc, element }
      })
      .filter(item => item.element)
  }

  // 获取要素的关联主题
  const getElementThemes = (elementId: string) => {
    return associations
      .filter(assoc => assoc.elementId === elementId)
      .map(assoc => {
        const theme = themes.find(th => th.id === assoc.themeId)
        return { ...assoc, theme }
      })
      .filter(item => item.theme)
  }

  // 添加要素关联
  const addElementAssociation = (themeId: string, elementIds: string[]) => {
    const newAssociations = elementIds.map(elementId => {
      const theme = themes.find(t => t.id === themeId)
      const element = elements.find(e => e.id === elementId)
      return {
        id: `${themeId}-${elementId}-${Date.now()}`,
        themeId,
        elementId,
        themeName: theme?.name || "",
        elementName: element?.name || "",
        elementType: element?.type || "risk_point",
        associationDate: new Date().toISOString().split('T')[0],
        strength: "medium" as const
      }
    })
    setAssociations([...associations, ...newAssociations])
    setIsAssociationDialogOpen(false)
    setSelectedElements([])
  }

  // 移除要素关联
  const removeElementAssociation = (associationId: string) => {
    setAssociations(associations.filter(assoc => assoc.id !== associationId))
  }

  // 更新关联要素
  const updateAssociatedElements = async (themeId: string) => {
    setIsUpdating(true)
    setUpdateProgress(0)

    // 模拟更新过程
    for (let i = 0; i <= 100; i += 20) {
      await new Promise(resolve => setTimeout(resolve, 500))
      setUpdateProgress(i)
    }

    // 模拟更新建议
    const suggestions: UpdateSuggestion[] = [
      {
        type: "new_element",
        element: {
          id: "4",
          name: "最新电价调整政策",
          type: "policy_basis",
          description: "2024年最新电价调整政策文件",
          source: "发改委官网",
          status: "active",
          associatedThemesCount: 1,
          lastUpdated: "2024-01-21"
        },
        reason: "发现新的相关政策文件",
        confidence: 0.95,
        action: "add"
      },
      {
        type: "updated_element",
        element: {
          id: "1",
          name: "电价政策文件",
          type: "policy_basis",
          description: "国家发改委发布的电价政策文件（已更新）",
          source: "发改委官网",
          status: "active",
          associatedThemesCount: 3,
          lastUpdated: "2024-01-21"
        },
        reason: "政策文件内容已更新",
        confidence: 0.88,
        action: "update"
      }
    ]

    setUpdateSuggestions(suggestions)
    setIsUpdating(false)
    setIsUpdateDialogOpen(true)
  }

  // 应用更新建议
  const applyUpdateSuggestions = (suggestions: UpdateSuggestion[]) => {
    suggestions.forEach(suggestion => {
      if (suggestion.action === "add") {
        // 添加新要素到知识库
        setElements(prev => [...prev, suggestion.element])
        // 创建关联关系
        if (selectedTheme) {
          addElementAssociation(selectedTheme.id, [suggestion.element.id])
        }
      } else if (suggestion.action === "update") {
        // 更新现有要素
        setElements(prev => prev.map(el => 
          el.id === suggestion.element.id ? suggestion.element : el
        ))
      }
    })
    setIsUpdateDialogOpen(false)
    setUpdateSuggestions([])
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-red-100 text-red-800'
      case 'medium': return 'bg-yellow-100 text-yellow-800'
      case 'low': return 'bg-green-100 text-green-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getStrengthColor = (strength: string) => {
    switch (strength) {
      case 'strong': return 'bg-green-100 text-green-800'
      case 'medium': return 'bg-yellow-100 text-yellow-800'
      case 'weak': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getElementTypeIcon = (type: string) => {
    switch (type) {
      case 'risk_point': return <AlertTriangle className="w-4 h-4" />
      case 'policy_basis': return <FileText className="w-4 h-4" />
      case 'control_measure': return <Target className="w-4 h-4" />
      case 'compliance_requirement': return <Settings className="w-4 h-4" />
      default: return <FileText className="w-4 h-4" />
    }
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">稽查主题要素关联</h1>
          <p className="text-gray-600 mt-2">
            管理稽查主题与相关稽查要素的关联关系，支持一键更新关联要素
          </p>
        </div>
        <Button onClick={() => setIsAssociationDialogOpen(true)}>
          <Plus className="w-4 h-4 mr-2" />
          新建关联
        </Button>
      </div>

      <Tabs defaultValue="themes" className="space-y-6">
        <TabsList>
          <TabsTrigger value="themes">稽查主题视图</TabsTrigger>
          <TabsTrigger value="elements">稽查要素视图</TabsTrigger>
          <TabsTrigger value="associations">关联关系管理</TabsTrigger>
        </TabsList>

        <TabsContent value="themes" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>稽查主题列表</CardTitle>
              <CardDescription>
                查看所有稽查主题及其关联的稽查要素
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4">
                {themes.map((theme) => (
                  <Card key={theme.id} className="cursor-pointer hover:shadow-md transition-shadow"
                        onClick={() => setSelectedTheme(theme)}>
                    <CardContent className="p-4">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                            <h3 className="font-semibold">{theme.name}</h3>
                            <Badge className={getPriorityColor(theme.priority)}>
                              {theme.priority === 'high' ? '高优先级' : 
                               theme.priority === 'medium' ? '中优先级' : '低优先级'}
                            </Badge>
                            <Badge variant={theme.status === 'active' ? 'default' : 'secondary'}>
                              {theme.status === 'active' ? '活跃' : 
                               theme.status === 'inactive' ? '非活跃' : '草稿'}
                            </Badge>
                          </div>
                          <p className="text-sm text-gray-600 mb-2">{theme.description}</p>
                          <div className="flex items-center gap-4 text-sm text-gray-500">
                            <span>分类: {theme.category}</span>
                            <span>关联要素: {theme.associatedElementsCount} 个</span>
                            <span>更新时间: {theme.updatedAt}</span>
                          </div>
                        </div>
                        <div className="flex gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation()
                              setSelectedTheme(theme)
                              setIsAssociationDialogOpen(true)
                            }}
                          >
                            <Link className="w-4 h-4 mr-1" />
                            关联要素
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation()
                              setSelectedTheme(theme)
                              updateAssociatedElements(theme.id)
                            }}
                          >
                            <RefreshCw className="w-4 h-4 mr-1" />
                            更新要素
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>

          {selectedTheme && (
            <Card>
              <CardHeader>
                <CardTitle>关联要素详情 - {selectedTheme.name}</CardTitle>
                <CardDescription>
                  该主题关联的所有稽查要素
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>要素名称</TableHead>
                      <TableHead>类型</TableHead>
                      <TableHead>关联强度</TableHead>
                      <TableHead>关联日期</TableHead>
                      <TableHead>操作</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {getThemeElements(selectedTheme.id).map((association) => (
                      <TableRow key={association.id}>
                        <TableCell className="font-medium">
                          {association.elementName}
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            {getElementTypeIcon(association.elementType)}
                            <span className="capitalize">
                              {association.elementType === 'risk_point' ? '风险点' :
                               association.elementType === 'policy_basis' ? '政策依据' :
                               association.elementType === 'control_measure' ? '控制措施' : '合规要求'}
                            </span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge className={getStrengthColor(association.strength)}>
                            {association.strength === 'strong' ? '强关联' :
                             association.strength === 'medium' ? '中关联' : '弱关联'}
                          </Badge>
                        </TableCell>
                        <TableCell>{association.associationDate}</TableCell>
                        <TableCell>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => removeElementAssociation(association.id)}
                          >
                            <Unlink className="w-4 h-4" />
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="elements" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>稽查要素列表</CardTitle>
              <CardDescription>
                查看所有稽查要素及其关联的稽查主题
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex gap-4 mb-4">
                <div className="flex-1">
                  <Input
                    placeholder="搜索要素..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="max-w-sm"
                  />
                </div>
                <Select value={filterType} onValueChange={setFilterType}>
                  <SelectTrigger className="w-48">
                    <SelectValue placeholder="选择类型" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">所有类型</SelectItem>
                    <SelectItem value="risk_point">风险点</SelectItem>
                    <SelectItem value="policy_basis">政策依据</SelectItem>
                    <SelectItem value="control_measure">控制措施</SelectItem>
                    <SelectItem value="compliance_requirement">合规要求</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>要素名称</TableHead>
                    <TableHead>类型</TableHead>
                    <TableHead>来源</TableHead>
                    <TableHead>关联主题数</TableHead>
                    <TableHead>最后更新</TableHead>
                    <TableHead>状态</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {elements
                    .filter(element => 
                      element.name.toLowerCase().includes(searchTerm.toLowerCase()) &&
                      (filterType === "all" || element.type === filterType)
                    )
                    .map((element) => (
                      <TableRow key={element.id}>
                        <TableCell className="font-medium">{element.name}</TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            {getElementTypeIcon(element.type)}
                            <span className="capitalize">
                              {element.type === 'risk_point' ? '风险点' :
                               element.type === 'policy_basis' ? '政策依据' :
                               element.type === 'control_measure' ? '控制措施' : '合规要求'}
                            </span>
                          </div>
                        </TableCell>
                        <TableCell>{element.source}</TableCell>
                        <TableCell>{element.associatedThemesCount}</TableCell>
                        <TableCell>{element.lastUpdated}</TableCell>
                        <TableCell>
                          <Badge variant={element.status === 'active' ? 'default' : 'secondary'}>
                            {element.status === 'active' ? '活跃' : 
                             element.status === 'inactive' ? '非活跃' : '待审核'}
                          </Badge>
                        </TableCell>
                      </TableRow>
                    ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="associations" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>关联关系管理</CardTitle>
              <CardDescription>
                查看和管理所有稽查主题与要素的关联关系
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>稽查主题</TableHead>
                    <TableHead>稽查要素</TableHead>
                    <TableHead>要素类型</TableHead>
                    <TableHead>关联强度</TableHead>
                    <TableHead>关联日期</TableHead>
                    <TableHead>操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {associations.map((association) => (
                    <TableRow key={association.id}>
                      <TableCell className="font-medium">{association.themeName}</TableCell>
                      <TableCell>{association.elementName}</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          {getElementTypeIcon(association.elementType)}
                          <span className="capitalize">
                            {association.elementType === 'risk_point' ? '风险点' :
                             association.elementType === 'policy_basis' ? '政策依据' :
                             association.elementType === 'control_measure' ? '控制措施' : '合规要求'}
                          </span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge className={getStrengthColor(association.strength)}>
                          {association.strength === 'strong' ? '强关联' :
                           association.strength === 'medium' ? '中关联' : '弱关联'}
                        </Badge>
                      </TableCell>
                      <TableCell>{association.associationDate}</TableCell>
                      <TableCell>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => removeElementAssociation(association.id)}
                        >
                          <Unlink className="w-4 h-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* 关联要素对话框 */}
      <Dialog open={isAssociationDialogOpen} onOpenChange={setIsAssociationDialogOpen}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>关联稽查要素</DialogTitle>
            <DialogDescription>
              为稽查主题选择要关联的稽查要素
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label>选择稽查主题</Label>
                <Select onValueChange={(value) => {
                  const theme = themes.find(t => t.id === value)
                  setSelectedTheme(theme || null)
                }}>
                  <SelectTrigger>
                    <SelectValue placeholder="选择稽查主题" />
                  </SelectTrigger>
                  <SelectContent>
                    {themes.map((theme) => (
                      <SelectItem key={theme.id} value={theme.id}>
                        {theme.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label>要素类型筛选</Label>
                <Select onValueChange={setFilterType}>
                  <SelectTrigger>
                    <SelectValue placeholder="选择要素类型" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">所有类型</SelectItem>
                    <SelectItem value="risk_point">风险点</SelectItem>
                    <SelectItem value="policy_basis">政策依据</SelectItem>
                    <SelectItem value="control_measure">控制措施</SelectItem>
                    <SelectItem value="compliance_requirement">合规要求</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div>
              <Label>选择稽查要素</Label>
              <div className="border rounded-md p-4 max-h-60 overflow-y-auto">
                {elements
                  .filter(element => 
                    (filterType === "all" || element.type === filterType) &&
                    !associations.some(assoc => 
                      assoc.themeId === selectedTheme?.id && assoc.elementId === element.id
                    )
                  )
                  .map((element) => (
                    <div key={element.id} className="flex items-center space-x-2 py-2">
                      <Checkbox
                        id={element.id}
                        checked={selectedElements.includes(element.id)}
                        onCheckedChange={(checked) => {
                          if (checked) {
                            setSelectedElements([...selectedElements, element.id])
                          } else {
                            setSelectedElements(selectedElements.filter(id => id !== element.id))
                          }
                        }}
                      />
                      <Label htmlFor={element.id} className="flex-1 cursor-pointer">
                        <div className="flex items-center gap-2">
                          {getElementTypeIcon(element.type)}
                          <span className="font-medium">{element.name}</span>
                          <Badge variant="outline" className="text-xs">
                            {element.type === 'risk_point' ? '风险点' :
                             element.type === 'policy_basis' ? '政策依据' :
                             element.type === 'control_measure' ? '控制措施' : '合规要求'}
                          </Badge>
                        </div>
                        <p className="text-sm text-gray-600 mt-1">{element.description}</p>
                      </Label>
                    </div>
                  ))}
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAssociationDialogOpen(false)}>
              取消
            </Button>
            <Button 
              onClick={() => {
                if (selectedTheme && selectedElements.length > 0) {
                  addElementAssociation(selectedTheme.id, selectedElements)
                }
              }}
              disabled={!selectedTheme || selectedElements.length === 0}
            >
              确认关联
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 更新要素对话框 */}
      <Dialog open={isUpdateDialogOpen} onOpenChange={setIsUpdateDialogOpen}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>更新关联要素</DialogTitle>
            <DialogDescription>
              基于最新政策预测结果，建议的要素更新
            </DialogDescription>
          </DialogHeader>
          
          {isUpdating ? (
            <div className="space-y-4">
              <div className="text-center">
                <RefreshCw className="w-8 h-8 animate-spin mx-auto mb-2" />
                <p>正在分析政策文件并提取稽查要素...</p>
              </div>
              <Progress value={updateProgress} className="w-full" />
            </div>
          ) : (
            <div className="space-y-4">
              {updateSuggestions.map((suggestion, index) => (
                <Card key={index}>
                  <CardContent className="p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          {suggestion.type === 'new_element' && <Plus className="w-4 h-4 text-green-600" />}
                          {suggestion.type === 'updated_element' && <RefreshCw className="w-4 h-4 text-blue-600" />}
                          {suggestion.type === 'outdated_element' && <AlertTriangle className="w-4 h-4 text-yellow-600" />}
                          {suggestion.type === 'removed_element' && <Unlink className="w-4 h-4 text-red-600" />}
                          
                          <span className="font-medium">
                            {suggestion.type === 'new_element' ? '新增要素' :
                             suggestion.type === 'updated_element' ? '更新要素' :
                             suggestion.type === 'outdated_element' ? '过时要素' : '移除要素'}
                          </span>
                          
                          <Badge variant="outline">
                            置信度: {Math.round(suggestion.confidence * 100)}%
                          </Badge>
                        </div>
                        
                        <h4 className="font-semibold mb-1">{suggestion.element.name}</h4>
                        <p className="text-sm text-gray-600 mb-2">{suggestion.element.description}</p>
                        <p className="text-sm text-gray-500">{suggestion.reason}</p>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        <Badge className={getElementTypeIcon(suggestion.element.type) ? 'bg-blue-100 text-blue-800' : ''}>
                          {suggestion.element.type === 'risk_point' ? '风险点' :
                           suggestion.element.type === 'policy_basis' ? '政策依据' :
                           suggestion.element.type === 'control_measure' ? '控制措施' : '合规要求'}
                        </Badge>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsUpdateDialogOpen(false)}>
              取消
            </Button>
            <Button 
              onClick={() => applyUpdateSuggestions(updateSuggestions)}
              disabled={isUpdating || updateSuggestions.length === 0}
            >
              应用更新
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
} 