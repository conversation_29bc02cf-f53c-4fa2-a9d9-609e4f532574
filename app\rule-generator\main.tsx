"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rig<PERSON> } from "@/components/ui/tabs"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Separator } from "@/components/ui/separator"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import {
  Wand2,
  Palette,
  Code,
  Save,
  ReplaceIcon as Update,
  AlertTriangle,
  Shield,
  BookOpen,
  Plus,
  Trash2,
  Co<PERSON>,
  RefreshCw,
  FileText,
  X,
  CheckCircle,
  Circle,
  Layout,
  Target,
  Users,
  Database,
  MessageSquare,
  TrendingUp,
} from "lucide-react"
import React from "react"
import Step0IntentGuide from './Step0IntentGuide'
import Step1TemplateSelect from './Step1TemplateSelect'
import Step2SemanticAnalyze from './Step2SemanticAnalyze'
import Step3SQLConfirm from './Step3SQLConfirm'
import ThemeEvolutionAnalysis from './ThemeEvolutionAnalysis'
import ThemeEvolutionDetail from './ThemeEvolutionDetail'

// 基础条件类型定义
type BaseCondition = {
  label: string;
  type: 'basic' | 'derived';
  field: string;
  operator?: string;
  value?: string;
  unit?: string;
};

// 规则模板定义
interface RuleTemplate {
  id: string
  name: string
  description: string
  category: string
  structure: {
    scenario: string
    targetUsers: string[]
    baseConditions: string[]
    branchConditions: {
      condition: string
      threshold: string
      unit: string
    }[]
  }
  objectTypes: string[]
  professionalCategories: string[]
  riskPoints: string[]
}

// 预定义规则模板
const ruleTemplates: RuleTemplate[] = [
  {
    id: "ev_charging_anomaly",
    name: "电动汽车充电桩电量异常",
    description: "针对充电桩用户的电量异常监控",
    category: "用电异常",
    structure: {
      scenario: "电动汽车充电桩电量异常",
      targetUsers: ["营销业务用户"],
      baseConditions: ["用户名称包含'充电'字段", "用电地址包含'充电'字段", "电价执行居民合表电价"],
      branchConditions: [
        {
          condition: "抄表周期为'单月'",
          threshold: "5000",
          unit: "千瓦时",
        },
        {
          condition: "抄表周期为'双月'",
          threshold: "10000",
          unit: "千瓦时",
        },
      ],
    },
    objectTypes: ["用电户", "电能表计"],
    professionalCategories: ["计量采集"],
    riskPoints: ["充电桩电量异常"],
  },
  {
    id: "high_consumption_commercial",
    name: "工商业高耗能异常",
    description: "工商业用户异常高耗能监控",
    category: "用电异常",
    structure: {
      scenario: "工商业高耗能异常",
      targetUsers: ["工商业用户"],
      baseConditions: ["客户类型为'工商业'", "电压等级为'10kV'或'35kV'"],
      branchConditions: [
        {
          condition: "行业类型为'制造业'",
          threshold: "50000",
          unit: "千瓦时",
        },
        {
          condition: "行业类型为'服务业'",
          threshold: "20000",
          unit: "千瓦时",
        },
      ],
    },
    objectTypes: ["用电户"],
    professionalCategories: ["客户服务"],
    riskPoints: ["工商业高耗能异常"],
  },
  {
    id: "power_factor_anomaly",
    name: "功率因数不达标",
    description: "功率因数低于标准值的监控",
    category: "电能质量",
    structure: {
      scenario: "功率因数不达标",
      targetUsers: ["工商业用户", "大工业用户"],
      baseConditions: ["电压等级为'10kV'以上", "用电性质为'生产用电'"],
      branchConditions: [
        {
          condition: "装机容量≥100kW",
          threshold: "0.9",
          unit: "功率因数",
        },
        {
          condition: "装机容量<100kW",
          threshold: "0.85",
          unit: "功率因数",
        },
      ],
    },
    objectTypes: ["用电户"],
    professionalCategories: ["计量采集"],
    riskPoints: ["功率因数异常"],
  },
]

// 新增筛选选项
const objectTypeOptions = ["用电户", "发电户", "台区", "电能表计"]
const professionalCategoryOptions = ["计量采集", "客户服务"]
const riskPointOptions = ["充电桩电量异常", "工商业高耗能异常", "功率因数异常", "光伏客户档案异常", "居民电量异常"]

// 可选字段定义
const fieldOptions = {
  scenarios: [
    "电动汽车充电桩电量异常",
    "工商业高耗能异常",
    "功率因数不达标",
    "线损异常",
    "电费计算异常",
    "分布式光伏计量异常",
  ],
  targetUsers: ["营销业务用户", "工商业用户", "居民用户", "大工业用户", "临时用电用户"],
  baseConditions: [
    // 派生/复合原子
    { label: "用户名称包含'充电'", type: "derived" as const, field: "用户名称", operator: "包含", value: "充电", unit: "" },
    { label: "用电地址包含'充电'", type: "derived" as const, field: "用电地址", operator: "包含", value: "充电", unit: "" },
    { label: "电价执行居民合表电价", type: "derived" as const, field: "电价", operator: "等于", value: "居民合表电价", unit: "" },
    { label: "客户类型为'工商业'", type: "derived" as const, field: "客户类型", operator: "等于", value: "工商业", unit: "" },
    { label: "电压等级为'10kV'或'35kV'", type: "derived" as const, field: "电压等级", operator: "等于", value: "10kV/35kV", unit: "" },
    { label: "用电性质为'生产用电'", type: "derived" as const, field: "用电性质", operator: "等于", value: "生产用电", unit: "" },
    { label: "装机容量≥100kW", type: "derived" as const, field: "装机容量", operator: "大于", value: "100", unit: "kW" },
    { label: "装机容量<100kW", type: "derived" as const, field: "装机容量", operator: "小于", value: "100", unit: "kW" },
    // 基础原子
    { label: "用户名称", type: "basic" as const, field: "用户名称" },
    { label: "用电地址", type: "basic" as const, field: "用电地址" },
    { label: "电价", type: "basic" as const, field: "电价" },
    { label: "客户类型", type: "basic" as const, field: "客户类型" },
    { label: "电压等级", type: "basic" as const, field: "电压等级" },
    { label: "用电性质", type: "basic" as const, field: "用电性质" },
    { label: "装机容量", type: "basic" as const, field: "装机容量" },
  ],
  operators: ["包含", "等于", "大于", "小于", "介于"],
  units: ["千瓦时", "千瓦", "功率因数", "百分比", "元"],
}

// 递归组件完整声明提前
function ConditionGroup({
  group,
  onChange,
  onDelete,
  fieldOptions,
  path = []
}: any) {
  // 修改当前组的某个子项
  const updateChild = (idx: number, newItem: any) => {
    const newConds = (group.conditions ?? []).map((c: any, i: number) => (i === idx ? newItem : c))
    onChange({ ...group, conditions: newConds }, path)
  }
  // 新增：直接替换某个条件对象
  const updateConditionObject = (idx: number, newObj: any) => {
    const newConds = (group.conditions ?? []).map((c: any, i: number) => (i === idx ? newObj : c))
    onChange({ ...group, conditions: newConds }, path)
  }
  // 删除子项
  const removeChild = (idx: number) => {
    const newConds = (group.conditions ?? []).filter((_: any, i: number) => i !== idx)
    onChange({ ...group, conditions: newConds }, path)
  }
  // 添加普通条件
  const addCondition = () => {
    onChange({ ...group, conditions: [...(group.conditions ?? []), { type: "condition", field: "", operator: "", value: "", unit: "" }] }, path)
  }
  // 添加子组
  const addGroup = () => {
    onChange({ ...group, conditions: [...(group.conditions ?? []), { type: 'group', logic: 'AND', conditions: [] }] }, path)
  }
  // 切换逻辑
  const setLogic = (logic: "AND" | "OR" | "NOT") => {
    onChange({ ...group, logic }, path)
  }
  return (
    <div className="p-3 border rounded space-y-2 bg-gray-50">
      <div className="flex items-center gap-2 mb-2">
        <label className="text-xs mr-1">逻辑</label>
        <select value={group.logic} onChange={e => setLogic(e.target.value as "AND" | "OR" | "NOT")} className="w-20 h-7 text-xs">
          <option value="AND">AND（与）</option>
          <option value="OR">OR（或）</option>
          <option value="NOT">NOT（非）</option>
        </select>
        {onDelete && (
          <button type="button" className="h-6 w-6 p-0" onClick={onDelete}>×</button>
        )}
      </div>
      <div className="flex flex-col gap-2">
        {(group.conditions ?? []).map((item: any, idx: number) =>
          item.type === "condition" ? (
            <div key={idx} className="flex items-center gap-2">
              <select
                value={item.field}
                onChange={e => {
                  const label = e.target.value
                  const opt = fieldOptions.baseConditions.find((c: any) => c.label === label)
                  if (opt && opt.type === "derived") {
                    updateConditionObject(idx, {
                      type: "condition",
                      field: opt.field,
                      operator: opt.operator || "",
                      value: opt.value || "",
                      unit: opt.unit || ""
                    })
                  } else if (opt && opt.type === "basic") {
                    updateConditionObject(idx, {
                      type: "condition",
                      field: opt.field,
                      operator: "",
                      value: "",
                      unit: ""
                    })
                  }
                }}
                className="w-40 h-7 text-xs"
              >
                <option value="">选择条件字段</option>
                {fieldOptions.baseConditions.map((opt: any) => (
                  <option key={opt.label} value={opt.label}>{opt.label}</option>
                ))}
              </select>
              <select
                value={item.operator}
                onChange={e => updateChild(idx, { ...item, operator: e.target.value })}
                className="w-20 h-7 text-xs"
              >
                <option value="">操作符</option>
                {fieldOptions.operators.map((opt: any) => (
                  <option key={opt} value={opt}>{opt}</option>
                ))}
              </select>
              <input
                className="w-24 h-7 text-xs"
                placeholder="阈值"
                value={item.value}
                onChange={e => updateChild(idx, { ...item, value: e.target.value })}
              />
              <select
                value={item.unit}
                onChange={e => updateChild(idx, { ...item, unit: e.target.value })}
                className="w-20 h-7 text-xs"
              >
                <option value="">单位</option>
                {fieldOptions.units.map((opt: any) => (
                  <option key={opt} value={opt}>{opt}</option>
                ))}
              </select>
              <button type="button" className="h-6 w-6 p-0" onClick={() => removeChild(idx)}>×</button>
            </div>
          ) : (
            <div key={idx} className="ml-4">
              <ConditionGroup
                group={item}
                onChange={(newGroup: any, childPath: any) => {
                  const newConds = (group.conditions ?? []).map((c: any, i: number) =>
                    i === idx ? newGroup : c
                  )
                  onChange({ ...group, conditions: newConds }, path)
                }}
                onDelete={() => removeChild(idx)}
                fieldOptions={fieldOptions}
                path={[...path, idx]}
              />
            </div>
          )
        )}
        <div className="flex gap-2 mt-2">
          <button type="button" className="w-fit text-xs" onClick={addCondition}>+ 添加条件</button>
          <button type="button" className="w-fit text-xs" onClick={addGroup}>+ 添加子组</button>
        </div>
      </div>
    </div>
  )
}

export default function RuleGenerator() {
  const [activeTab, setActiveTab] = useState("template")
  const [selectedTemplate, setSelectedTemplate] = useState<RuleTemplate | null>(null)
  const [customRule, setCustomRule] = useState({
    scenario: "",
    targetUsers: [] as string[],
    baseConditions: [] as BaseCondition[],
    branchConditions: [] as any[],
  })
  const [naturalLanguageInput, setNaturalLanguageInput] = useState("")
  const [generatedDescription, setGeneratedDescription] = useState("")
  const [selectedCodeBlock, setSelectedCodeBlock] = useState<string | null>(null)
  const [objectTypeFilter, setObjectTypeFilter] = useState<string>("all")
  const [professionalCategoryFilter, setProfessionalCategoryFilter] = useState<string>("all")
  const [riskPointFilter, setRiskPointFilter] = useState<string>("all")
  const [semanticNLResult, setSemanticNLResult] = useState("")

  // 新增：URL参数处理 - 判断是否为迭代已有规则
  const [isIteratingExistingRule, setIsIteratingExistingRule] = useState(false)
  const [originalRuleId, setOriginalRuleId] = useState<string | null>(null)
  const [showEvolutionAnalysis, setShowEvolutionAnalysis] = useState(false)

  // 新增：主题演进分析相关状态
  const [themeVersions, setThemeVersions] = useState<any[]>([])
  const [showEvolutionDetail, setShowEvolutionDetail] = useState(false)

  // 处理URL参数
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search)
    const ruleId = urlParams.get("ruleId")
    
    if (ruleId) {
      setIsIteratingExistingRule(true)
      setOriginalRuleId(ruleId)
      
      // 模拟加载原始规则数据
      loadOriginalRuleData(ruleId)
      
      // 模拟加载主题版本历史
      loadThemeVersions(ruleId)
    }
  }, [])

  // 模拟加载原始规则数据
  const loadOriginalRuleData = (ruleId: string) => {
    // 这里应该从API获取原始规则数据
    // 现在使用模拟数据
    const mockOriginalRule = {
      id: ruleId,
      scenario: "电动汽车充电桩电量异常",
      targetUsers: ["营销业务用户"],
      baseConditions: [
        { label: "用户名称包含'充电'", type: "derived" as const, field: "用户名称", operator: "包含", value: "充电", unit: "" },
        { label: "用电地址包含'充电'", type: "derived" as const, field: "用电地址", operator: "包含", value: "充电", unit: "" },
        { label: "电价执行居民合表电价", type: "derived" as const, field: "电价", operator: "等于", value: "居民合表电价", unit: "" },
      ],
      branchConditions: [
        {
          type: 'group',
          logic: 'AND',
          conditions: [
            {
              type: 'condition',
              field: '抄表周期',
              label: '抄表周期为单月',
              operator: '等于',
              value: '单月',
              unit: ''
            },
            {
              type: 'condition',
              field: '月度用电量',
              label: '月度用电量大于5000',
              operator: '大于',
              value: '5000',
              unit: '千瓦时'
            }
          ]
        }
      ]
    }
    
    // 填充到当前规则中
    setCustomRule({
      scenario: mockOriginalRule.scenario,
      targetUsers: [...mockOriginalRule.targetUsers],
      baseConditions: [...mockOriginalRule.baseConditions],
      branchConditions: [...mockOriginalRule.branchConditions],
    })
    setGeneratedDescription(`${mockOriginalRule.scenario}，对${mockOriginalRule.targetUsers.join('、')}，用户名称包含'充电'且用电地址包含'充电'且电价执行居民合表电价的${mockOriginalRule.targetUsers.join('、')}，开展符合以下情况的稽查监控：\n1. 抄表周期为单月且月度用电量大于5000千瓦时`)
  }

  // 模拟加载主题版本历史
  const loadThemeVersions = (ruleId: string) => {
    // 模拟主题版本数据
    const mockVersions = [
      {
        id: "v1.0",
        version: "1.0",
        createdAt: "2024-01-15T10:00:00Z",
        description: "电动汽车充电桩电量异常，对营销业务用户，用户名称包含'充电'且用电地址包含'充电'且电价执行居民合表电价的营销业务用户，开展符合以下情况的稽查监控：\n1. 抄表周期为单月且月度用电量大于3000千瓦时",
        targetUsers: ["营销业务用户"],
        baseConditions: [
          { label: "用户名称包含'充电'", type: "derived" as const, field: "用户名称", operator: "包含", value: "充电", unit: "" },
          { label: "用电地址包含'充电'", type: "derived" as const, field: "用电地址", operator: "包含", value: "充电", unit: "" },
          { label: "电价执行居民合表电价", type: "derived" as const, field: "电价", operator: "等于", value: "居民合表电价", unit: "" },
        ],
        branchConditions: [
          {
            type: 'group',
            logic: 'AND',
            conditions: [
              {
                type: 'condition',
                field: '抄表周期',
                label: '抄表周期为单月',
                operator: '等于',
                value: '单月',
                unit: ''
              },
              {
                type: 'condition',
                field: '月度用电量',
                label: '月度用电量大于3000',
                operator: '大于',
                value: '3000',
                unit: '千瓦时'
              }
            ]
          }
        ],
        hitCount: 45,
        executionCount: 120,
        status: 'published' as const,
        changes: [
          {
            type: 'added' as const,
            field: '月度用电量阈值',
            newValue: '3000千瓦时',
            description: '新增月度用电量阈值限制'
          }
        ]
      },
      {
        id: "v1.1",
        version: "1.1",
        createdAt: "2024-02-20T14:30:00Z",
        description: "电动汽车充电桩电量异常，对营销业务用户，用户名称包含'充电'且用电地址包含'充电'且电价执行居民合表电价的营销业务用户，开展符合以下情况的稽查监控：\n1. 抄表周期为单月且月度用电量大于4000千瓦时\n2. 抄表周期为双月且月度用电量大于8000千瓦时",
        targetUsers: ["营销业务用户"],
        baseConditions: [
          { label: "用户名称包含'充电'", type: "derived" as const, field: "用户名称", operator: "包含", value: "充电", unit: "" },
          { label: "用电地址包含'充电'", type: "derived" as const, field: "用电地址", operator: "包含", value: "充电", unit: "" },
          { label: "电价执行居民合表电价", type: "derived" as const, field: "电价", operator: "等于", value: "居民合表电价", unit: "" },
        ],
        branchConditions: [
          {
            type: 'group',
            logic: 'OR',
            conditions: [
              {
                type: 'group',
                logic: 'AND',
                conditions: [
                  {
                    type: 'condition',
                    field: '抄表周期',
                    label: '抄表周期为单月',
                    operator: '等于',
                    value: '单月',
                    unit: ''
                  },
                  {
                    type: 'condition',
                    field: '月度用电量',
                    label: '月度用电量大于4000',
                    operator: '大于',
                    value: '4000',
                    unit: '千瓦时'
                  }
                ]
              },
              {
                type: 'group',
                logic: 'AND',
                conditions: [
                  {
                    type: 'condition',
                    field: '抄表周期',
                    label: '抄表周期为双月',
                    operator: '等于',
                    value: '双月',
                    unit: ''
                  },
                  {
                    type: 'condition',
                    field: '月度用电量',
                    label: '月度用电量大于8000',
                    operator: '大于',
                    value: '8000',
                    unit: '千瓦时'
                  }
                ]
              }
            ]
          }
        ],
        hitCount: 78,
        executionCount: 150,
        status: 'published' as const,
        changes: [
          {
            type: 'modified' as const,
            field: '单月阈值',
            oldValue: '3000千瓦时',
            newValue: '4000千瓦时',
            description: '调整单月用电量阈值'
          },
          {
            type: 'added' as const,
            field: '双月条件',
            newValue: '双月且大于8000千瓦时',
            description: '新增双月抄表周期条件'
          }
        ]
      },
      {
        id: "v1.2",
        version: "1.2",
        createdAt: "2024-03-10T09:15:00Z",
        description: "电动汽车充电桩电量异常，对营销业务用户，用户名称包含'充电'且用电地址包含'充电'且电价执行居民合表电价的营销业务用户，开展符合以下情况的稽查监控：\n1. 抄表周期为单月且月度用电量大于5000千瓦时\n2. 抄表周期为双月且月度用电量大于10000千瓦时",
        targetUsers: ["营销业务用户"],
        baseConditions: [
          { label: "用户名称包含'充电'", type: "derived" as const, field: "用户名称", operator: "包含", value: "充电", unit: "" },
          { label: "用电地址包含'充电'", type: "derived" as const, field: "用电地址", operator: "包含", value: "充电", unit: "" },
          { label: "电价执行居民合表电价", type: "derived" as const, field: "电价", operator: "等于", value: "居民合表电价", unit: "" },
        ],
        branchConditions: [
          {
            type: 'group',
            logic: 'OR',
            conditions: [
              {
                type: 'group',
                logic: 'AND',
                conditions: [
                  {
                    type: 'condition',
                    field: '抄表周期',
                    label: '抄表周期为单月',
                    operator: '等于',
                    value: '单月',
                    unit: ''
                  },
                  {
                    type: 'condition',
                    field: '月度用电量',
                    label: '月度用电量大于5000',
                    operator: '大于',
                    value: '5000',
                    unit: '千瓦时'
                  }
                ]
              },
              {
                type: 'group',
                logic: 'AND',
                conditions: [
                  {
                    type: 'condition',
                    field: '抄表周期',
                    label: '抄表周期为双月',
                    operator: '等于',
                    value: '双月',
                    unit: ''
                  },
                  {
                    type: 'condition',
                    field: '月度用电量',
                    label: '月度用电量大于10000',
                    operator: '大于',
                    value: '10000',
                    unit: '千瓦时'
                  }
                ]
              }
            ]
          }
        ],
        hitCount: 92,
        executionCount: 180,
        status: 'draft' as const,
        changes: [
          {
            type: 'modified' as const,
            field: '单月阈值',
            oldValue: '4000千瓦时',
            newValue: '5000千瓦时',
            description: '进一步调整单月用电量阈值'
          },
          {
            type: 'modified' as const,
            field: '双月阈值',
            oldValue: '8000千瓦时',
            newValue: '10000千瓦时',
            description: '调整双月用电量阈值'
          }
        ]
      }
    ]
    
    setThemeVersions(mockVersions)
  }

  // 新增：业务意图状态
  const [businessIntents, setBusinessIntents] = useState<Array<{
    id: string
    category: string
    title: string
    description: string
    icon: React.ReactNode
    options: string[]
    selected: boolean
    selectedOptions: string[]
  }>>([
    // {
    //   id: "basic_info",
    //   category: "基本信息",
    //   title: "基本信息",
    //   description: "选择稽查涉及的基本信息类型",
    //   icon: <Database className="h-4 w-4" />,
    //   options: ["用电户", "发电户", "台区", "电能表计"],
    //   selected: false,
    //   selectedOptions: []
    // },
    {
      id: "audit_object",
      category: "稽查对象",
      title: "稽查对象",
      description: "选择具体的稽查对象类型",
      icon: <Users className="h-4 w-4" />,
      options: ["营销业务用户", "工商业用户", "居民用户", "大工业用户", "临时用电用户"],
      selected: false,
      selectedOptions: []
    },
    {
      id: "professional_category",
      category: "专业分类",
      title: "专业分类",
      description: "选择稽查涉及的专业领域",
      icon: <Shield className="h-4 w-4" />,
      options: ["计量采集", "客户服务", "电费计算", "电能质量"],
      selected: false,
      selectedOptions: []
    },
    {
      id: "risk_points",
      category: "风险点",
      title: "风险点",
      description: "选择需要稽查的风险点类型",
      icon: <AlertTriangle className="h-4 w-4" />,
      options: ["充电桩电量异常", "工商业高耗能异常", "功率因数异常", "光伏客户档案异常", "居民电量异常"],
      selected: false,
      selectedOptions: []
    }
  ])

  // Mock generated code outputs
  const [generatedOutputs, setGeneratedOutputs] = useState({
    semanticExpression: `{
  "rule_id": "RULE_001",
  "rule_name": "电动汽车充电桩电量异常",
  "scenario": "电动汽车充电桩电量异常",
  "target_users": ["营销业务用户"],
  "base_conditions": [
    {
      "field": "user_name",
      "operator": "contains",
      "value": "充电"
    },
    {
      "logic": "OR",
      "field": "address",
      "operator": "contains", 
      "value": "充电"
    },
    {
      "logic": "AND",
      "field": "price_type",
      "operator": "equals",
      "value": "居民合表电价"
    }
  ],
  "branch_conditions": [
    {
      "condition": "meter_cycle = '单月'",
      "threshold": 5000,
      "unit": "kWh"
    },
    {
      "condition": "meter_cycle = '双月'", 
      "threshold": 10000,
      "unit": "kWh"
    }
  ]
}`,
    semanticSQL: `SELECT 
  customer_id,
  customer_name,
  user_address,
  meter_cycle,
  monthly_consumption,
  price_type
FROM semantic_layer.customer_power_consumption 
WHERE (user_name LIKE '%充电%' OR address LIKE '%充电%')
  AND price_type = '居民合表电价'
  AND (
    (meter_cycle = '单月' AND monthly_consumption > 5000)
    OR (meter_cycle = '双月' AND monthly_consumption > 10000)
  )
  AND audit_period = CURRENT_MONTH()`,
    executableSQL: `SELECT 
  c.customer_id,
  c.customer_name,
  c.address as user_address,
  m.meter_cycle,
  SUM(mr.consumption_value) as monthly_consumption,
  p.price_type
FROM customers c
JOIN meters m ON c.customer_id = m.customer_id
JOIN meter_readings mr ON m.meter_id = mr.meter_id
JOIN price_policies p ON c.price_policy_id = p.policy_id
WHERE mr.reading_date >= DATE_TRUNC('month', CURRENT_DATE)
  AND mr.reading_date < DATE_TRUNC('month', CURRENT_DATE) + INTERVAL '1 month'
  AND (c.customer_name LIKE '%充电%' OR c.address LIKE '%充电%')
  AND p.price_type = '居民合表电价'
GROUP BY c.customer_id, c.customer_name, c.address, m.meter_cycle, p.price_type
HAVING (
  (m.meter_cycle = '单月' AND SUM(mr.consumption_value) > 5000)
  OR (m.meter_cycle = '双月' AND SUM(mr.consumption_value) > 10000)
)`,
  })

  // 步骤管理
  const [step, setStep] = useState(0)
  const [semanticResult, setSemanticResult] = useState("")
  const [sqlResult, setSqlResult] = useState("")

  // 新增：目标结果字段和原子
  const [resultFields, setResultFields] = useState<{ name: string }[]>([])
  // 语义逻辑基础原子（实际应从逻辑管理获取，这里mock）
  const semanticAtoms = [
    { name: "客户编号", desc: "客户唯一编号" },
    { name: "客户名称", desc: "客户名称" },
    { name: "客户地址", desc: "客户地址" },
    { name: "上月度数", desc: "上月用电度数" },
    { name: "电价类型", desc: "电价类型" },
    { name: "计量点编号", desc: "计量点编号" },
    { name: "容量", desc: "计量点容量" },
    { name: "供电单位", desc: "供电单位" },
    { name: "用户分类", desc: "用户分类" }
  ]

  // 步骤切换
  const nextStep = () => setStep((s) => Math.min(s + 1, 3))
  const prevStep = () => setStep((s) => Math.max(s - 1, 0))

  // 语义化分析（Mock）
  const handleSemanticAnalyze = () => {
    setSemanticResult(`{
  "rule_id": "RULE_001",
  "rule_name": "${generatedDescription.slice(0, 10)}...",
  "description": "${generatedDescription}",
  "base_conditions": ${JSON.stringify(customRule.baseConditions.map(c => c.label))}
}`)
    nextStep()
  }
  // SQL生成（Mock）
  const handleSQLGenerate = () => {
    setSqlResult(`SELECT * FROM table WHERE ... -- ${generatedDescription.slice(0, 10)}...`)
    nextStep()
  }

  // 过滤模板
  const filteredTemplates = ruleTemplates.filter(t => {
    return (
      (objectTypeFilter === "all" || t.objectTypes.includes(objectTypeFilter)) &&
      (professionalCategoryFilter === "all" || t.professionalCategories.includes(professionalCategoryFilter)) &&
      (riskPointFilter === "all" || t.riskPoints.includes(riskPointFilter))
    )
  })

  // 选择模板
  const handleTemplateSelect = (template: RuleTemplate) => {
    setSelectedTemplate(template)
    // baseConditions兼容string[]和BaseCondition[]
    const baseConds: BaseCondition[] = (template.structure.baseConditions as any[]).map((c: any) => {
      if (typeof c === 'string') {
        // 通过label查找
        return fieldOptions.baseConditions.find(opt => opt.label === c) as BaseCondition
      }
      return c as BaseCondition
    })
    setCustomRule({
      scenario: template.structure.scenario,
      targetUsers: [...template.structure.targetUsers],
      baseConditions: baseConds,
      branchConditions: [...template.structure.branchConditions],
    })
    updateGeneratedDescription(template.structure)
  }

  // 更新自定义规则
  const updateCustomRule = (field: string, value: any) => {
    const newRule = { ...customRule, [field]: value }
    setCustomRule(newRule)
    updateGeneratedDescription(newRule)
  }

  // 添加基础条件
  const addBaseCondition = (cond: BaseCondition) => {
    if (!customRule.baseConditions.some((c: BaseCondition) => c.label === cond.label)) {
      updateCustomRule("baseConditions", [...customRule.baseConditions, cond])
    }
  }

  // 移除基础条件
  const removeBaseCondition = (cond: BaseCondition) => {
    updateCustomRule(
      "baseConditions",
      customRule.baseConditions.filter((c: BaseCondition) => c.label !== cond.label)
    )
  }

  // 添加分支条件
  const addBranchCondition = () => {
    const newBranch = {
      condition: "",
      threshold: "",
      unit: "千瓦时",
    }
    const newBranches = [...customRule.branchConditions, newBranch]
    updateCustomRule("branchConditions", newBranches)
  }

  // 更新分支条件
  const updateBranchCondition = (index: number, field: string, value: string) => {
    const newBranches = [...customRule.branchConditions]
    newBranches[index] = { ...newBranches[index], [field]: value || "" }
    updateCustomRule("branchConditions", newBranches)
  }

  // 移除分支条件
  const removeBranchCondition = (index: number) => {
    const newBranches = customRule.branchConditions.filter((_, i) => i !== index)
    updateCustomRule("branchConditions", newBranches)
  }

  // 生成规则描述
  const updateGeneratedDescription = (ruleStructure: any) => {
    let description = `${ruleStructure.scenario}，每月筛查${ruleStructure.targetUsers.join("、")}，`

    if (ruleStructure.baseConditions.length > 0) {
      description += `对符合以下基础条件的用户：\n`
      ruleStructure.baseConditions.forEach((condition: string, index: number) => {
        if (index > 0) description += index === ruleStructure.baseConditions.length - 1 ? "，且" : "，或"
        description += condition
      })
      description += `，\n`
    }

    if (ruleStructure.branchConditions.length > 0) {
      description += `对符合以下情况的开展稽查监控：\n`
      ruleStructure.branchConditions.forEach((branch: any) => {
        description += `${branch.condition}月度发行电量（抄见电量）${branch.threshold}${branch.unit}以上；\n`
      })
    }

    setGeneratedDescription(description)
  }

  const handleGenerateRule = () => {
    if (naturalLanguageInput.trim()) {
      setGeneratedDescription(naturalLanguageInput)
    }
  }

  // 历史规则mock
  const historyRules = [
    {
      id: 'rule_001',
      scenario: '电动汽车充电桩电量异常',
      targetUsers: ['营销业务用户'],
      baseConditions: ["用户名称包含'充电'字段", "用电地址包含'充电'字段", "电价执行居民合表电价"],
      branchConditions: [
        { condition: "抄表周期为'单月'", threshold: "5000", unit: "千瓦时" },
        { condition: "抄表周期为'双月'", threshold: "10000", unit: "千瓦时" }
      ]
    },
    {
      id: 'rule_002',
      scenario: '工商业高耗能异常',
      targetUsers: ['工商业用户'],
      baseConditions: ["客户类型为'工商业'", "电压等级为'10kV'或'35kV'"],
      branchConditions: [
        { condition: "行业类型为'制造业'", threshold: "50000", unit: "千瓦时" },
        { condition: "行业类型为'服务业'", threshold: "20000", unit: "千瓦时" }
      ]
    }
  ]

  // 冲突检测逻辑（简单对比场景、对象、条件、阈值等）
  function detectRuleConflict(currentRule: any) {
    const conflicts: any[] = []
    historyRules.forEach(hr => {
      // 场景完全相同
      if (hr.scenario === currentRule.scenario) {
        conflicts.push({
          type: '场景重复',
          message: `与历史规则【${hr.scenario}】场景完全重复。`,
          rule: hr
        })
      }
      // 对象和条件高度重叠
      if (
        hr.targetUsers.some((u: string) => currentRule.targetUsers.includes(u)) &&
        hr.baseConditions.some((c: string) => currentRule.baseConditions.map((bc: any) => bc.label).includes(c))
      ) {
        conflicts.push({
          type: '对象与条件重叠',
          message: `与历史规则【${hr.scenario}】的对象和部分条件高度重叠。`,
          rule: hr
        })
      }
      // 阈值冲突（如同一条件阈值更宽松/更严格）
      hr.branchConditions.forEach((bc: any) => {
        currentRule.branchConditions.forEach((cbc: any) => {
          if (bc.condition === cbc.condition && bc.threshold === cbc.threshold && bc.unit === cbc.unit) {
            conflicts.push({
              type: '阈值完全一致',
              message: `与历史规则【${hr.scenario}】的分支条件完全一致。`,
              rule: hr
            })
          }
        })
      })
    })
    return conflicts
  }

  // 冲突检测结果状态
  const [conflictResult, setConflictResult] = useState<any[]>([])
  const handleAIDetectConflict = () => {
    const result = detectRuleConflict(customRule)
    setConflictResult(result)
  }

  return (
    <div className="flex-1 p-6 space-y-6">
      {/* 步骤条 */}
      <div className="flex items-center gap-4 mb-4">
        <div
          className={`flex-1 flex flex-col items-center ${
            step === 0 ? 'text-blue-600 font-bold' : 'text-gray-400'
          }`}
        >
          0. 主题设计要素引导
        </div>
        <div className="w-8 h-0.5 bg-gray-300" />
        <div
          className={`flex-1 flex flex-col items-center ${
            step === 1 ? 'text-blue-600 font-bold' : 'text-gray-400'
          }`}
        >
          1. 主题定义结构化编排
        </div>
        <div className="w-8 h-0.5 bg-gray-300" />
        <div
          className={`flex-1 flex flex-col items-center ${
            step === 2 ? 'text-blue-600 font-bold' : 'text-gray-400'
          }`}
        >
          2. 语义化分析
        </div>
        <div className="w-8 h-0.5 bg-gray-300" />
        <div
          className={`flex-1 flex flex-col items-center ${
            step === 3 ? 'text-blue-600 font-bold' : 'text-gray-400'
          }`}
        >
          3. 脚本生成与确认
        </div>
      </div>
      {step === 0 && (
        <Step0IntentGuide
          businessIntents={businessIntents}
          setBusinessIntents={setBusinessIntents}
          naturalLanguageInput={naturalLanguageInput}
          setNaturalLanguageInput={setNaturalLanguageInput}
          nextStep={nextStep}
        />
      )}
      {step === 1 && (
        <Step1TemplateSelect
          selectedTemplate={selectedTemplate}
          setSelectedTemplate={setSelectedTemplate}
          customRule={customRule}
          setCustomRule={setCustomRule}
          generatedDescription={generatedDescription}
          setGeneratedDescription={setGeneratedDescription}
          nextStep={handleSemanticAnalyze}
          aiDetectConflict={handleAIDetectConflict}
          conflictResult={conflictResult}
        />
      )}
      {step === 2 && (
        <Step2SemanticAnalyze
          generatedDescription={generatedDescription}
          setGeneratedDescription={setGeneratedDescription}
          semanticResult={semanticResult}
          setSemanticResult={setSemanticResult}
          prevStep={prevStep}
          nextStep={nextStep}
          setSemanticNLResult={setSemanticNLResult}
          resultFields={resultFields}
          setResultFields={setResultFields}
          semanticAtoms={semanticAtoms}
        />
      )}
      {step === 3 && (
        <Step3SQLConfirm
          generatedDescription={generatedDescription}
          semanticResult={semanticResult}
          sqlResult={sqlResult}
          setSqlResult={setSqlResult}
          prevStep={prevStep}
          semanticNLResult={semanticNLResult}
          resultFields={resultFields}
          isIteratingExistingRule={isIteratingExistingRule}
          onShowEvolutionAnalysis={() => setShowEvolutionAnalysis(true)}
        />
      )}

      {/* 主题演进趋势分析弹窗 */}
      {showEvolutionAnalysis && (
        <ThemeEvolutionAnalysis
          themeId={originalRuleId || ""}
          themeName={customRule.scenario || "未知主题"}
          versions={themeVersions}
          onClose={() => setShowEvolutionAnalysis(false)}
          onShowDetail={() => {
            setShowEvolutionAnalysis(false)
            setShowEvolutionDetail(true)
          }}
        />
      )}

      {/* 主题演进明细弹窗 */}
      {showEvolutionDetail && (
        <ThemeEvolutionDetail
          themeId={originalRuleId || ""}
          themeName={customRule.scenario || "未知主题"}
          versions={themeVersions}
          onClose={() => setShowEvolutionDetail(false)}
        />
      )}

      {/* Code Explanation Modal */}
      <Dialog
        open={!!selectedCodeBlock}
        onOpenChange={() => setSelectedCodeBlock(null)}
      >
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Code className="h-5 w-5" />
              代码格式化与解释
            </DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <h4 className="font-medium mb-2">格式化代码</h4>
              <pre className="text-sm bg-gray-50 p-4 rounded border overflow-x-auto">
                {selectedCodeBlock === 'semantic' &&
                  generatedOutputs.semanticExpression}
                {selectedCodeBlock === 'logical' &&
                  generatedOutputs.semanticSQL}
                {selectedCodeBlock === 'physical' &&
                  generatedOutputs.executableSQL}
              </pre>
            </div>
            <Separator />
            <div>
              <h4 className="font-medium mb-2">详细解释</h4>
              <div className="text-sm text-gray-700 space-y-2">
                {selectedCodeBlock === 'semantic' && (
                  <div>
                    <p>
                      <strong>语义表达式说明：</strong>
                    </p>
                    <p>• rule_id: 规则的唯一标识符</p>
                    <p>• scenario: 业务场景描述</p>
                    <p>• target_users: 筛查对象</p>
                    <p>• base_conditions: 基础筛选条件</p>
                    <p>• branch_conditions: 分支判断条件</p>
                  </div>
                )}
                {selectedCodeBlock === 'logical' && (
                  <div>
                    <p>
                      <strong>语义层SQL说明：</strong>
                    </p>
                    <p>• 使用语义层的抽象表名和字段名</p>
                    <p>• 支持复杂的OR和AND逻辑组合</p>
                    <p>• 包含分支条件的判断逻辑</p>
                    <p>• 提供业务友好的查询接口</p>
                  </div>
                )}
                {selectedCodeBlock === 'physical' && (
                  <div>
                    <p>
                      <strong>物理SQL说明：</strong>
                    </p>
                    <p>• 直接操作底层数据库表</p>
                    <p>• 包含具体的表连接和聚合逻辑</p>
                    <p>• 实现复杂的分支条件判断</p>
                    <p>• 可直接在数据库中执行</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}
